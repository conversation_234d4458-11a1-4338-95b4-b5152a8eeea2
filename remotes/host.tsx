import '@/globals.css';
import { PropsWithChildren } from 'react';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { ThemeProvider } from '@/components/theme/theme-provider';
import ApolloClientProvider from '@/common/graphql/ApolloProvider';
import { cn } from '@/lib/utils';

export default function RemoteComponentHost({ children }: PropsWithChildren) {
	return (
		// TODO: should be externalized & shared across MFs
		<ApolloClientProvider>
			<ThemeProvider>
				<SidebarProvider
					className={cn('__ABRAXA_PROTOTYPE__', 'antialiased')}
					style={{
						height: '100%',
						width: '100%',
						minHeight: 'unset'
					}}
				>
					<SidebarInset
						style={{
							marginTop: '0',
							marginRight: '0',
							marginBottom: '0',
							marginLeft: '0',
							borderRadius: '6px',
							minHeight: 'unset'
						}}
					>
						{children}
					</SidebarInset>
				</SidebarProvider>
			</ThemeProvider>
		</ApolloClientProvider>
	);
}
