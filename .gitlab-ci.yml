image: node:22-alpine

stages:
    - validation
    - build
    - deploy

.npm-preparation:
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - .npm/
    before_script:
        - node -v
        - npm -v
        - npm config set //gitlab.abraxa.com:_authToken="${CI_JOB_TOKEN}"
        - npm ci --cache .npm --prefer-offline

lint:
    extends: .npm-preparation
    stage: validation
    interruptible: true
    script:
        - npm run lint

typecheck:
    extends: .npm-preparation
    stage: validation
    interruptible: true
    script:
        - npm run typecheck

build:
    extends: .npm-preparation
    stage: build
    interruptible: true
    script:
        - npm run build
    artifacts:
        expire_in: 24 hrs
        paths:
            - build/public
    variables:
      PUBLIC_PATH: https://static.abraxa.com/
      BASE_PATH: /prototype

deploy:
  needs:
    - build
  image: google/cloud-sdk:latest
  stage: deploy
  variables:
    PROJECT_ID: abraxa-prod
    BUCKET_PATH: static.abraxa.com
    GOOGLE_APPLICATION_CREDENTIALS: ${GOOGLE_APPLICATION_CREDENTIALS_PROD}
    PACKAGE_NAME: prototype
    ENV_URL: dev.abraxa.com
  before_script:
    - gcloud auth activate-service-account --key-file ${GOOGLE_APPLICATION_CREDENTIALS}
    - gcloud config set project ${PROJECT_ID}
  script:
    - export VERSION=${CI_COMMIT_TAG:-$CI_COMMIT_SHORT_SHA}
    - gcloud storage rsync --cache-control="public, max-age=86400, stale-while-revalidate=7776000" -r build/public/static/ gs://${BUCKET_PATH}/static
    - gcloud storage rsync --cache-control="public, max-age=60, stale-while-revalidate=300" build/public/ gs://${BUCKET_PATH}/${ENV_URL}/${PACKAGE_NAME}/${VERSION}
    - gcloud storage rsync --cache-control="public, max-age=60, stale-while-revalidate=300" build/public/ gs://${BUCKET_PATH}/${ENV_URL}/${PACKAGE_NAME}/latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
