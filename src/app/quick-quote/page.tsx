import { lazy } from 'react';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { QuoteForm } from '@/app/quick-quote/components/quote-form';
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const QuoteAmount = lazy(() => delay(100).then(() => import('./components/quote-amount')));
const QuoteEstimate = lazy(() => delay(100).then(() => import('./components/quote-estimate')));

export default function Page() {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbPage>Quick Quote</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4 pt-0">
				<div className="flex w-full max-w-3xl flex-col gap-6">
					<Card className="flex-1 gap-6">
						<CardHeader>
							<CardTitle>Estimated quote</CardTitle>
							<CardDescription>The calculated price is based on official tariffs.</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="flex flex-col gap-6 lg:flex-row">
								<QuoteForm />
								<QuoteAmount />
							</div>
						</CardContent>
					</Card>
					<QuoteEstimate />
				</div>
			</div>
		</>
	);
}
