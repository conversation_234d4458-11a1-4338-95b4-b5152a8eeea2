import { Separator } from '@/components/ui/separator';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion-quick';

export default function QuoteEstimate() {
	return (
		<div className="flex flex-col gap-4">
			<div className="flex flex-col px-2">
				<h2 className="text-xl font-semibold">Rotterdam, NL</h2>
				<p className="font text-muted-foreground text-sm">
					<i>Agency fees are not included in below calculations</i>
				</p>
			</div>
			<Separator />
			<Accordion type="single" collapsible className="w-full">
				<AccordionItem value="item-1">
					<AccordionTrigger>A.&nbsp;&nbsp;Pilotage</AccordionTrigger>
					<AccordionContent>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">1. Inwards Pilotage</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">2. Outwards Pilotage</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">3. Pilot boat fee</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>500.00
							</div>
						</div>
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="item-2">
					<AccordionTrigger>B.&nbsp;&nbsp;Towage</AccordionTrigger>
					<AccordionContent>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">1. Inwards Towage</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">2. Outwards Towage</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="item-3">
					<AccordionTrigger>C.&nbsp;&nbsp;Mooring services</AccordionTrigger>
					<AccordionContent>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">1. Inwards Mooring</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">2. Outwards Mooring</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="item-4">
					<AccordionTrigger>D.&nbsp;&nbsp;Port dues</AccordionTrigger>
					<AccordionContent>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">1. Inwards Mooring</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">2. Outwards Mooring</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="item-5">
					<AccordionTrigger>E.&nbsp;&nbsp;Waste disposal</AccordionTrigger>
					<AccordionContent>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">1. Inwards Mooring</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
						<div className="flex h-10 items-center justify-between">
							<div className="pl-12">2. Outwards Mooring</div>
							<div>
								<span className="text-muted-foreground text-xs">USD </span>3,250.00
							</div>
						</div>
					</AccordionContent>
				</AccordionItem>
			</Accordion>
		</div>
	);
}
