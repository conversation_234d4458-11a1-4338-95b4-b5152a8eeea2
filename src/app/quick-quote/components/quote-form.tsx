import * as React from 'react';
import { Ship, Package, Anchor, ArrowRightLeft, Warehouse } from 'lucide-react';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const vessels: ComboboxOption[] = [
	{ value: '1', label: 'mv Meridiaan Express' },
	{ value: '2', label: 'mv Meridiaan Cinco' },
	{ value: '3', label: 'mv Vertom Meridiaan' },
	{ value: '4', label: 'mv Gulf Meridiaan' },
	{ value: '5', label: 'mv Astra Meridiaan' }
];

const ports: ComboboxOption[] = [
	{ value: '1', label: 'Rotterdam, NL' },
	{ value: '2', label: 'Amsterdam, NL' },
	{ value: '3', label: 'Varna, BG' },
	{ value: '4', label: 'Barcelona, BG' },
	{ value: '5', label: 'Singapore, SG' }
];

const portFunction: ComboboxOption[] = [
	{ value: '1', label: 'Loading' },
	{ value: '2', label: 'Discharging' },
	{ value: '3', label: 'Canal transit' }
];

const terminals: ComboboxOption[] = [
	{ value: '1', label: 'Steinweg Handelsveem Europoort Terminal (45-8)' },
	{ value: '2', label: 'BP Raffinaderij Rotterdam B.V. (Europoort) (97-1)' },
	{ value: '3', label: 'LBC Rotterdam B.V. (11-1)' },
	{ value: '4', label: 'Cargill B.V. (19-1)' },
	{ value: '5', label: 'Barge Center Waalhaven B.V. (55-1)' }
];

const cargoType: ComboboxOption[] = [
	{ value: '1', label: 'Other liquid bulk' },
	{ value: '2', label: 'Crude oil' },
	{ value: '3', label: 'Naphtha' },
	{ value: '4', label: 'LPG' },
	{ value: '5', label: 'Petroleum products' }
];

export function QuoteForm() {
	const [, setSelectedVessel] = React.useState('');
	const [, setSelectedPort] = React.useState('');
	const [, setSelectedPortFunction] = React.useState('');
	const [, setSelectedTerminal] = React.useState('');
	const [, setSelectedCargoType] = React.useState('');

	return (
		<form className="grid flex-1 grid-cols-2 gap-3">
			<div className="col-span-2 flex">
				<Combobox
					data={vessels}
					placeholder="Vessel"
					icon={Ship}
					onSelect={value => setSelectedVessel(value)}
				/>
			</div>
			<div className="">
				<Combobox data={ports} placeholder="Port" icon={Anchor} onSelect={value => setSelectedPort(value)} />
			</div>
			<div className="">
				<Combobox
					data={portFunction}
					placeholder="Port function"
					icon={ArrowRightLeft}
					onSelect={value => setSelectedPortFunction(value)}
				/>
			</div>
			<div className="col-span-2 flex">
				<Combobox
					data={terminals}
					placeholder="Terminal"
					icon={Warehouse}
					onSelect={value => setSelectedTerminal(value)}
				/>
			</div>
			<div className="">
				<Combobox
					data={cargoType}
					placeholder="Cargo"
					icon={Package}
					onSelect={value => setSelectedCargoType(value)}
				/>
			</div>
			<div className="">
				<Input placeholder="0.000.000" className="bg-background" />
			</div>
			<div className="flex items-center gap-2">
				<Label className="text-muted-foreground flex-1 text-nowrap">Port stay</Label>
				<Input placeholder="0 days" className="bg-background flex-1" />
			</div>
		</form>
	);
}
