import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Accordion, AccordionItem } from '@/components/ui/accordion-quick';

export function QuoteEstimateSkeleton() {
	return (
		<div className="flex flex-col gap-4">
			<div className="flex h-12 flex-col gap-4 px-2">
				<Skeleton className="h-3 w-40" />
				<Skeleton className="h-3 w-96" />
			</div>
			<Separator />
			<Accordion type="single" collapsible className="w-full">
				<AccordionItem className="flex h-12 items-center justify-between" value="item-1">
					<Skeleton className="h-3 w-96" />
					<Skeleton className="h-3 w-24" />
				</AccordionItem>
				<AccordionItem className="flex h-12 items-center justify-between" value="item-2">
					<Skeleton className="h-3 w-96" />
					<Skeleton className="h-3 w-24" />
				</AccordionItem>
				<AccordionItem className="flex h-12 items-center justify-between" value="item-3">
					<Skeleton className="h-3 w-96" />
					<Skeleton className="h-3 w-24" />
				</AccordionItem>
				<AccordionItem className="flex h-12 items-center justify-between" value="item-4">
					<Skeleton className="h-3 w-96" />
					<Skeleton className="h-3 w-24" />
				</AccordionItem>
				<AccordionItem className="flex h-12 items-center justify-between" value="item-5">
					<Skeleton className="h-3 w-96" />
					<Skeleton className="h-3 w-24" />
				</AccordionItem>
				<AccordionItem className="flex h-12 items-center justify-between" value="item-6">
					<Skeleton className="h-3 w-96" />
					<Skeleton className="h-3 w-24" />
				</AccordionItem>
			</Accordion>
		</div>
	);
}
