import '../globals.css';
import { Outlet } from 'react-router';
import { GraphqlApiGatewayProvider } from '@abraxa/graphql';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '@/components/theme/theme-provider';
import ApolloClientProvider from '@/common/graphql/ApolloProvider';
import { Loader } from '@/components/ui/loader';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { Authentication } from '@/authentication';
import { SidebarSwitcher } from '@/components/sidebar/sidebar-switcher';
import { CommandSearchDialog } from '@/components/command/command-search-dialog';
import { Toaster } from '@/components/ui/sonner';

function LoadingContainer() {
	return (
		<div className="flex h-screen w-screen flex-col items-center justify-center bg-white dark:bg-gray-950">
			<Loader />
		</div>
	);
}

const queryClient = new QueryClient();

export default function RootLayout() {
	return (
		<ApolloClientProvider>
			<GraphqlApiGatewayProvider basePath={process.env.NEXT_PUBLIC_GRAPHQL_ADDRESS}>
				<QueryClientProvider client={queryClient}>
					<ThemeProvider>
						<Authentication loadingElement={<LoadingContainer />}>
							<div vaul-drawer-wrapper="">
								<SidebarProvider>
									<SidebarSwitcher />
									<SidebarInset>
										<Outlet />
									</SidebarInset>
									<CommandSearchDialog />
								</SidebarProvider>
								<Toaster />
							</div>
						</Authentication>
					</ThemeProvider>
				</QueryClientProvider>
			</GraphqlApiGatewayProvider>
		</ApolloClientProvider>
	);
}
