import { useState } from 'react';
import { CircleDotDashed, Crown, Hash, Ship } from 'lucide-react';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

const vessels = [
	{ value: 'meridiaan-express', label: 'mv Meridiaan Express' },
	{ value: 'meridiaan-cinco', label: 'mv Meridiaan Cinco' },
	{ value: 'vertom-meridiaan', label: 'mv Vertom Meridiaan' },
	{ value: 'gulf-meridiaan', label: 'mv Gulf Meridiaan' },
	{ value: 'astra-meridiaan', label: 'mv Astra Meridiaan' }
];

const statuses = [
	{ value: 'at-port', label: 'At port' },
	{ value: 'at-sea', label: 'At sea' }
];

export default function VesselGeneralForm({
	vesselType,
	setVesselType
}: {
	vesselType: string;
	setVesselType: (value: string) => void;
}) {
	const [, setSelectedVessel] = useState<string | null>(null);
	const [, setSelectedStatus] = useState<string | null>(null);

	return (
		<div className="flex flex-col gap-6 lg:flex-row">
			<div className="w-full flex-1">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-2 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<Ship className="size-4" />
						Vessel
					</div>
					<div>
						<Combobox
							data={vessels}
							placeholder="Choose vessel"
							className="h-8 font-normal"
							onSelect={value => setSelectedVessel(value)}
						/>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Hash className="size-4" />
						Vessel ID
					</div>
					<div>
						<Input className="h-8" placeholder="---" />
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<CircleDotDashed className="size-4" />
						Vessel Status
					</div>
					<div>
						<Combobox
							data={statuses}
							placeholder="Choose status"
							className="h-8 font-normal"
							onSelect={value => setSelectedStatus(value)}
						/>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Crown className="size-4" />
						Ownership
					</div>
					<div>
						<RadioGroup
							defaultValue="owned"
							value={vesselType}
							className="flex h-8 items-center space-x-2"
							onValueChange={value => setVesselType(value)}
						>
							<div className="flex items-center space-x-2">
								<RadioGroupItem value="owned" id="r1" />
								<Label htmlFor="r1">Owned vessel</Label>
							</div>
							<div className="flex items-center space-x-2">
								<RadioGroupItem value="chartered" id="r2" />
								<Label htmlFor="r2">Time chartered</Label>
							</div>
						</RadioGroup>
					</div>
				</div>
			</div>
			<div className="w-full flex-1">
				<div className="bg-card text-card-foreground rounded-xl border p-4 shadow-sm">
					<div className="text-muted-foreground grid grid-cols-[60px_minmax(0px,_1fr)_60px_minmax(0px,_1fr)] gap-x-2 gap-y-2 text-sm">
						<div>IMO</div>
						<div>---</div>
						<div>Owner</div>
						<div>---</div>
						<div>DWT</div>
						<div>---</div>
						<div>Capacity</div>
						<div>---</div>
					</div>
				</div>
			</div>
		</div>
	);
}
