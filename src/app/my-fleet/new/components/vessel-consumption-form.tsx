import { useState } from 'react';
import { Plus } from 'lucide-react';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';

const conditions = [
	{ value: 'laden', label: 'Laden' },
	{ value: 'ballast', label: 'Ballast' },
	{ value: 'idle', label: 'In Port Idle' },
	{ value: 'working', label: 'In Port Working' }
];

const warranties = [
	{ value: '', label: '' },
	{ value: 'cp', label: 'CP' }
];

const bunkers = [
	{ value: 'ifo', label: 'IFO' },
	{ value: 'vlsfo', label: 'VLSFO' },
	{ value: 'mgo', label: 'MGO' }
];

const consumption = [
	{
		id: 1,
		condition: 'laden',
		warranty: 'cp',
		speed: 13,
		consumption1: 16.5,
		grade1: 'vlsfo',
		consumption2: 0.1,
		grade2: 'mgo'
	},
	{
		id: 2,
		condition: 'laden',
		warranty: 'cp',
		speed: 13,
		consumption1: 20,
		grade1: 'vlsfo',
		consumption2: 0.1,
		grade2: 'mgo'
	},
	{
		id: 3,
		condition: 'laden',
		warranty: 'cp',
		speed: 14,
		consumption1: 24,
		grade1: 'vlsfo',
		consumption2: 0.1,
		grade2: 'mgo'
	},
	{
		id: 4,
		condition: 'ballast',
		warranty: 'cp',
		speed: 12,
		consumption1: 14.5,
		grade1: 'vlsfo',
		consumption2: 0.1,
		grade2: 'mgo'
	},
	{
		id: 5,
		condition: 'ballast',
		warranty: 'cp',
		speed: 13,
		consumption1: 18,
		grade1: 'vlsfo',
		consumption2: 0.1,
		grade2: 'mgo'
	},
	{
		id: 6,
		condition: 'ballast',
		warranty: 'cp',
		speed: 14,
		consumption1: 20.5,
		grade1: 'vlsfo',
		consumption2: 0.1,
		grade2: 'mgo'
	},
	{
		id: 7,
		condition: 'idle',
		warranty: '',
		speed: 0,
		consumption1: 0,
		grade1: 'vlsfo',
		consumption2: 0,
		grade2: 'mgo'
	},
	{
		id: 8,
		condition: 'working',
		warranty: '',
		speed: 0,
		consumption1: 0,
		grade1: 'vlsfo',
		consumption2: 0,
		grade2: 'mgo'
	}
];

export default function VesselConsumptionForm() {
	const [, setSelectedCondition] = useState<string | null>(null);
	const [, setSelectedWarranty] = useState<string | null>(null);
	const [, setSelectedBunker] = useState<string | null>(null);
	return (
		<>
			<h3 className="text-base font-semibold">Speed and Consumption</h3>
			<Table className="text-sm">
				<TableHeader>
					<TableRow>
						<TableHead>Condition</TableHead>
						<TableHead>Warranted</TableHead>
						<TableHead className="w-[70px]">Speed</TableHead>
						<TableHead className="w-[120px]">Consumption-1</TableHead>
						<TableHead>Grade-1</TableHead>
						<TableHead className="w-[120px]">Consumption-2</TableHead>
						<TableHead>Grade-2</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{consumption.map(consumption => (
						<TableRow key={consumption.id}>
							<TableCell>
								<Combobox
									data={conditions}
									initialValue={consumption.condition}
									placeholder="---"
									className="h-8 font-normal"
									onSelect={value => setSelectedCondition(value)}
								/>
							</TableCell>
							<TableCell>
								<Combobox
									data={warranties}
									initialValue={consumption.warranty}
									placeholder="---"
									className="h-8 font-normal"
									onSelect={value => setSelectedWarranty(value)}
								/>
							</TableCell>
							<TableCell>
								<Input className="bg-background h-8" placeholder="00.00" value={consumption.speed} />
							</TableCell>
							<TableCell>
								<Input
									className="bg-background h-8"
									placeholder="00.00"
									value={consumption.consumption1}
								/>
							</TableCell>
							<TableCell>
								<Combobox
									data={bunkers}
									initialValue={consumption.grade1}
									placeholder="---"
									className="h-8 font-normal"
									onSelect={value => setSelectedBunker(value)}
								/>
							</TableCell>
							<TableCell>
								<Input
									className="bg-background h-8"
									placeholder="00.00"
									value={consumption.consumption2}
								/>
							</TableCell>
							<TableCell>
								<Combobox
									data={bunkers}
									initialValue={consumption.grade2}
									placeholder="---"
									className="h-8 font-normal"
									onSelect={value => setSelectedBunker(value)}
								/>
							</TableCell>
						</TableRow>
					))}
					<TableRow></TableRow>
				</TableBody>
				<TableFooter className="bg-transparent">
					<TableRow className="hover:bg-transparent">
						<TableCell colSpan={7}>
							<Button variant="outline" size="sm" className="w-fit">
								<Plus className="h-4 w-4" />
								Add Condition
							</Button>
						</TableCell>
					</TableRow>
				</TableFooter>
			</Table>
		</>
	);
}
