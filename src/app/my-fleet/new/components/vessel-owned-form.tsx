import { useState } from 'react';
import { DollarSign, Droplets } from 'lucide-react';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';

const currencies = [
	{ value: 'usd', label: 'USD' },
	{ value: 'eur', label: 'EUR' },
	{ value: 'gbp', label: 'GBP' }
];

export default function VesselOwnedForm() {
	const [, setSelectedCurrency] = useState<string | null>(null);
	return (
		<>
			<h3 className="text-base font-semibold">Owned vessel</h3>
			<div className="flex flex-col gap-6 lg:flex-row">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-2 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<DollarSign className="size-4" />
						Daily OPEX
					</div>
					<div className="flex items-center gap-2">
						<Input className="h-8 min-w-40" placeholder="00.00" />
						<Combobox
							data={currencies}
							placeholder="USD"
							className="h-8 font-normal"
							onSelect={value => setSelectedCurrency(value)}
						/>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Droplets className="size-4" />
						Bunker Qty
					</div>
					<div>
						<Input className="h-8" placeholder="000.000" suffix="mts" />
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Droplets className="size-4" />
						Bunker Price
					</div>
					<div className="flex items-center gap-2">
						<Input className="h-8 min-w-40" placeholder="00.00" />
						<Combobox
							data={currencies}
							placeholder="USD"
							className="h-8 font-normal"
							onSelect={value => setSelectedCurrency(value)}
						/>
					</div>
				</div>
				<div className="w-full flex-1"></div>
			</div>
		</>
	);
}
