import { useState } from 'react';
import { Anchor, BriefcaseBusiness, Calendar, File, Hash, Timer, DollarSign, Text, Percent } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { Combobox } from '@/components/ui/combobox';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const currencies = [
	{ value: 'usd', label: 'USD' },
	{ value: 'eur', label: 'EUR' },
	{ value: 'gbp', label: 'GBP' }
];

const contractTypes = [
	{ value: 'period', label: 'Period' },
	{ value: 'trip-tx', label: 'Trip TC' }
];

const ports = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' }
];

const hireTypes = [
	{ value: 'spot', label: 'Spot' },
	{ value: 'flat', label: 'Flat' }
];

const bunkers = [
	{ value: 'ifo', label: 'IFO' },
	{ value: 'vlsfo', label: 'VLSFO' },
	{ value: 'mgo', label: 'MGO' }
];

export default function VesselCharteredForm2() {
	const [, setSelectedCurrency] = useState<string | null>(null);
	const [, setSelectedContractType] = useState<string | null>(null);
	const [, setSelectedPorts] = useState<string | null>(null);
	const [, setSelectedHireType] = useState<string | null>(null);
	const [, setSelectedBunker] = useState<string | null>(null);
	const [date, setDate] = useState<Date | undefined>();
	return (
		<>
			<h3 className="text-base font-semibold">Time chartered</h3>
			<div className="grid w-full flex-1 grid-cols-[auto_minmax(0px,1fr)_auto_minmax(0px,1fr)] gap-x-6 gap-y-2 text-sm">
				<div className="text-muted-foreground flex items-center gap-2">
					<Hash className="size-4" />
					Time Charter ID
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="Enter ID" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Calendar className="size-4" />
					C/P Date
				</div>
				<div>
					<DatePicker date={date} setDate={setDate} />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<File className="size-4" />
					Contract Type
				</div>
				<div>
					<Combobox
						data={contractTypes}
						placeholder="Choose type"
						className="h-8 font-normal"
						onSelect={value => setSelectedContractType(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<BriefcaseBusiness className="size-4" />
					Counterparty
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="Enter Name" />
				</div>
				<Separator className="col-span-4 my-4" />
				<h4 className="col-span-4 mb-4 text-sm font-semibold">Laydays and Duration</h4>
				<div className="text-muted-foreground flex items-center gap-2">
					<Calendar className="size-4" />
					Laydays
				</div>
				<div className="flex items-center gap-2">
					<DatePicker date={date} setDate={setDate} />
					<Input className="h-8 min-w-20" placeholder="HH:MM" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Calendar className="size-4" />
					Canceling
				</div>
				<div className="flex items-center gap-2">
					<DatePicker date={date} setDate={setDate} />
					<Input className="h-8 min-w-20" placeholder="HH:MM" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Timer className="size-4" />
					Duration
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0" suffix="days" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Anchor className="size-4" />
					Delivery Place
				</div>
				<div>
					<Combobox
						data={ports}
						placeholder="Choose port"
						className="h-8 font-normal"
						onSelect={value => setSelectedPorts(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Anchor className="size-4" />
					Redelivery Place
				</div>
				<div>
					<Combobox
						data={ports}
						placeholder="Choose port"
						className="h-8 font-normal"
						onSelect={value => setSelectedPorts(value)}
					/>
				</div>
				<Separator className="col-span-4 my-4" />
				<h4 className="col-span-4 mb-4 text-sm font-semibold">Hire and Commission</h4>
				<div className="text-muted-foreground flex items-center gap-2">
					<DollarSign className="size-4" />
					Daily Hire rate
				</div>
				<div className="flex items-center gap-2">
					<Input className="h-8 min-w-40" placeholder="00.00" />
					<Combobox
						data={currencies}
						placeholder="USD"
						className="h-8 font-normal"
						onSelect={value => setSelectedCurrency(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Text className="size-4" />
					Daily Hire type
				</div>
				<div>
					<Combobox
						data={hireTypes}
						placeholder="Choose type"
						className="h-8 font-normal"
						onSelect={value => setSelectedHireType(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Percent className="size-4" />
					Ballast Bonus
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0" suffix="%" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Percent className="size-4" />
					Addr. Commission
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0" suffix="%" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Percent className="size-4" />
					Brok. Commission
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0" suffix="%" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<DollarSign className="size-4" />
					ILOHC amount
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0.00" suffix="USD" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<DollarSign className="size-4" />
					IHC amount
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0.00" suffix="USD" />
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<DollarSign className="size-4" />
					CVE amount
				</div>
				<div>
					<Input className="h-8 min-w-40" placeholder="0.00" suffix="USD" />
				</div>
			</div>
			<Separator />
			<h4 className="text-sm font-semibold">Bunker quantity and price</h4>
			<Table className="text-sm">
				<TableHeader>
					<TableRow>
						<TableHead className="w-[180px]">Grade</TableHead>
						<TableHead colSpan={2}>On delivery</TableHead>
						<TableHead colSpan={2}>On redelivery</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow>
						<TableCell>
							<Combobox
								data={bunkers}
								initialValue="vlsfo"
								placeholder="Choose bunker"
								className="h-8 font-normal"
								onSelect={value => setSelectedBunker(value)}
							/>
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="000.000" suffix="mts" />
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="0.00" suffix="USD" />
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="000.000" suffix="mts" />
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="0.00" suffix="USD" />
						</TableCell>
					</TableRow>
					<TableRow>
						<TableCell>
							<Combobox
								data={bunkers}
								initialValue="mgo"
								placeholder="Choose bunker"
								className="h-8 font-normal"
								onSelect={value => setSelectedBunker(value)}
							/>
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="000.000" suffix="mts" />
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="0.00" suffix="USD" />
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="000.000" suffix="mts" />
						</TableCell>
						<TableCell>
							<Input className="h-8" placeholder="0.00" suffix="USD" />
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</>
	);
}
