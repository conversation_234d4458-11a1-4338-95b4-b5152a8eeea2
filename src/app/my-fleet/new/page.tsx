import { useState } from 'react';
import VesselFooter from './components/vessel-footer';
import VesselOwnedForm from './components/vessel-owned-form';
import VesselConsumptionForm from './components/vessel-consumption-form';
import VesselCharteredForm from './components/vessel-chartered-form';
import VesselGeneralForm from './components/vessel-general-form';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';

export default function NewVesselPage() {
	const [vesselType, setVesselType] = useState<string>('owned');

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/my-fleet">My Fleet</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>New Vessel</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-6 overflow-auto p-4">
				<div className="w-full max-w-4xl">
					<div className="grid gap-6">
						<h2 className="text-xl font-semibold">Add New Vessel</h2>
						<VesselGeneralForm vesselType={vesselType} setVesselType={setVesselType} />
						<Separator />
						{vesselType === 'chartered' ? <VesselCharteredForm /> : <VesselOwnedForm />}
						<Separator />
						<VesselConsumptionForm />
					</div>
				</div>
			</div>
			<VesselFooter />
		</>
	);
}
