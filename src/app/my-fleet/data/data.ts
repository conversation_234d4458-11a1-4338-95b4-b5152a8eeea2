import { Vessel } from './schema';

// Mock data for vessels with fixed values
export const vessels: Vessel[] = [
	{
		id: '1',
		name: 'mv Meridian',
		flag: 'nl',
		ownership: 'Owned',
		type: 'Handymax',
		bunkers: '12,500 mt',
		dailyCost: 18500,
		status: 'en-route'
	},
	{
		id: '2',
		name: 'mv Pacific Explorer',
		flag: 'gb',
		ownership: 'Time charter',
		type: 'Capesize',
		bunkers: '22,300 mt',
		dailyCost: 25000,
		status: 'en-route'
	},
	{
		id: '3',
		name: 'mv Los Testigos',
		flag: 'br',
		ownership: 'Owned',
		type: 'Panamax',
		bunkers: '15,800 mt',
		dailyCost: 21000,
		status: 'in-port'
	},
	{
		id: '4',
		name: 'mt Desert Harrier',
		flag: 'fr',
		ownership: 'Bareboat charter',
		type: 'Handysize',
		bunkers: '9,200 mt',
		dailyCost: 16500,
		status: 'en-route'
	},
	{
		id: '5',
		name: 'mv Northern Light',
		flag: 'no',
		ownership: 'Time charter',
		type: 'Supramax',
		bunkers: '18,700 mt',
		dailyCost: 22500,
		status: 'in-port'
	},
	{
		id: '6',
		name: 'mv Ocean Voyager',
		flag: 'sg',
		ownership: 'Owned',
		type: 'Panamax',
		bunkers: '16,400 mt',
		dailyCost: 20000,
		status: 'en-route'
	},
	{
		id: '7',
		name: 'mv Global Trader',
		flag: 'de',
		ownership: 'Voyage charter',
		type: 'Handymax',
		bunkers: '13,900 mt',
		dailyCost: 19500,
		status: 'en-route'
	},
	{
		id: '8',
		name: 'mt Coastal Runner',
		flag: 'jp',
		ownership: 'Owned',
		type: 'Handysize',
		bunkers: '8,600 mt',
		dailyCost: 15500,
		status: 'in-port'
	},
	{
		id: '9',
		name: 'mv Atlantic Star',
		flag: 'it',
		ownership: 'Time charter',
		type: 'Capesize',
		bunkers: '24,100 mt',
		dailyCost: 27000,
		status: 'en-route'
	},
	{
		id: '10',
		name: 'mv Silver Wave',
		flag: 'gr',
		ownership: 'Bareboat charter',
		type: 'Ultramax',
		bunkers: '17,300 mt',
		dailyCost: 23000,
		status: 'en-route'
	}
];
