import { ColumnDef } from '@tanstack/react-table';
import { Anchor, Navigation } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import { Vessel } from '../data/schema';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { Badge } from '@/components/ui/badge';
import { formatAmount } from '@/common/utils/formatUtils';

export const columns: ColumnDef<Vessel>[] = [
	{
		accessorKey: 'name',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => {
			const flag = row.original.flag;
			return (
				<div className="flex items-center gap-2">
					<CircleFlag countryCode={flag} className="size-4" />
					<div className="font-semibold">{row.getValue('name')}</div>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = row.getValue('status');
			return (
				<div>
					{status === 'en-route' ? (
						<Badge variant="outline" className="rounded-full">
							<Navigation className="h-3 w-3 text-amber-500" />
							En route
						</Badge>
					) : (
						<Badge variant="outline" className="rounded-full">
							<Anchor className="h-3 w-3 text-blue-500" />
							In port
						</Badge>
					)}
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'ownership',
		meta: {
			label: 'Ownership'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Ownership" />,
		cell: ({ row }) => (
			<Badge variant="secondary" className="text-muted-foreground rounded-full text-xs">
				<span className="font-medium">{row.getValue('ownership')}</span>
			</Badge>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'type',
		meta: {
			label: 'Type'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Type" />,
		cell: ({ row }) => <div>{row.getValue('type')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'bunkers',
		meta: {
			label: 'Bunkers'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Bunkers" reverse />,
		cell: ({ row }) => <div className="text-right">{row.getValue('bunkers')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'dailyCost',
		meta: {
			label: 'Daily Cost'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Daily Cost" reverse />,
		cell: ({ row }) => (
			<div className="text-right font-medium">
				<span className="text-muted-foreground text-2xs uppercase">usd</span>{' '}
				{formatAmount(row.getValue('dailyCost'))}
			</div>
		),
		enableSorting: true,
		enableHiding: true
	}
];
