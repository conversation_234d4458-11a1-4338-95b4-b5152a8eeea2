import { useEffect, useState } from 'react';
import { Vessel } from '../data/schema';
import { vessels } from '../data/data';
import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';

export default function MyFleet() {
	const [data, setData] = useState<Vessel[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		// Simulate loading data
		setLoading(true);

		// In a real app, this would be an API call
		// For now, we'll use the mock data with a timeout
		const timer = setTimeout(() => {
			try {
				setData(vessels);
			} catch (error) {
				console.error('Error loading vessels:', error);
			} finally {
				setLoading(false);
			}
		}, 500);

		// Clean up the timer if the component unmounts
		return () => clearTimeout(timer);
	}, []);

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={data} columns={columns} />;
}
