import HeadingWidget1 from './home/<USER>/heading-widget-1';
import ApprovalsWidget1 from './home/<USER>/approvals-widget-1';
import AIWidget1 from './home/<USER>/ai-widget-1';
import MapWidget1 from './home/<USER>/map-widget-1';
import AmountWidget1 from './home/<USER>/amount-widget-1';
import CompactWidget2 from './home/<USER>/compact-widget-2';
import ApprovalsWidget2 from './home/<USER>/approvals-widget-2';
import LineChartLean from './home/<USER>/content/line-chart-lean';
import CompactWidget3 from './home/<USER>/compact-widget-3';
import ApprovalsWidget3 from './home/<USER>/approvals-widget-3';
import MapWidget2 from './home/<USER>/map-widget-2';
import MapWidget3 from './home/<USER>/map-widget-3';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, <PERSON>readcrumb<PERSON><PERSON>, <PERSON>readcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { ModeToggle } from '@/components/theme/mode-toggle';

export default function Page() {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbPage>Home</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center overflow-auto p-4 pt-0">
				<div className="grid w-full grid-cols-12 gap-6">
					<MapWidget3 className="col-span-12" />
					<MapWidget2 className="col-span-12" />
				</div>
				<div className="grid w-full max-w-5xl grid-cols-12 gap-6">
					<HeadingWidget1 className="col-span-12" />
					<CompactWidget3 className="col-span-12 xl:col-span-4 xl:col-start-5" />
					<ApprovalsWidget3 className="col-span-12 xl:col-span-6" />
					<AmountWidget1
						className="col-span-12 xl:col-span-6"
						title="Balance Exposure"
						amount="$128,231.89"
						subtitle="+20.1% from last month"
					>
						<LineChartLean className="mt-8 w-full md:h-[165px]" />
					</AmountWidget1>
					<MapWidget1 className="col-span-12" />

					<HeadingWidget1 className="col-span-12" />
					<CompactWidget3 className="col-span-12 xl:col-span-4 xl:col-start-5" />
					<ApprovalsWidget2 className="col-span-12 xl:col-span-8" />
					<AmountWidget1
						className="col-span-12 xl:col-span-4"
						title="Balance Exposure"
						amount="$128,231.89"
						subtitle="+20.1% from last month"
					>
						<LineChartLean className="w-full md:h-[160px]" />
					</AmountWidget1>
					<MapWidget1 className="col-span-12" />

					<HeadingWidget1 className="col-span-12" />
					<ApprovalsWidget1 className="col-span-12 xl:col-span-7" />
					<AIWidget1 className="col-span-12 xl:col-span-5" />
					<MapWidget1 className="col-span-12" />

					<HeadingWidget1 className="col-span-12" />
					<CompactWidget2 className="col-span-12" />
					<AmountWidget1
						className="col-span-12 xl:col-span-4"
						title="Demurrage"
						amount="$45,231.89"
						subtitle="+20.1% from last month"
					/>
					<AmountWidget1
						className="col-span-12 xl:col-span-4"
						title="Demurrage"
						amount="$45,231.89"
						subtitle="+20.1% from last month"
					/>
					<AmountWidget1
						className="col-span-12 xl:col-span-4"
						title="Demurrage"
						amount="$45,231.89"
						subtitle="+20.1% from last month"
					/>
					<MapWidget1 className="col-span-12" />
				</div>
			</div>
		</>
	);
}
