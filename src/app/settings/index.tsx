import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const SettingsPage = lazy(() => import('./page'));
const SettingsLayout = lazy(() => import('./layout'));
const EmailTemplatesPage = lazy(() => import('./email/templates'));

export default function Settings() {
	return (
		<Routes>
			<Route
				index
				element={
					<SettingsLayout>
						<SettingsPage />
					</SettingsLayout>
				}
			/>
			<Route path="email">
				<Route
					path="templates"
					element={
						<SettingsLayout>
							<EmailTemplatesPage />
						</SettingsLayout>
					}
				/>
			</Route>
		</Routes>
	);
}
