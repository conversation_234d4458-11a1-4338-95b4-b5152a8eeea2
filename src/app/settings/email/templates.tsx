import { useState } from 'react';
import { Save } from 'lucide-react';
import { toast } from 'sonner';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

// Sample template data - would be fetched from an API in a real implementation
const initialTemplates = [
	{
		id: 'delay-notification',
		name: 'Delay Notification',
		subject: 'Delay Notification – MV {{vessel_name}}',
		content: `Dear {{agent_name}},

Please be advised that MV {{vessel_name}} is delayed by {{delay_hours}} hours en route to {{port_name}} due to {{delay_reason}}. The updated ETA is now {{new_eta}}.

Kindly acknowledge receipt of this update and confirm any necessary adjustments on your end. Please let us know if any further actions are required.

Best regards,
{{sender_name}}
{{company_name}}`
	},
	{
		id: 'port-arrival',
		name: 'Port Arrival Notice',
		subject: 'Port Arrival Notice – MV {{vessel_name}}',
		content: `Dear {{agent_name}},

This is to inform you that MV {{vessel_name}} is scheduled to arrive at {{port_name}} on {{eta_datetime}}.

**Voyage details**:
- Cargo: {{cargo_details}}
- Draft: {{vessel_draft}}
- LOA: {{vessel_loa}}

Please arrange for the necessary port services and confirm receipt of this notice.

Best regards,
{{sender_name}}
{{company_name}}`
	}
];

export default function EmailTemplatesPage() {
	const [templates, setTemplates] = useState(initialTemplates);
	const [selectedTab, setSelectedTab] = useState(templates[0].id);
	const [previewMode, setPreviewMode] = useState(false);
	const [previewValues, setPreviewValues] = useState<Record<string, string>>({});

	// Get the selected template
	const selectedTemplate = templates.find(t => t.id === selectedTab) || templates[0];

	// Extract template variables
	const extractVariables = (text: string): string[] => {
		const matches = text.match(/{{([^}]+)}}/g) || [];
		return [...new Set(matches.map(match => match.replace(/{{|}}/g, '')))];
	};

	const variables = [
		...extractVariables(selectedTemplate.subject),
		...extractVariables(selectedTemplate.content)
	].filter((v, i, a) => a.indexOf(v) === i);

	// Update template content
	const handleContentChange = (content: string) => {
		setTemplates(templates.map(t => (t.id === selectedTab ? { ...t, content } : t)));
	};

	// Update template subject
	const handleSubjectChange = (subject: string) => {
		setTemplates(templates.map(t => (t.id === selectedTab ? { ...t, subject } : t)));
	};

	// Save template
	const handleSave = () => {
		// In a real implementation, this would save to a backend API
		toast.success('Template saved successfully');
	};

	// Replace template variables with their values
	const applyTemplate = (text: string) =>
		text.replace(/{{([^}]+)}}/g, (match, variable) => previewValues[variable as string] || match);

	return (
		<>
			<div>
				<h2 className="text-xl font-semibold">Email Templates</h2>
				<p className="text-muted-foreground text-sm">Manage and customize your email templates</p>
			</div>

			<div className="grid gap-6">
				<Tabs value={selectedTab} onValueChange={setSelectedTab}>
					<div className="flex items-center justify-between">
						<TabsList>
							{templates.map(template => (
								<TabsTrigger key={template.id} value={template.id}>
									{template.name}
								</TabsTrigger>
							))}
						</TabsList>
						<div>
							<Button variant="outline" size="sm" onClick={() => setPreviewMode(!previewMode)}>
								{previewMode ? 'Edit Mode' : 'Preview Mode'}
							</Button>
						</div>
					</div>

					{templates.map(template => (
						<TabsContent key={template.id} value={template.id}>
							<Card>
								<CardHeader>
									<CardTitle>{template.name}</CardTitle>
									<CardDescription>
										{previewMode ? 'Preview Mode' : 'Edit the template fields below'}
									</CardDescription>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="space-y-2">
										<Label htmlFor="subject">Subject</Label>
										{previewMode ? (
											<div className="rounded-md border p-3">
												{applyTemplate(template.subject)}
											</div>
										) : (
											<Input
												id="subject"
												value={template.subject}
												className="w-full"
												onChange={e => handleSubjectChange(e.target.value)}
											/>
										)}
									</div>
									<div className="space-y-2">
										<Label htmlFor="content">Content</Label>
										{previewMode ? (
											<div className="rounded-md border p-3 whitespace-pre-line">
												{applyTemplate(template.content)}
											</div>
										) : (
											<AutosizeTextarea
												id="content"
												value={template.content}
												className="min-h-[200px]"
												onChange={e => handleContentChange(e.target.value)}
											/>
										)}
										<p className="text-muted-foreground text-xs">
											Use double curly braces for variables e.g. {'{{'} variable_name {'}}'}
										</p>
									</div>

									{previewMode && variables.length > 0 && (
										<div className="border-t pt-4">
											<h3 className="mb-3 font-medium">Template Variables</h3>
											<div className="grid gap-3 sm:grid-cols-2">
												{variables.map(variable => (
													<div key={variable} className="space-y-1">
														<Label htmlFor={`var-${variable}`}>{variable}</Label>
														<Input
															id={`var-${variable}`}
															placeholder={variable}
															value={previewValues[variable] || ''}
															className="w-full"
															onChange={e =>
																setPreviewValues({
																	...previewValues,
																	[variable]: e.target.value
																})
															}
														/>
													</div>
												))}
											</div>
										</div>
									)}
								</CardContent>
								<CardFooter className="flex justify-between">
									<Button
										variant="secondary"
										onClick={() => {
											if (previewMode) {
												setPreviewMode(false);
											} else {
												// Reset to initial template
												const original = initialTemplates.find(t => t.id === template.id);
												if (original) {
													setTemplates(
														templates.map(t => (t.id === template.id ? original : t))
													);
												}
											}
										}}
									>
										{previewMode ? 'Edit' : 'Reset'}
									</Button>
									<Button onClick={handleSave}>
										<Save className="mr-2 h-4 w-4" />
										Save Template
									</Button>
								</CardFooter>
							</Card>
						</TabsContent>
					))}
				</Tabs>
			</div>
		</>
	);
}
