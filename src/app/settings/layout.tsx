import { ModeToggle } from '@/components/theme/mode-toggle';
import { SidebarTrigger } from '@/components/ui/sidebar';

export default function Layout({ children }: { children: React.ReactNode }) {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex w-full flex-col items-center overflow-auto py-4">
				<div className="flex w-full max-w-3xl flex-col gap-6 px-4">{children}</div>
			</div>
		</>
	);
}
