import { Check<PERSON><PERSON><PERSON>, Mail } from 'lucide-react';
import { <PERSON> } from 'react-router';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

export default function Page() {
	return (
		<>
			<div>
				<h2 className="text-xl font-semibold">Company profile</h2>
			</div>
			<Card>
				<CardHeader>
					<CardTitle>Company verification</CardTitle>
					<CardDescription>
						In an effort to keep Abraxa a safe and clean place for its users, we need to verify your
						company.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Badge variant="outline" className="rounded-full bg-emerald-600 text-white">
						<CheckCheck className="h-3 w-3" />
						Your company is verified
					</Badge>
				</CardContent>
			</Card>
			<Card>
				<CardHeader>
					<CardTitle>Company details</CardTitle>
					<CardDescription>Your company details</CardDescription>
				</CardHeader>
				<CardContent>Details here</CardContent>
				<CardFooter className="flex justify-end gap-2">
					<Button variant="secondary">Cancel</Button>
					<Button>Save</Button>
				</CardFooter>
			</Card>
			<Card>
				<CardHeader>
					<CardTitle>Email Templates</CardTitle>
					<CardDescription>Manage and customize your email templates</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex items-center gap-2">
						<Mail className="text-primary h-5 w-5" />
						<span>Configure templates with custom variables</span>
					</div>
				</CardContent>
				<CardFooter className="flex justify-end">
					<Link to="/settings/email/templates">
						<Button>Manage Templates</Button>
					</Link>
				</CardFooter>
			</Card>
		</>
	);
}
