import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';

export function InboxDisplaySkeleton() {
	return (
		<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4">
			<div className="flex w-full max-w-xl flex-col gap-6">
				<h2 className="pt-4 text-xl font-bold">
					<Skeleton className="h-3 w-[50%]" />
				</h2>
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-6 text-sm">
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<Separator className="col-span-2" />
					<div className="col-span-2">
						<Skeleton className="h-3 w-[35%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[50%]" />
					</div>
					<div>
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<Separator className="col-span-2" />
					<div className="col-span-2">
						<Skeleton className="h-3 w-[35%]" />
					</div>
					<div className="col-span-2">
						<Skeleton className="h-3 w-[75%]" />
					</div>
					<Separator className="col-span-2" />
					<div className="col-span-2">
						<Skeleton className="h-3 w-[35%]" />
					</div>
					<div className="col-span-2 font-medium opacity-0">Comments</div>
					<div className="opacity-0">
						<i>Please approve the Final DA.</i>
					</div>
				</div>
			</div>
		</div>
	);
}
