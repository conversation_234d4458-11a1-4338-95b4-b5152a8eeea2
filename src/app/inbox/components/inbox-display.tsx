import { Box, BriefcaseBusiness, Calendar, Clock, DollarSign, GitCompareArrows, MapPin, Text } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function InboxDisplay() {
	return (
		<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4">
			<div className="flex w-full max-w-xl flex-col gap-6">
				<h2 className="text-xl font-semibold">Approval request</h2>
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<BriefcaseBusiness className="h-4 w-4" />
						Port call
					</div>
					<div className="flex items-center gap-2">
						<span className="font-medium">mv Meridiaan</span>
						<span className="text-muted-foreground">(#ABX-123)</span>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<GitCompareArrows className="h-4 w-4" />
						Requested by
					</div>
					<div>Wilhelmsen Port Services S.A.</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="h-4 w-4" />
						Requested on
					</div>
					<div>4 Nov 24 - 19:43</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<MapPin className="h-4 w-4" />
						Port
					</div>
					<div>Rotterdam, NL</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="h-4 w-4" />
						ETA
					</div>
					<div>12 Nov 24 - 10:10</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="h-4 w-4" />
						Sailed
					</div>
					<div>19 Nov 24 - 08:00</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Box className="h-4 w-4" />
						Cargo
					</div>
					<div className="flex items-center gap-2">
						<span className="font-medium">2,500.000 mts</span> - Iron Ore
						<span className="text-muted-foreground">(Loading)</span>
					</div>
					<Separator className="col-span-2" />
					<div className="col-span-2 font-medium">Disbursement</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<DollarSign className="h-4 w-4" />
						Final DA Name
					</div>
					<div className="flex items-center gap-2">
						<span className="font-medium">USD 16,873.00</span>
						<Badge variant="outline" className="rounded-full border-emerald-500">
							FDA
						</Badge>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Text className="h-4 w-4" />
						Status
					</div>
					<div>
						<Badge variant="outline" className="bg-background rounded-full">
							<Clock className="h-3 w-3 text-amber-400" />
							Pending approval
						</Badge>
					</div>
					<Separator className="col-span-2" />
					<div className="col-span-2 font-medium">Comments</div>
					<div>
						<i>Please approve the Final DA.</i>
					</div>
					<Separator className="col-span-2" />
					<div className="col-span-2">
						<Button size="sm">View disbursement</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
