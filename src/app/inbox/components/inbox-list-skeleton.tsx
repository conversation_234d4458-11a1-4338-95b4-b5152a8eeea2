import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';

export function InboxListSkeleton() {
	return (
		<ScrollArea className="h-screen">
			<div className="flex flex-col gap-2 p-4">
				<button className="flex flex-col gap-4 rounded-lg p-3">
					<Skeleton className="h-3 w-[100%]" />
					<Skeleton className="h-3 w-[75%]" />
					<Skeleton className="h-3 w-[50%]" />
				</button>
				<button className="flex flex-col gap-4 rounded-lg p-3">
					<Skeleton className="h-3 w-[100%]" />
					<Skeleton className="h-3 w-[75%]" />
					<Skeleton className="h-3 w-[50%]" />
				</button>
				<button className="flex flex-col gap-4 rounded-lg p-3">
					<Skeleton className="h-3 w-[100%]" />
					<Skeleton className="h-3 w-[75%]" />
					<Skeleton className="h-3 w-[50%]" />
				</button>
				<button className="flex flex-col gap-4 rounded-lg p-3">
					<Skeleton className="h-3 w-[100%]" />
					<Skeleton className="h-3 w-[75%]" />
					<Skeleton className="h-3 w-[50%]" />
				</button>
				<button className="flex flex-col gap-4 rounded-lg p-3">
					<Skeleton className="h-3 w-[100%]" />
					<Skeleton className="h-3 w-[75%]" />
					<Skeleton className="h-3 w-[50%]" />
				</button>
			</div>
		</ScrollArea>
	);
}
