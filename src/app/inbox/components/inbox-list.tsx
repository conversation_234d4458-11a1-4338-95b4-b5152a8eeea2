import { formatDistanceToNow } from 'date-fns';
import { CircleCheck, CircleX, Clock } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

const data = [
	{
		id: '6c84fb90-12c4-11e1-840d-7b25c5ee775a',
		name: 'Wilhelmsen Port Services S.A.',
		subject: 'Submitted a disbursement for approval',
		date: '2025-01-28T09:00:00',
		vessel: 'mv <PERSON>ridia<PERSON>',
		portCallId: 'ABX-123',
		object: 'DA-123AA',
		read: false,
		label: 'pending'
	},
	{
		id: '6c84fb90-12c4-11e1-840d-7b25c5ee772a',
		name: 'Varna Port Agent Ltd.',
		subject: 'Accepted your appointment request',
		date: '2025-01-27T17:00:00',
		vessel: 'mv <PERSON>ridia<PERSON>',
		portCallId: 'ABX-123',
		object: 'DA-123AA',
		read: false,
		label: 'accepted'
	},
	{
		id: '6c84fb90-12c4-11e1-840d-7b25c5ee771a',
		name: 'Global Vendor Unlimited AD',
		subject: 'Declined your appointment request',
		date: '2025-01-24T17:00:00',
		vessel: 'mv Meridiaan',
		portCallId: 'ABX-123',
		object: 'DA-123AA',
		read: true,
		label: 'declined'
	},
	{
		id: '7c84fb90-12c4-11e1-840d-7b25c5ee775a',
		name: '<PERSON>sen Port Services S.A.',
		subject: 'Submitted a disbursement for approval',
		date: '2025-01-24T17:00:00',
		vessel: 'mv Meridiaan',
		portCallId: 'ABX-123',
		object: 'DA-123AA',
		read: true,
		label: 'pending'
	},
	{
		id: '8c84fb90-12c4-11e1-840d-7b25c5ee775a',
		name: 'Wilhelmsen Port Services S.A.',
		subject: 'Submitted a disbursement for approval',
		date: '2025-01-24T17:00:00',
		vessel: 'mv Meridiaan',
		portCallId: 'ABX-123',
		object: 'DA-123AA',
		read: true,
		label: 'pending'
	}
];

const renderLabelContent = (label: string) => {
	switch (label) {
		case 'pending':
			return (
				<Badge variant="outline" className="bg-background">
					<Clock className="h-3 w-3 text-amber-400" />
					Pending approval
				</Badge>
			);
		case 'accepted':
			return (
				<Badge variant="outline" className="bg-background">
					<CircleCheck className="h-3 w-3 text-emerald-500" />
					Accepted appointment
				</Badge>
			);
		case 'declined':
			return (
				<Badge variant="outline" className="bg-background">
					<CircleX className="h-3 w-3 text-red-500" />
					Declined appointment
				</Badge>
			);
	}
};

export default function InboxList() {
	return (
		<ScrollArea className="h-screen">
			<div className="flex flex-col gap-2 p-4">
				{data.map(item => (
					<button
						key={item.id}
						className="hover:bg-accent flex flex-col items-start gap-1 rounded-lg border p-3 text-left text-sm transition-all"
					>
						<div className="flex w-full flex-col gap-2">
							<div className="flex items-center">
								<div className="flex items-center gap-2 pr-4">
									<div className="font-semibold">{item.name}</div>
								</div>
								<div className="text-muted-foreground ml-auto flex items-center gap-2 text-xs">
									{item.read ? '' : <span className="bg-primary flex h-2 w-2 rounded-full"></span>}

									{formatDistanceToNow(new Date(item.date), {
										addSuffix: true
									})}
								</div>
							</div>
							<div>{item.subject}</div>
						</div>
						<div className="text-muted-foreground line-clamp-2 text-xs">
							{item.vessel} ({item.portCallId}) › {item.object}
						</div>
						<div className="flex items-center gap-2">{renderLabelContent(item.label)}</div>
					</button>
				))}
			</div>
		</ScrollArea>
	);
}
