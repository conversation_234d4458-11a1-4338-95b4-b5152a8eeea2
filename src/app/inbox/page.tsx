import { Ship } from 'lucide-react';
import { lazy } from 'react';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbSeparator,
	BreadcrumbPage
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const InboxList = lazy(() => delay(100).then(() => import('./components/inbox-list')));
const InboxDisplay = lazy(() => delay(100).then(() => import('./components/inbox-display')));

export default function Page() {
	return (
		<ResizablePanelGroup direction="horizontal">
			<ResizablePanel defaultSize={25} minSize={25} maxSize={50} className="hidden xl:block">
				<header className="flex h-16 shrink-0 items-center justify-between gap-2">
					<div className="flex items-center gap-2 px-4">
						<SidebarTrigger className="-ml-1" />
						<Separator orientation="vertical" className="mr-2 h-4" />
						<Breadcrumb>
							<BreadcrumbList>
								<BreadcrumbItem>
									<BreadcrumbPage>Inbox</BreadcrumbPage>
								</BreadcrumbItem>
							</BreadcrumbList>
						</Breadcrumb>
					</div>
					<div className="flex items-center space-x-2 px-4">
						<span className="text-muted-foreground text-sm">Unread</span>
						<Switch id="inbox" />
					</div>
				</header>
				<div className="px-4">
					<Tabs defaultValue="all" className="w-full">
						<TabsList className="w-full justify-start">
							<TabsTrigger className="flex-1" value="all">
								All
								<Badge variant="outline" className="border-0">
									14
								</Badge>
							</TabsTrigger>
							<TabsTrigger className="flex-1" value="approvals">
								Approvals
							</TabsTrigger>
							<TabsTrigger className="flex-1" value="appointments">
								Appointments
							</TabsTrigger>
						</TabsList>
					</Tabs>
				</div>
				<InboxList />
			</ResizablePanel>
			<ResizableHandle />
			<ResizablePanel className="flex flex-col" defaultSize={75}>
				<header className="flex h-16 shrink-0 items-center justify-between gap-2">
					<div className="flex items-center gap-2 px-4">
						<SidebarTrigger className="-ml-1 block md:hidden" />
						<Separator orientation="vertical" className="mr-2 block h-4 md:hidden" />
						<Breadcrumb>
							<BreadcrumbList>
								<BreadcrumbItem>
									<BreadcrumbLink className="flex items-center gap-2" href="#">
										<Ship className="h-4 w-4" />
										mv Meridiaan
									</BreadcrumbLink>
								</BreadcrumbItem>
								<BreadcrumbSeparator />
								<BreadcrumbItem>
									<BreadcrumbLink href="#">#DA-123AA</BreadcrumbLink>
								</BreadcrumbItem>
								<BreadcrumbSeparator />
								<BreadcrumbItem>
									<BreadcrumbPage>Approval request</BreadcrumbPage>
								</BreadcrumbItem>
							</BreadcrumbList>
						</Breadcrumb>
					</div>
					<div className="px-4">
						<ModeToggle />
					</div>
				</header>
				<InboxDisplay />
			</ResizablePanel>
		</ResizablePanelGroup>
	);
}
