import { useState, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { data as defaultChatData } from './components/chat/data/data';
import { voyagesSummaryData } from './components/chat/data/voyages-summary-data';
import MapHeading from './components/map/map-heading';
import MapBox from './components/mapbox';
import MapAlertsPanel from './components/map-alerts-panel';
import MapSearch from './components/map/map-search';
import MapAISummary from './components/map-ai-summary';
import MapToolbar from './components/map/map-toolbar';
import MapVoyagePanel from './components/map-voyage-panel';
import MapPortcallPanel from './components/map-portcall-panel';
import MapChatPanel from './components/map-chat-panel';
import MapAISummaryPanel from './components/map-ai-summary-panel';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { ResizablePanel, ResizableHandle, ResizablePanelGroup } from '@/components/ui/resizable';

interface VesselInfo {
	name: string;
	voyageId: string;
	pnl?: { amount: string; currency: string };
	nextPort?: string;
}

type AcrivePanel = 'alerts' | 'voyage' | 'portcall' | 'ai' | 'chat';
export default function Page() {
	const [isPanelVisible, setIsPanelVisible] = useState(false);
	const [minimized, setMinimized] = useState(true);
	const [activePanel, setActivePanel] = useState<AcrivePanel | null>(null);
	const [vesselInfo, setVesselInfo] = useState<VesselInfo | null>(null);
	const [useDefaultChatData, setUseDefaultChatData] = useState(false);

	const handleMarkerClick = (options?: {
		resetView: () => void;
		vesselName?: string;
		voyageId?: string;
		pnl?: { amount: string; currency: string };
		nextPort?: string;
	}) => {
		if (options) {
			mapRef.current = options;

			// Store vessel information if provided
			if (options.vesselName && options.voyageId) {
				setVesselInfo({
					name: options.vesselName,
					voyageId: options.voyageId,
					pnl: options.pnl,
					nextPort: options.nextPort
				});
			}
		}
		setIsPanelVisible(true);
		setActivePanel('voyage');
	};

	const handlePanelClose = () => {
		setIsPanelVisible(false);
		setActivePanel(null);
		mapRef.current?.resetView();
	};

	const openAlertsPanel = () => {
		setIsPanelVisible(true);
		setActivePanel('alerts');
	};

	const openVoyagePanel = () => {
		setIsPanelVisible(true);
		setActivePanel('voyage');
	};

	const openPortcallPanel = () => {
		setIsPanelVisible(true);
		setActivePanel('portcall');
	};

	const openAIPanel = () => {
		setIsPanelVisible(true);
		setActivePanel('ai');
	};

	const openChatPanel = (useDefault: boolean = false) => {
		setIsPanelVisible(true);
		setActivePanel('chat');
		setUseDefaultChatData(useDefault);
	};

	const mapRef = useRef<{ resetView: () => void }>();

	return (
		<div className="relative h-screen overflow-hidden">
			<ResizablePanelGroup direction="horizontal" className="overflow-hidden rounded-md">
				<ResizablePanel defaultSize={65} minSize={40}>
					<div className="relative h-full">
						<header className="absolute top-0 right-0 left-0 z-10 flex h-16 items-center justify-between px-4">
							<SidebarTrigger className="-ml-1" />
							<div
								className={cn(
									'flex flex-1 flex-col items-center gap-2 transition-all duration-240 ease-in-out',
									isPanelVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
								)}
							>
								<MapSearch />
							</div>
							<MapToolbar />
						</header>
						<MapBox onMarkerClick={handleMarkerClick} onPanelClose={handlePanelClose} />
					</div>
				</ResizablePanel>
				<div
					className={cn(
						'absolute top-0 right-0 left-0 z-10 m-auto flex max-w-md flex-1 flex-col items-center gap-2 py-4 transition-all duration-240 ease-in-out',
						isPanelVisible ? '-translate-y-full opacity-0' : 'translate-y-0 opacity-100'
					)}
				>
					<MapHeading className={isPanelVisible ? 'opacity-0' : 'opacity-100'} />
					<MapSearch />
				</div>
				<ResizableHandle />
				{isPanelVisible && (
					<AnimatePresence>
						<ResizablePanel defaultSize={35} minSize={30} className="z-50">
							<motion.div
								initial={{ opacity: 0, x: 20 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: 20 }}
								transition={{ duration: 0.24 }}
								className="h-full"
							>
								{activePanel === 'alerts' && (
									<MapAlertsPanel onClose={handlePanelClose} onOpenAIpanel={openAIPanel} />
								)}
								{activePanel === 'voyage' && (
									<MapVoyagePanel
										vesselName={vesselInfo?.name || 'Unknown Vessel'}
										voyageId={vesselInfo?.voyageId || 'Unknown Voyage'}
										pnl={vesselInfo?.pnl || { amount: '0', currency: 'USD' }}
										nextPort={vesselInfo?.nextPort || 'Mumbai, IN'}
										onClose={handlePanelClose}
										onOpenPortcallPanel={openPortcallPanel}
										onOpenAIPanel={openAIPanel}
										onOpenChatPanel={openChatPanel}
									/>
								)}
								{activePanel === 'portcall' && (
									<MapPortcallPanel onClose={handlePanelClose} onOpenVoyagePanel={openVoyagePanel} />
								)}
								{activePanel === 'ai' && <MapAISummaryPanel onOpenVoyagePanel={openVoyagePanel} />}
								{activePanel === 'chat' && (
									<MapChatPanel
										chatId={vesselInfo?.voyageId || 'VOY-12345'}
										chatTitle={
											useDefaultChatData
												? (vesselInfo?.name || 'Vessel') + ' Chat'
												: 'Voyages summary'
										}
										customData={useDefaultChatData ? defaultChatData : voyagesSummaryData}
										onOpenVoyagePanel={openVoyagePanel}
									/>
								)}
							</motion.div>
						</ResizablePanel>
					</AnimatePresence>
				)}
			</ResizablePanelGroup>
			<MapAISummary
				minimized={minimized}
				setMinimized={setMinimized}
				onOpenAlertsPanel={openAlertsPanel}
				onOpenChatPanel={openChatPanel}
			/>
		</div>
	);
}
