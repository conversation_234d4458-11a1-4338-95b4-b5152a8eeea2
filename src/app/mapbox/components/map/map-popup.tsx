import { Popup } from 'react-map-gl/mapbox';
import { ArrowRight, Radio, Webcam } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface PopupInfo {
	longitude: number;
	latitude: number;
	vessel: string;
	voyageId: string;
	nextPort: string;
	eta: string;
	cargo: string;
	charterer: string;
	info: string;
	description: string;
	pnl: {
		amount: string;
		currency: string;
	};
}

interface MapPopupProps {
	popupInfo: PopupInfo;
	onClose: () => void;
}

export default function MapPopup({ popupInfo, onClose }: MapPopupProps) {
	const handleClose = () => {
		onClose();
	};

	return (
		<Popup
			longitude={popupInfo.longitude}
			latitude={popupInfo.latitude}
			closeOnClick={false}
			closeButton
			anchor="bottom"
			maxWidth="none"
			offset={12}
			onClose={handleClose}
		>
			<div className="grid gap-6 font-sans">
				<div className="flex flex-col gap-1">
					<div className="flex items-center gap-2">
						<CircleFlag countryCode="nl" className="h-4" />
						<div className="text-base font-semibold">{popupInfo.vessel}</div>
					</div>
					<div className="text-muted-foreground flex items-center gap-2 text-xs">
						<div className="text-sm">#{popupInfo.voyageId}</div>
						<span>•</span>
						<Badge variant="outline" className="rounded-full">
							<span className="text-muted-foreground">Commenced</span>
						</Badge>
					</div>
				</div>
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-3 gap-y-2 text-xs">
					<div className="text-muted-foreground flex items-center gap-2">
						Next port
						<ArrowRight className="text-muted-foreground size-3.5" />
					</div>
					<div>
						{popupInfo.nextPort} <span className="text-muted-foreground">({popupInfo.eta})</span>
					</div>
					<div className="text-muted-foreground">Cargo</div>
					<div>{popupInfo.cargo}</div>
					<div className="text-muted-foreground">P&L</div>
					<div>
						{popupInfo.pnl && (
							<div>
								{popupInfo.pnl.currency} {popupInfo.pnl.amount}
							</div>
						)}
					</div>
				</div>
				<div className="flex items-center gap-2">
					<Button size="xs" variant="outline" className="text-muted-foreground">
						<Webcam className="size-3" />
						Live
					</Button>
					<Button size="xs" variant="outline" className="text-muted-foreground">
						<Radio className="size-3" />
						25.29 N&nbsp;&nbsp;15.36 W&nbsp;&nbsp;•&nbsp;&nbsp;219°&nbsp;&nbsp;•&nbsp;&nbsp;10.4
						kn&nbsp;&nbsp;•&nbsp;&nbsp;7.9m
					</Button>
				</div>
			</div>
		</Popup>
	);
}
