import { Layers, ListFilter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { ModeToggle } from '@/components/theme/mode-toggle';

export default function MapToolbar() {
	return (
		<div className="flex items-center gap-2">
			<Button variant="outline" size="icon">
				<ListFilter />
			</Button>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="icon">
						<Layers />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuItem>
						<Checkbox id="weather-zones" />
						<label htmlFor="terms">Weather Zones</label>
					</DropdownMenuItem>
					<DropdownMenuItem>
						<Checkbox id="piracy-zones" />
						<label htmlFor="piracy-zones">Piracy Zones</label>
					</DropdownMenuItem>
					<DropdownMenuItem>
						<Checkbox id="ports" />
						<label htmlFor="ports">Ports</label>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
			<ModeToggle />
		</div>
	);
}
