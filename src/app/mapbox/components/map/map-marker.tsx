import { Marker } from 'react-map-gl/mapbox';
import { Anchor, Navigation } from 'lucide-react';
import { MarkerData } from '../../types';

interface MapMarkerProps {
	marker: MarkerData;
	isSelected: boolean;
	onClick: (longitude: number, latitude: number, id: string) => void;
	onMouseEnter: () => void;
	onMouseLeave: () => void;
}

export default function MapMarker({ marker, isSelected, onClick, onMouseEnter, onMouseLeave }: MapMarkerProps) {
	return (
		<Marker
			longitude={marker.longitude}
			latitude={marker.latitude}
			onClick={() => onClick(marker.longitude, marker.latitude, marker.id)}
		>
			<div
				className="group flex cursor-pointer flex-col items-center transition-transform duration-240"
				onMouseEnter={onMouseEnter}
				onMouseLeave={onMouseLeave}
			>
				{marker.status === 'anchored' ? (
					<div
						className={`cursor-pointer rounded-full p-3 transition group-hover:bg-sky-500/10 hover:bg-sky-500/10 ${
							isSelected ? 'scale-100 animate-pulse bg-sky-500/10' : 'scale-75'
						}`}
					>
						<Anchor className={`rotate-${marker.position} size-4 text-sky-400`} />
					</div>
				) : (
					<div
						className={`cursor-pointer rounded-full p-3 transition group-hover:bg-amber-500/10 hover:bg-amber-500/10 ${
							isSelected ? 'scale-100 animate-pulse bg-amber-500/10' : 'scale-75'
						}`}
					>
						<Navigation className={`rotate-${marker.position} size-4 text-amber-400`} />
					</div>
				)}
				<div
					className={`absolute top-8 flex flex-col items-center gap-1 group-hover:text-inherit ${isSelected ? 'text-white' : 'text-muted-foreground'}`}
				>
					<span className="text-shadow-md text-xs text-nowrap">{marker.vessel}</span>
				</div>
			</div>
		</Marker>
	);
}
