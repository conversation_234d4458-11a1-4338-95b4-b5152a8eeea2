import { <PERSON><PERSON>, MoveLeft, Paperclip, Plus, Sparkles } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import ChatbotMin from '@/components/chatbot/chatbot-min';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface Props {
	onOpenVoyagePanel?: () => void;
}

export default function MapAISummaryPanel({ onOpenVoyagePanel }: Props) {
	return (
		<div className="bg-background flex h-full flex-col">
			<div className="flex items-center gap-3 p-4">
				<Button
					variant="ghost"
					size="icon"
					className="size-8 self-start"
					onClick={() => {
						if (onOpenVoyagePanel) {
							onOpenVoyagePanel();
						}
					}}
				>
					<MoveLeft className="h-4 w-4" />
				</Button>
				<div className="flex-1 leading-none font-semibold">AI Summary Panel</div>
			</div>
			<div className="flex h-full flex-1 flex-col gap-6 overflow-auto">
				<div className="flex-1 px-6">
					<div className="bg-panel grid gap-4 rounded-xl p-4 leading-6">
						<div className="flex items-center gap-2 text-sm leading-6">
							<Sparkles className="text-primary size-4" />
							Let me prepare a draft email for you.
						</div>
						<div className="bg-accent rounded-md border text-sm leading-6">
							<div className="flex items-center justify-between border-b px-2 py-2">
								<Select>
									<SelectTrigger className="bg-accent h-auto w-[180px] border-none py-1.5">
										<SelectValue placeholder="Template" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="template1">Template 1</SelectItem>
										<SelectItem value="template2">Template 2</SelectItem>
									</SelectContent>
								</Select>
								<div className="flex items-center gap-1">
									<TooltipProvider delayDuration={100}>
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													variant="ghost"
													size="icon"
													className="text-muted-foreground h-7 w-7"
												>
													<Copy />
												</Button>
											</TooltipTrigger>
											<TooltipContent>Copy</TooltipContent>
										</Tooltip>
										<DropdownMenu>
											<DropdownMenuTrigger>
												<Tooltip>
													<TooltipTrigger asChild>
														<Button
															variant="ghost"
															size="icon"
															className="text-muted-foreground h-7 w-7"
														>
															<Plus />
														</Button>
													</TooltipTrigger>
													<TooltipContent>Add Snippet</TooltipContent>
												</Tooltip>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												<DropdownMenuItem>Snippet 1</DropdownMenuItem>
												<DropdownMenuItem>Snippet 2</DropdownMenuItem>
												<DropdownMenuItem>Snippet 3</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									</TooltipProvider>
								</div>
							</div>
							<div className="px-4 py-2" contentEditable>
								<p className="my-3">
									<strong>Subject:</strong> Delay Notification – MV <em>Suez Navigator</em>
								</p>
								<p className="my-3">Dear [Port Agent&apos;s Name],</p>
								<p className="my-3">
									Please be advised that MV <em>Suez Navigator</em> is delayed by{' '}
									<strong>3 hours</strong> en route to Port Said due to adverse weather conditions.
									The updated ETA is now <strong>[New ETA Time]</strong>.
								</p>
								<p className="my-3">
									Kindly acknowledge receipt of this update and confirm any necessary adjustments on
									your end. Please let us know if any further actions are required.
								</p>
								<p className="my-3">
									Best regards, <br />
									[Your Name] <br />
									[Vessel Operator&apos;s Name]
									<br />
									[Company Name]
								</p>
							</div>
							<div className="flex items-center justify-between border-t p-2">
								<TooltipProvider delayDuration={100}>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="text-muted-foreground h-7 w-7"
											>
												<Paperclip />
											</Button>
										</TooltipTrigger>
										<TooltipContent>Attach</TooltipContent>
									</Tooltip>
								</TooltipProvider>

								<Button size="sm">Send</Button>
							</div>
						</div>
						<div className="grid gap-4">
							<div className="p-2 text-xs font-semibold">Suggestions</div>
							<div className="flex gap-2">
								<Button
									variant="outline"
									className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
								>
									<div className="text-left text-wrap">Call to Action 1</div>
								</Button>
								<Button
									variant="outline"
									className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
								>
									<div className="text-left text-wrap">Call to Action 2</div>
								</Button>
							</div>
						</div>
					</div>
				</div>
				<ChatbotMin />
			</div>
		</div>
	);
}
