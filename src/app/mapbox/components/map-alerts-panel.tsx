import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronRight, TriangleAlert, Sparkles, X, CircleAlert, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ChatbotMin from '@/components/chatbot/chatbot-min';
import { Separator } from '@/components/ui/separator';

const data = [
	{
		id: '1',
		vessel: 'mv Suez Navigator',
		port: 'Aden, YE',
		status: 'En route',
		destination: 'Port Said, EG',
		atd: '03 Mar, 08:00',
		eta: '12 Mar, 17:00',
		time: '12 min ago',
		alert: 'Delayed with 3hrs due to adverse weather conditions',
		type: 'alert'
	},
	{
		id: '2',
		vessel: 'mv Pacific Voyager',
		port: 'Rotterdam, NL',
		status: 'En route',
		destination: 'Shanghai, CN',
		atd: '02 Mar, 10:00',
		eta: '11 Mar, 19:00',
		time: '3 hrs ago',
		alert: 'Delayed with 2hrs due to technical issues',
		type: 'alert'
	}
];

interface Props {
	onClose: () => void;
	onOpenAIpanel?: () => void;
}

export default function MapAlertsPanel({ onClose, onOpenAIpanel }: Props) {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="bg-background flex h-full flex-col">
			<div className="flex h-16 justify-between">
				<div className="flex items-center gap-3 px-6">
					<Sparkles className="text-primary size-4" />
					<div className="flex-1 font-semibold">Summary of your voyages</div>
				</div>
				<div className="flex items-center px-4">
					<Button variant="ghost" size="icon" className="h-8 w-8" onClick={onClose}>
						<X className="h-4 w-4" />
					</Button>
				</div>
			</div>
			<div className="flex h-full flex-1 flex-col gap-6 overflow-auto">
				<div className="flex-1 px-6">
					<div className="bg-panel grid gap-6 rounded-xl p-4 leading-6">
						<div className="grid gap-2 text-sm leading-6">
							<p>
								Your fleet of <b>18 vessels</b> is operating with key alerts requiring attention.
							</p>
							<ul className="ml-4 list-disc [&>li]:mt-2">
								<li>
									<a href="/mapbox" className="font-bold">
										mv Suez Navigator
									</a>{' '}
									is en route to Port Said, EG, with a{' '}
									<a href="/mapbox" className="text-link">
										3-hour delay
									</a>{' '}
									due to adverse weather.
								</li>
								<li>
									<a href="/mapbox" className="font-bold">
										mv Pacific Voyager
									</a>{' '}
									is en route to Shanghai, CN, with a{' '}
									<a href="/mapbox" className="text-link">
										2-hour delay
									</a>{' '}
									due to technical issues.
								</li>
								<li>
									<a href="/mapbox" className="font-bold">
										mv Atlantic Guardian
									</a>
									, en route to Amsterdam, NL, has reported{' '}
									<a href="/mapbox" className="text-link">
										higher fuel consumption
									</a>{' '}
									of 3.1 MT.
								</li>
							</ul>
						</div>
						<div className="grid gap-4">
							<Separator />
							<div className="text-xs font-semibold">Suggestions</div>
							<div className="grid gap-2">
								<div>
									<Button
										variant="outline"
										className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
										onClick={onOpenAIpanel}
									>
										<div className="text-left text-wrap">
											mv Suez Navigator: Notify Port Agent of 3-hour delay
										</div>
									</Button>
								</div>
								<div>
									<Button
										variant="outline"
										className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
										onClick={onOpenAIpanel}
									>
										<div className="text-left text-wrap">
											mv Pacific Voyager: Investigate and update stakeholders.
										</div>
									</Button>
								</div>
								<div>
									<Button
										variant="outline"
										className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
										onClick={onOpenAIpanel}
									>
										<div className="text-left text-wrap">
											mv Atlantic Guardian: Analyze fuel consumption and optimize.
										</div>
									</Button>
								</div>
							</div>
						</div>
						<Separator />
						<div className="grid gap-4">
							<div className="text-muted-foreground text-sm">
								<Button
									variant="ghost"
									className="h-auto gap-1 px-2 py-1 font-normal"
									onClick={() => {
										setExpanded(prev => !prev);
									}}
								>
									Issues
									<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
								</Button>
							</div>
							<AnimatePresence>
								{expanded && (
									<motion.div
										initial={{ height: 0, overflow: 'hidden' }}
										animate={{ height: 'auto', overflow: 'visible' }}
										exit={{ height: 0, overflow: 'hidden' }}
										transition={{ duration: 0.15 }}
									>
										<div className="grid gap-2">
											{data.map(item => (
												<Button
													key={item.id}
													variant="outline"
													className="hover:bg-accent/50 h-auto justify-start bg-transparent px-2"
													onClick={onOpenAIpanel}
												>
													<div className="flex w-full items-center gap-2">
														{item.type === 'warning' && (
															<CircleAlert className="mx-2 text-amber-500" />
														)}
														{item.type === 'alert' && (
															<TriangleAlert className="mx-2 text-red-500" />
														)}
														<div className="grid flex-1 gap-1">
															<div className="flex items-center justify-between gap-2">
																<div className="flex flex-1 items-center justify-between gap-2">
																	<div className="font-medium">{item.vessel}</div>
																	<div className="text-muted-foreground text-xs">
																		{item.time}
																	</div>
																</div>
																<ChevronRight className="text-muted-foreground" />
															</div>
															<div className="text-muted-foreground text-left text-sm font-normal text-wrap">
																{item.alert}
															</div>
														</div>
													</div>
												</Button>
											))}
										</div>
									</motion.div>
								)}
							</AnimatePresence>
						</div>
					</div>
				</div>
				<ChatbotMin />
			</div>
		</div>
	);
}
