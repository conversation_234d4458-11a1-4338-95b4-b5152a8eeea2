import { X, CircleDotDashed, Maximize2 } from 'lucide-react';
import { useNavigate } from 'react-router';
import { CircleFlag } from 'react-circle-flags';
import VoyageOverview from './voyage/overview/voyage-overview';
import VoyageActivity from './voyage/activity/voyage-activity';
import VoyagePnl from './voyage/pnl/voyage-pnl';
import Voyage from './voyage/voyage/voyage';
import VoyageItinerary from './voyage/itinerary/voyage-itinerary';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import ChatbotSimple from '@/components/chatbot/chatbot-simple';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface MapVoyagePanelProps {
	onClose: () => void;
	onOpenPortcallPanel: () => void;
	onOpenAIPanel: () => void;
	onOpenChatPanel?: () => void;
	vesselName?: string;
	voyageId?: string;
	pnl?: { amount: string; currency: string };
	nextPort?: string;
}

const tabClassName =
	'data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border hover:text-foreground';

export default function MapVoyagePanel({
	onClose,
	onOpenPortcallPanel,
	onOpenAIPanel,
	onOpenChatPanel,
	vesselName = 'mv Mediterranean Falcon',
	voyageId = '#VOY-1234-AA',
	pnl = { amount: '0.00', currency: 'USD' },
	nextPort = 'Mumbai, IN'
}: MapVoyagePanelProps) {
	const navigate = useNavigate();

	return (
		<div className="bg-background flex h-full flex-col">
			<div className="flex items-start gap-3 px-4 py-6">
				<div className="grid flex-1 gap-2 px-2">
					<div className="flex items-center gap-2">
						<CircleFlag countryCode="nl" className="h-4" />
						<div className="leading-none font-semibold">{vesselName}</div>
					</div>
					<div className="text-muted-foreground flex items-center gap-2 text-sm">
						<div>#{voyageId}</div>
						<span>•</span>
						<Badge variant="outline" className="rounded-full">
							<CircleDotDashed className="size-3 text-emerald-500" />
							<span className="text-muted-foreground">Commenced</span>
						</Badge>
					</div>
				</div>
				<TooltipProvider delayDuration={100}>
					<div className="flex items-center justify-end gap-2">
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="text-muted-foreground size-8 self-start"
									onClick={() => navigate('/operations/voyages/7f8a9b0c-1d2e-4f3a-5b6c-7d8e9f0a1b2c')}
								>
									<Maximize2 className="h-4 w-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Expand</TooltipContent>
						</Tooltip>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="text-muted-foreground size-8 self-start"
									onClick={onClose}
								>
									<X className="h-4 w-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Close</TooltipContent>
						</Tooltip>
					</div>
				</TooltipProvider>
			</div>
			<Tabs defaultValue="overview" className="flex flex-1 flex-col overflow-auto">
				<TabsList className="mx-6 flex border bg-transparent px-0.5">
					<TabsTrigger value="overview" className={tabClassName}>
						Overview
					</TabsTrigger>
					<TabsTrigger value="itinerary" className={tabClassName}>
						Itinerary
					</TabsTrigger>
					<TabsTrigger value="voyage" className={tabClassName}>
						Voyage
					</TabsTrigger>
					<TabsTrigger value="pnl" className={tabClassName}>
						P&L
					</TabsTrigger>
					<TabsTrigger value="activity" className={tabClassName}>
						Activity
					</TabsTrigger>
				</TabsList>
				<TabsContent value="overview">
					<VoyageOverview
						pnl={pnl}
						nextPort={nextPort}
						onOpenPortcallPanel={onOpenPortcallPanel}
						onOpenAIPanel={onOpenAIPanel}
						onOpenChatPanel={onOpenChatPanel}
					/>
				</TabsContent>
				<TabsContent value="itinerary">
					<VoyageItinerary />
				</TabsContent>
				<TabsContent value="voyage">
					<Voyage onOpenPortcallPanel={onOpenPortcallPanel} />
				</TabsContent>
				<TabsContent value="pnl">
					<VoyagePnl />
				</TabsContent>
				<TabsContent value="activity">
					<VoyageActivity />
				</TabsContent>
			</Tabs>
			<ChatbotSimple onOpenChatPanel={onOpenChatPanel} />
		</div>
	);
}
