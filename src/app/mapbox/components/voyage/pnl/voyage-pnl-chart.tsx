import { Area, AreaChart, XAxis } from 'recharts';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

const chartData = [
	{ day: 'Mon 17 Mar', actual: 43000, estimated: 32000 },
	{ day: 'Tue 18 Mar', actual: 54000, estimated: 32000 },
	{ day: 'Wed 19 Mar', actual: 22800, estimated: 32000 },
	{ day: 'Thu 20 Mar', actual: 47000, estimated: 32000 },
	{ day: 'Fri 21 Mar', actual: 36000, estimated: 32000 },
	{ day: 'Sat 22 Mar', actual: 53000, estimated: 32000 },
	{ day: 'Sun 23 Mar', actual: 59000, estimated: 32000 }
];

const chartConfig = {
	actual: {
		label: 'Actual',
		color: 'hsl(var(--chart-1))'
	},
	estimated: {
		label: 'Est.',
		color: 'hsl(var(--chart-3))'
	}
} satisfies ChartConfig;

export default function VoyagePnlChart() {
	return (
		<div className="bg-panel rounded-xl border">
			<div className="grid grid-cols-2 border-b p-4">
				<div className="flex flex-col gap-1">
					<div className="text-muted-foreground flex w-full justify-start text-xs font-normal">
						Estimated P&L
					</div>
					<div className="flex items-end gap-2">
						<div className="text-muted-foreground text-xs">USD</div>
						<div className="text-xl leading-5 font-semibold">38,700.00</div>
					</div>
				</div>
				<div className="flex flex-col gap-1">
					<div className="text-muted-foreground flex w-full justify-end text-xs font-normal">Actual P&L</div>
					<div className="flex items-end justify-end gap-2">
						<div className="text-muted-foreground text-xs">USD</div>
						<div className="text-xl leading-5 font-semibold">45,330.00</div>
					</div>
				</div>
			</div>
			<ChartContainer config={chartConfig} className="h-44 w-full">
				<AreaChart
					accessibilityLayer
					data={chartData}
					margin={{
						top: 24,
						left: 16,
						bottom: 8,
						right: 16
					}}
				>
					<XAxis
						dataKey="day"
						tickLine
						axisLine
						tickMargin={8}
						fontSize={10}
						stroke="var(--color-border)"
						color="var(--color-muted-foreground)"
						tickFormatter={(value: string) => value.slice(0, 3)}
					/>
					<ChartTooltip cursor={false} content={<ChartTooltipContent />} />
					<defs>
						<linearGradient id="fillPnl" x1="0" y1="0" x2="0" y2="1">
							<stop offset="5%" stopColor="var(--color-actual)" stopOpacity={0.6} />
							<stop offset="95%" stopColor="var(--color-actual)" stopOpacity={0} />
						</linearGradient>
					</defs>
					<Area
						dataKey="actual"
						type="step"
						fill="url(#fillPnl)"
						fillOpacity={0.4}
						stroke="var(--color-actual)"
						stackId="a"
						dot={{
							r: 1,
							fill: 'var(--color-actual)'
						}}
					/>
					<Area
						dataKey="estimated"
						type="linear"
						strokeWidth={0.5}
						strokeDasharray="1 3"
						fill="url(#fillPnl)"
						fillOpacity={0}
						stroke="var(--color-estimated)"
						stackId="b"
					/>
				</AreaChart>
			</ChartContainer>
		</div>
	);
}
