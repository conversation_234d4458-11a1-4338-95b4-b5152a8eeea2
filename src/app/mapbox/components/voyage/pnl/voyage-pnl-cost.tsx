import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const data = {
	revenues: [
		{
			id: 1,
			name: 'Freight',
			currency: 'USD',
			estimated: '810,000',
			actual: '850,000',
			variance: '40,000'
		},
		{
			id: 2,
			name: 'Freight Add.Comm',
			currency: 'USD',
			estimated: '-20,250',
			actual: '-21,250',
			variance: '-1,000'
		},
		{
			id: 3,
			name: '+Dem, -Des',
			currency: '',
			estimated: '',
			actual: '888,000',
			variance: '98,000'
		}
	],
	bunkers: [
		{
			id: 1,
			name: 'VLSFO',
			currency: 'USD',
			estimated: '265,000',
			actual: '285,000',
			variance: '20,000'
		},
		{
			id: 2,
			name: 'LSMGO',
			currency: 'USD',
			estimated: '7,100',
			actual: '7,100',
			variance: '0'
		}
	],
	portExpenses: [
		{
			id: 1,
			name: '<PERSON>sgrunn',
			currency: 'USD',
			estimated: '40,000',
			actual: '48,000',
			variance: '8,000'
		},
		{
			id: 2,
			name: 'Las Palmas',
			currency: 'USD',
			estimated: '5,000',
			actual: '5,000',
			variance: '0'
		},
		{
			id: 3,
			name: 'Paranagua',
			currency: 'USD',
			estimated: '65,000',
			actual: '60,000',
			variance: '-5,000'
		},
		{
			id: 4,
			name: 'Rio Grande',
			currency: 'USD',
			estimated: '40,000',
			actual: '42,000',
			variance: '2,000'
		}
	],
	runningCost: [
		{
			id: 1,
			name: 'Hire',
			currency: 'USD',
			estimated: '450,000',
			actual: '500,000',
			variance: '50,000'
		}
	]
};

export default function VoyagePnlCost() {
	return (
		<div className="grid gap-2">
			<Table className="text-xs">
				<TableHeader>
					<TableRow>
						<TableHead className="w-1/2"></TableHead>
						<TableHead className="text-right">Estimated</TableHead>
						<TableHead className="text-right">Actual</TableHead>
						<TableHead className="text-right">Variance</TableHead>
					</TableRow>
				</TableHeader>
				<TableHeader>
					<TableRow>
						<TableHead colSpan={4} className="text-sm text-inherit">
							Revenues <span className="text-muted-foreground text-xs font-normal">(USD)</span>
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{data.revenues.map(revenue => (
						<TableRow key={revenue.id} className="border-0">
							<TableCell className="text-muted-foreground text-sm">{revenue.name}</TableCell>
							<TableCell className="text-right">{revenue.estimated}</TableCell>
							<TableCell className="text-right font-medium">{revenue.actual}</TableCell>
							<TableCell className="text-right">{revenue.variance}</TableCell>
						</TableRow>
					))}
					<TableRow className="bg-muted/50 border-t">
						<TableCell className="text-muted-foreground font-semibold">Total Revenues</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">789,750</span>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">888,750</span>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">99,000</span>
						</TableCell>
					</TableRow>
					<TableRow className="hover:bg-transparent">
						<TableCell colSpan={4}></TableCell>
					</TableRow>
				</TableBody>
				<TableHeader>
					<TableRow>
						<TableHead colSpan={4} className="text-sm text-inherit">
							Bunkers <span className="text-muted-foreground text-xs font-normal">(USD)</span>
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{data.bunkers.map(bunker => (
						<TableRow key={bunker.id} className="border-0">
							<TableCell className="text-muted-foreground text-sm">{bunker.name}</TableCell>
							<TableCell className="text-right">{bunker.estimated}</TableCell>
							<TableCell className="text-right font-medium">{bunker.actual}</TableCell>
							<TableCell className="text-right">{bunker.variance}</TableCell>
						</TableRow>
					))}
				</TableBody>
				<TableHeader>
					<TableRow>
						<TableHead colSpan={4} className="text-sm text-inherit">
							Port Expenses <span className="text-muted-foreground text-xs font-normal">(USD)</span>
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{data.portExpenses.map(expenses => (
						<TableRow key={expenses.id} className="border-0">
							<TableCell className="text-muted-foreground text-sm">{expenses.name}</TableCell>
							<TableCell className="text-right">{expenses.estimated}</TableCell>
							<TableCell className="text-right font-medium">{expenses.actual}</TableCell>
							<TableCell className="text-right">{expenses.variance}</TableCell>
						</TableRow>
					))}
					<TableRow className="bg-muted/50 border-t">
						<TableCell className="text-muted-foreground font-semibold">Total Expenses</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">422,100</span>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">447,100</span>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">25,000</span>
						</TableCell>
					</TableRow>
					<TableRow className="hover:bg-transparent">
						<TableCell colSpan={4}></TableCell>
					</TableRow>
				</TableBody>
				<TableHeader>
					<TableRow>
						<TableHead colSpan={4} className="text-sm text-inherit">
							Running Cost <span className="text-muted-foreground text-xs font-normal">(USD)</span>
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{data.runningCost.map(cost => (
						<TableRow key={cost.id} className="border-0">
							<TableCell className="text-muted-foreground text-sm">{cost.name}</TableCell>
							<TableCell className="text-right">{cost.estimated}</TableCell>
							<TableCell className="text-right font-medium">{cost.actual}</TableCell>
							<TableCell className="text-right">{cost.variance}</TableCell>
						</TableRow>
					))}
					<TableRow className="bg-muted/50 border-t">
						<TableCell className="text-muted-foreground font-semibold">Profit/Loss</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">-82,350</span>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">-58,350</span>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-muted-foreground">USD</span>&nbsp;
							<span className="font-medium">24,000</span>
						</TableCell>
					</TableRow>
					<TableRow className="hover:bg-transparent">
						<TableCell colSpan={4}></TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
