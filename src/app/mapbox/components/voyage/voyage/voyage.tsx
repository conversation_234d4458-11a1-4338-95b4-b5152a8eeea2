import VoyageDetails from './voyage-details';
import VoyageCharterer from './voyage-charterer';
import VoyageVessel from './voyage-vessel';
import VoyageEmissions from './voyage-emissions';
import VoyageBunkers from './voyage-bunkers';
import VoyageCargo from './voyage-cargo';
import { Separator } from '@/components/ui/separator';

interface Props {
	onOpenPortcallPanel?: () => void;
}

export default function Voyage({ onOpenPortcallPanel }: Props) {
	return (
		<div className="grid gap-6 py-4">
			<VoyageDetails onOpenPortcallPanel={onOpenPortcallPanel} />
			<Separator className="bg-border/50" />
			<VoyageCharterer />
			<Separator className="bg-border/50" />
			<VoyageVessel />
			<Separator className="bg-border/50" />
			<VoyageEmissions />
			<Separator className="bg-border/50" />
			<VoyageBunkers />
			<Separator className="bg-border/50" />
			<VoyageCargo />
		</div>
	);
}
