import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, TrendingUp } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';

const data = {
	bunkers: [
		{
			fuel: 'IFO',
			last24h: '2,056',
			today: '2,490',
			variance: '434',
			unit: 'mt'
		},
		{
			fuel: 'HSFO',
			last24h: '325.3',
			today: '384.7',
			variance: '60.4',
			unit: 'mt'
		}
	]
};

export default function VoyageBunkers() {
	const [expanded, setExpanded] = useState(false);

	return (
		<div className="grid gap-4">
			<div className="text-muted-foreground px-4 text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Bunkers
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="px-6">
							<div className="bg-panel rounded-xl border p-4">
								<Table className="text-xs">
									<TableHeader>
										<TableRow>
											<TableHead className="w-1/3">Fuel</TableHead>
											<TableHead className="text-right">Last 24h</TableHead>
											<TableHead className="text-right">Today</TableHead>
											<TableHead className="text-right">Variance</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{data.bunkers.map(bunker => (
											<TableRow key={bunker.fuel}>
												<TableCell>{bunker.fuel}</TableCell>
												<TableCell className="text-right">
													{bunker.last24h} {bunker.unit}
												</TableCell>
												<TableCell className="text-right">
													{bunker.today} {bunker.unit}
												</TableCell>
												<TableCell className="text-right">
													<div className="flex items-center justify-end gap-2">
														<TrendingUp className="size-4 text-red-400" />
														<span className="text-red-400">
															{bunker.variance} {bunker.unit}
														</span>
													</div>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
