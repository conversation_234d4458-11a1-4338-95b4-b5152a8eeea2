import { CircleFlag } from 'react-circle-flags';
import { Anchor, ArrowUpRight, Box, BriefcaseBusiness, Calendar, FileText, Hash, Link, Text } from 'lucide-react';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const data = {
	voyage: {
		id: 'VOY-MF-01',
		status: 'Commenced',
		vessel: 'mv Mediterranean Falcon',
		loadPort: 'Rotterdam, NL',
		portFunction: 'Loading',
		dischargePort: 'Port Said, EG',
		destinationFunction: 'Discharging',
		legalEntity: 'WPS Legal Singapore',
		laycan: '17 Mar 25 - 19 Mar 25',
		cpType: 'Voyage Charterer',
		cpDate: '18 Mar 25',
		charterer: 'Global Chartering S.A.',
		freightRate: 'USD 25.00',
		loadTerms: '15,000 mts per WWD SHINC',
		dischargeTerms: '4,000 mts SHEX 5PM - MON',
		demurrageRate: 'USD 10,000 PDPR / DHD LTSBE',
		recap: 'View recap',
		atd: '03 Mar, 08:00',
		eta: '12 Mar, 17:00',
		alert: 'Delayed by 12 hours',
		cargo: {
			quantity: '2,500.000 mts',
			type: 'Iron Ore'
		},
		currentPosition: {
			coordinates: [23.7275, 37.9838], // Athens, Greece position
			heading: 135 // Vessel heading in degrees
		},
		route: {
			type: 'FeatureCollection',
			features: [
				{
					type: 'Feature',
					properties: {},
					geometry: {
						type: 'LineString',
						coordinates: [
							[4.1472, 51.9495], // Rotterdam, Netherlands
							[0.6589, 47.3557], // Tours, France
							[-1.9784, 43.2965], // San Sebastian, Spain
							[2.1686, 41.3874], // Barcelona, Spain
							[2.6502, 39.5696], // Palma de Mallorca, Spain
							[13.341, 38.1341], // Palermo, Italy
							[23.7275, 37.9838], // Athens, Greece
							[25.1442, 35.3387], // Heraklion, Greece
							[28.9133, 34.4311], // Mediterranean Sea
							[31.2304, 29.9526] // Port Said, Egypt
						]
					}
				}
			]
		}
	}
};

interface Props {
	onOpenPortcallPanel?: () => void;
}

export default function VoyageDetails({ onOpenPortcallPanel }: Props) {
	return (
		<div className="flex flex-col px-6">
			<div className="bg-panel grid grid-cols-[auto_minmax(0px,1fr)] items-center gap-x-8 gap-y-2 rounded-xl border p-4 text-sm">
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Hash className="size-3.5" />
					Voyage ID
				</div>
				<div className="px-1">{data.voyage.id}</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Text className="size-3.5" />
					Status
				</div>
				<div>
					<Badge variant="outline" className="rounded-full text-sm">
						{data.voyage.status}
					</Badge>
				</div>
				<div className="col-span-2 py-2">
					<Separator />
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Anchor className="size-3.5" />
					Load port
				</div>
				<div>
					<Button variant="ghost" className="h-auto p-1" onClick={onOpenPortcallPanel}>
						<CircleFlag countryCode="nl" className="h-4" />
						{data.voyage.loadPort}
						<ArrowUpRight className="text-muted-foreground -ml-1 size-3.5" />
					</Button>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Anchor className="size-3.5" />
					Discharge port
				</div>
				<div>
					<Button variant="ghost" className="h-auto p-1" onClick={onOpenPortcallPanel}>
						<CircleFlag countryCode="gi" className="h-4" />
						{data.voyage.dischargePort}
						<ArrowUpRight className="text-muted-foreground -ml-1 size-3.5" />
					</Button>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Box className="size-3.5" />
					Cargo
				</div>
				<div className="flex items-center gap-2">
					<Button variant="ghost" className="h-auto p-1">
						{data.voyage.cargo.quantity}
					</Button>
					<Separator className="w-4" />
					<Button variant="ghost" className="h-auto p-1">
						{data.voyage.cargo.type}
					</Button>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Calendar className="size-3.5" />
					Laycan
				</div>
				<div className="px-1">{data.voyage.laycan}</div>
				<div className="col-span-2 py-2">
					<Separator />
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<FileText className="size-3.5" />
					C/P type
				</div>
				<div className="px-1">{data.voyage.cpType}</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Calendar className="size-3.5" />
					C/P date
				</div>
				<div className="px-1">{data.voyage.cpDate}</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<BriefcaseBusiness className="size-3.5" />
					Charterer
				</div>
				<div>
					<Button variant="ghost" className="h-auto p-1">
						{data.voyage.charterer}
					</Button>
				</div>
				<div className="col-span-2 py-2">
					<Separator />
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Text className="size-3.5" />
					Freight rate
				</div>
				<div className="px-1">{data.voyage.freightRate}</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Text className="size-3.5" />
					Load terms
				</div>
				<div className="px-1">{data.voyage.loadTerms}</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Text className="size-3.5" />
					Discharge terms
				</div>
				<div className="px-1">{data.voyage.dischargeTerms}</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Text className="size-3.5" />
					Demurrage rate
				</div>
				<div className="px-1">{data.voyage.demurrageRate}</div>
				<div className="col-span-2 py-2">
					<Separator />
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2">
					<Link className="size-3.5" />
					Recap
				</div>
				<div>
					<Button variant="ghost" className="h-auto p-1">
						{data.voyage.recap}
						<ArrowUpRight className="text-muted-foreground -ml-1 size-3.5" />
					</Button>
				</div>
			</div>
		</div>
	);
}
