import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

const data = {
	timeCharterer: [
		{
			id: '1',
			label: 'Disponent Owners',
			value: 'Sea Stallion Pool Inc. Majuro, The Marshall Islands'
		},
		{
			id: '2',
			label: 'Delivery place',
			value: 'AFSPS TOCOPILLA, CHILE ATDNSSHINC'
		},
		{
			id: '3',
			label: 'Laydays',
			value: 'ATDNSHINC 30 NOV 2015 00:01HRS LOCAL TIME'
		},
		{
			id: '4',
			label: 'Cancelling',
			value: 'ATDNSHINC 5 DECEMBER 2015 23:59 HRS LOCAL TIME'
		},
		{
			id: '5',
			label: 'CP type',
			value: 'ONE TCT VIA CHILE TO SOUTH AFRICA, TRADING ALWAYS VIA GSP/S GSB/S GSA/S ALWAYS AFLOAT ALWAYS W/I INL'
		},
		{
			id: '6',
			label: 'Cargo',
			value: ' ONLY CARGOES ALLOWED ARE CHILEAN NITRATES IN BULK AND BIG BAGS AS PER CHRTS CARGO SPECS'
		},
		{
			id: '7',
			label: 'Redelivery',
			value: 'AFSPS TOCOPILLA, CHILE ATDNSSHINC'
		},
		{
			id: '8',
			label: 'Hire',
			value: 'USD 7000 PER DAY PRORATA INCLOT. PAYABLE 15 DAYS IN ADVANCE'
		},
		{
			id: '9',
			label: 'Hire payment terms',
			value: 'FIRST 15 DAYS HIRE PAYABLE W/I 3 BANKING DAYS AFTER VESSELS DELIVERY TOGETHER WITH VALUE OF'
		},
		{
			id: '10',
			label: 'Bunker Clause',
			value: 'Clause 1'
		},
		{
			id: '11',
			label: 'Delivery messages',
			value: '10d Approximate + 7,5,3,2,1 days  Deffinite notices'
		},
		{
			id: '12',
			label: 'Re-Delivery messages',
			value: '20,15d Approximate + 10,7,5,3,2,1 days Deffinite notices'
		}
	]
};

export default function VoyageCharterer() {
	const [expanded, setExpanded] = useState(false);

	return (
		<div className="grid gap-4">
			<div className="text-muted-foreground px-4 text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Voyage Charterer
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="px-6">
							<div className="bg-panel grid grid-cols-[auto_minmax(0px,1fr)] gap-4 rounded-xl border p-4 text-sm">
								{data.timeCharterer.map(item => (
									<>
										<div className="text-muted-foreground">{item.label}</div>
										<div>{item.value}</div>
									</>
								))}
							</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
