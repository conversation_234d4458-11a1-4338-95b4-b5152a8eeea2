import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function VoyageEmissions() {
	const [expanded, setExpanded] = useState(false);

	return (
		<div className="grid gap-4">
			<div className="text-muted-foreground px-4 text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Emissions
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-4 px-6 text-sm">
							<div className="bg-panel rounded-xl border p-4">[EU ETS, FUEL EU]</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
