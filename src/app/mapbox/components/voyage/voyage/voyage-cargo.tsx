import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Box, BriefcaseBusiness, Calendar, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function VoyageCargo() {
	const [expanded, setExpanded] = useState(false);

	return (
		<div className="grid gap-4">
			<div className="text-muted-foreground px-4 text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Cargo
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="px-6">
							<div className="bg-panel grid gap-6 rounded-xl border p-4 leading-6">
								<div className="ext-sm grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
									<div className="text-muted-foreground flex items-center gap-2">
										<Box className="size-3.5" />
										Cargo
									</div>
									<div className="flex flex-1 items-center gap-2 text-sm">
										<div className="font-medium">2,500.000 mts</div>
										<Separator className="w-4" />
										<div className="text-muted-foreground">Iron Ore</div>
									</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Calendar className="size-3.5" />
										C/P Date
									</div>
									<div>11 Feb 2024</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<BriefcaseBusiness className="size-3.5" />
										Charterer
									</div>
									<div>Global Chartering S.A.</div>
								</div>
							</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
