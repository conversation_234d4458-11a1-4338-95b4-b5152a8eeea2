import { Navigation2, Radio, CircleCheck, Circle, Check } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function VoyageItineraryTimeline() {
	return (
		<div className="relative z-10 grid gap-6 px-2">
			<div className="sep absolute top-8 bottom-24 left-4 -z-10 w-[1px] bg-slate-700"></div>
			<div className="flex gap-2">
				<div>
					<div className="bg-background py-1.5">
						<CircleCheck className="text-primary size-4" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-2">
						<Button variant="ghost" className="h-auto p-1">
							Rotterdam, NL
						</Button>
						<Badge variant="outline" className="text-muted-foreground rounded-full">
							<span>Commencement</span>
							<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>
							<span>Ballast</span>
						</Badge>
					</div>
					<div className="text-muted-foreground grid grid-cols-[60px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-sm">
						<div>Departed:</div>
						<div>11 Feb, 06:00</div>
						<div className="col-span-2 text-right">318 mts / 195 mts</div>
					</div>
				</div>
			</div>
			<div className="flex gap-2">
				<div>
					<div className="bg-background py-1.5">
						<CircleCheck className="text-primary size-4" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-2">
						<Button variant="ghost" className="h-auto p-1">
							Rotterdam, NL
						</Button>
						<Badge variant="outline" className="text-muted-foreground rounded-full">
							<span>Loading</span>
							<span>&nbsp;•&nbsp;</span>
							<span>Laden</span>
							<span>&nbsp;•&nbsp;</span>
							<span>32,000 mts - Iron Ore</span>
						</Badge>
					</div>
					<div className="text-muted-foreground grid grid-cols-[60px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-sm">
						<div>Arrived:</div>
						<div>11 Feb, 06:00</div>
						<div className="col-span-2 text-right">318 mts / 195 mts</div>
						<div>Berthed:</div>
						<div>11 Feb, 06:00</div>
						<div className="col-span-2 text-right">318 mts / 195 mts</div>
						<div>Departed:</div>
						<div>11 Feb, 06:00</div>
						<div className="col-span-2 text-right">318 mts / 195 mts</div>
						<div>Demurrage:</div>
						<div className="text-red-500">USD 22,000</div>
						<div className="col-span-2 text-right">Provision Calc.</div>
						<div>DA Cost:</div>
						<div>USD 90,000</div>
						<div className="col-span-2 flex items-center justify-end gap-2 text-right">
							<Check className="size-3 text-emerald-500" />
							PDA Paid
						</div>
					</div>
				</div>
			</div>
			<div className="flex gap-2">
				<div>
					<div className="bg-background py-1.5">
						<Navigation2 className="size-4 rotate-180 text-amber-500" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-2">
						<div className="min-w-[100px] px-1 text-sm">En route</div>
						<Badge variant="outline" className="text-muted-foreground rounded-full">
							<Radio className="size-3" />
							<span>219°</span>
							<span>&nbsp;•&nbsp;</span>
							<span>10.4 kn</span>
							<span>&nbsp;•&nbsp;</span>
							<span>7.9m</span>
						</Badge>
					</div>
					<div className="text-muted-foreground grid grid-cols-[60px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-sm">
						<div>Actual:</div>
						<div>26 Feb, 16:53</div>
						<div className="col-span-2 text-right">220 mts / 163 mts</div>
					</div>
				</div>
			</div>
			<div className="flex gap-2">
				<div>
					<div className="bg-background py-1.5">
						{/* <CircleFlag countryCode="es" className="h-4" /> */}
						<Circle className="text-muted-foreground size-4" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-2">
						<Button variant="ghost" className="h-auto p-1">
							Las Palmas, ES
						</Button>
						<Badge variant="outline" className="text-muted-foreground rounded-full">
							<span>Bunkering</span>
							<span>&nbsp;•&nbsp;</span>
							<span>Laden</span>
						</Badge>
					</div>
					<div className="text-muted-foreground grid grid-cols-[60px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-sm">
						<div>ETA:</div>
						<div>28 Feb, 06:00</div>
						<div className="col-span-2 text-right">--- mts / --- mts</div>
						<div className="flex items-center">DA Cost:</div>
						<div>
							<Button variant="secondary" size="xs" className="text-primary-foreground">
								Request quote
							</Button>
						</div>
					</div>
				</div>
			</div>
			<div className="flex gap-2">
				<div>
					<div className="bg-background py-1.5">
						<Circle className="text-muted-foreground size-4" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-2">
						<Button variant="ghost" className="h-auto p-1">
							Alappuzha, IN
						</Button>
						<Badge variant="outline" className="text-muted-foreground rounded-full">
							<span>Discharge</span>
							<span>&nbsp;•&nbsp;</span>
							<span>Laden</span>
							<span>&nbsp;•&nbsp;</span>
							<span>32,000 mts - Iron Ore</span>
						</Badge>
					</div>
					<div className="text-muted-foreground grid grid-cols-[60px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-sm">
						<div>ETA:</div>
						<div>11 Mar, 10:00</div>
						<div className="col-span-2 text-right">--- mts / --- mts</div>
						<div className="flex items-center"> Agent:</div>
						<div>
							<Button variant="default" size="xs">
								Appoint agent
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
