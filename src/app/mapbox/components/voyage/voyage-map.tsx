import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

// Replace with your actual Mapbox access token
mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || 'your-mapbox-token';

interface VoyageMapProps {
	voyage: {
		id: string;
		route: [number, number][];
		lastKnownPosition: [number, number];
		previousPort: [number, number];
		currentPort: [number, number];
		nextPort: [number, number];
	};
	className?: string;
}

export function VoyageMap({ voyage, className = '' }: VoyageMapProps) {
	const mapContainer = useRef<HTMLDivElement>(null);
	const map = useRef<mapboxgl.Map | null>(null);

	useEffect(() => {
		if (!mapContainer.current) return;

		// Initialize the map
		map.current = new mapboxgl.Map({
			container: mapContainer.current,
			style: 'mapbox://styles/mapbox/light-v11',
			center: voyage.lastKnownPosition,
			zoom: 3
		});

		// Add navigation controls
		map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

		// Wait for map to load before adding data
		map.current.on('load', () => {
			if (!map.current) return;

			// Add route line
			map.current.addSource('route', {
				type: 'geojson',
				data: {
					type: 'Feature',
					properties: {},
					geometry: {
						type: 'LineString',
						coordinates: voyage.route
					}
				}
			});

			map.current.addLayer({
				id: 'route-line',
				type: 'line',
				source: 'route',
				layout: {
					'line-join': 'round',
					'line-cap': 'round'
				},
				paint: {
					'line-color': '#0077cc',
					'line-width': 3
				}
			});

			// Add ports as points
			const ports = [
				{ id: 'previous-port', coordinates: voyage.previousPort, color: '#6b7280', label: 'Previous Port' },
				{ id: 'current-port', coordinates: voyage.currentPort, color: '#10b981', label: 'Current Port' },
				{ id: 'next-port', coordinates: voyage.nextPort, color: '#3b82f6', label: 'Next Port' }
			];

			// Add ship position marker
			new mapboxgl.Marker({ color: '#ef4444' })
				.setLngLat(voyage.lastKnownPosition)
				.setPopup(new mapboxgl.Popup().setHTML('<h3>Current Position</h3>'))
				.addTo(map.current);

			// Add port markers
			ports.forEach(port => {
				new mapboxgl.Marker({ color: port.color })
					.setLngLat(port.coordinates)
					.setPopup(new mapboxgl.Popup().setHTML(`<h3>${port.label}</h3>`))
					.addTo(map.current as mapboxgl.Map);
			});

			// Fit bounds to include the entire route
			const bounds = new mapboxgl.LngLatBounds();
			voyage.route.forEach(coord => bounds.extend(coord));
			map.current.fitBounds(bounds, { padding: 50 });
		});

		// Clean up on unmount
		return () => {
			if (map.current) {
				map.current.remove();
				map.current = null;
			}
		};
	}, [voyage]);

	return (
		<div className={`voyage-map-container relative overflow-hidden rounded-xl ${className}`}>
			<div ref={mapContainer} className="h-full min-h-[300px] w-full" />
		</div>
	);
}
