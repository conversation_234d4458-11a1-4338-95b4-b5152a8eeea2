import { Text } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';

export default function VoyageActivityTimeline() {
	return (
		<ul className="a-activity-list px-2">
			<li className="flex flex-col">
				<div className="flex flex-row items-center gap-4">
					<Avatar className="h-5 w-5 rounded-sm">
						<AvatarImage src="" alt="" />
						<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
							JD
						</AvatarFallback>
					</Avatar>
					<div className="flex flex-row items-center gap-2 text-sm">
						<span className="font-medium">The Master</span>
						<span className="text-muted-foreground hidden sm:block">sent a report via email</span>
						<span className="text-muted-foreground text-lg">•</span>
						<span className="text-muted-foreground text-xs">3 hrs ago</span>
					</div>
				</div>
				<div className="relative flex min-h-4 flex-row">
					<Separator orientation="vertical" className="a-list-sep absolute left-[9px]"></Separator>

					<div className="flex flex-row items-center gap-2 px-9 py-1 pb-4 text-sm">
						<Text className="h-3 w-3" />
						<i>Due to a storm, we are delayed by 3 hours...</i>
					</div>
				</div>
			</li>
			<li className="flex flex-col">
				<div className="flex flex-row items-center gap-4">
					<Avatar className="h-5 w-5 rounded-sm">
						<AvatarImage src="/static/media/avatar-vicky.webp" alt="Vicky" />
						<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
							JD
						</AvatarFallback>
					</Avatar>
					<div className="flex flex-row items-center gap-2 text-sm">
						<span className="font-medium">V.Encheva</span>
						<span className="text-muted-foreground hidden sm:block">submitted a PDA for approval</span>
						<span className="text-muted-foreground text-lg">•</span>
						<span className="text-muted-foreground text-xs">8 hrs ago</span>
					</div>
				</div>
				<div className="relative flex min-h-4 flex-row">
					<Separator orientation="vertical" className="a-list-sep absolute left-[9px]"></Separator>
				</div>
			</li>
			<li className="flex flex-col">
				<div className="flex flex-row items-center gap-4">
					<Avatar className="h-5 w-5 rounded-sm">
						<AvatarImage src="" alt="" />
						<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
							JD
						</AvatarFallback>
					</Avatar>
					<div className="flex flex-row items-center gap-2 text-sm">
						<span className="font-medium">The Master</span>
						<span className="text-muted-foreground hidden sm:block">sent a report via email</span>
						<span className="text-muted-foreground text-lg">•</span>
						<span className="text-muted-foreground text-xs">11 hrs ago</span>
					</div>
				</div>
				<div className="relative flex min-h-4 flex-row">
					<Separator orientation="vertical" className="a-list-sep absolute left-[9px]"></Separator>

					<div className="flex flex-row items-center gap-2 px-9 py-1 pb-4 text-sm">
						<Text className="h-3 w-3" />
						<i>Higher IFO consumption with 3.1 mt...</i>
					</div>
				</div>
			</li>
		</ul>
	);
}
