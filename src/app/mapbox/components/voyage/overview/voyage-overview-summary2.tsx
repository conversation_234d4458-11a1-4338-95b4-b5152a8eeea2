import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { MoreHorizontal, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface Props {
	onOpenAIpanel?: () => void;
	onOpenChatPanel?: (useDefault?: boolean) => void;
}

export default function VoyageOverviewSummary2({ onOpenAIpanel, onOpenChatPanel }: Props) {
	const [expanded, setExpanded] = useState(false);
	return (
		<div className="grid gap-2 px-6">
			<div>
				<Button
					variant="ghost"
					className="text-muted-foreground h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Voyage Summary
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<div className="grid gap-4 p-2 leading-6">
				<div className={`text-sm leading-6 ${expanded ? '' : 'a-mask-image'}`}>
					<TooltipProvider>
						<div>
							<p>
								Delayed by 3 hours due to adverse{' '}
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="text-link cursor-help underline decoration-dotted">
											weather conditions.
										</span>
									</TooltipTrigger>
									<TooltipContent>
										<div className="grid gap-2 p-1 leading-5">
											<div className="grid">
												<p>Fm: The Master of the vessel </p>
												<p>To: Agents B&M Agencia Maritima S.A.</p>
												<p>Subject : MV Abtenauer - calling Tocopilla for loading</p>
											</div>
											<p>[Email Body]</p>
										</div>
									</TooltipContent>
								</Tooltip>
							</p>
							<p>
								Additionally, a PDA document is{' '}
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="text-link cursor-help underline decoration-dotted">
											pending your approval.
										</span>
									</TooltipTrigger>
									<TooltipContent>[Email Body]</TooltipContent>
								</Tooltip>
							</p>
							<p>
								Higher fuel{' '}
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="text-link cursor-help underline decoration-dotted">
											consumption of 3.1 mts.
										</span>
									</TooltipTrigger>
									<TooltipContent>[Email Body]</TooltipContent>
								</Tooltip>
							</p>
						</div>
					</TooltipProvider>
					<AnimatePresence>
						{expanded && (
							<motion.div
								initial={{ height: 0, overflow: 'hidden' }}
								animate={{ height: 'auto', overflow: 'visible' }}
								exit={{ height: 0, overflow: 'hidden' }}
								transition={{ duration: 0.15 }}
							>
								<div className="mt-4 grid gap-4">
									<div className="text-xs font-semibold">Suggestions</div>
									<div className="flex flex-wrap gap-2">
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-medium"
											onClick={() => onOpenChatPanel && onOpenChatPanel(true)}
										>
											Summarize voyage
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Check weather forecast
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Approve PDA
											<span className="text-muted-foreground text-xs">(#DA-12345)</span>
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Inform the port agent about the delay
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Check fuel consumption
										</Button>
									</div>
								</div>
							</motion.div>
						)}
					</AnimatePresence>
				</div>
				<div className="flex justify-center">
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									size="xs"
									variant="ghost"
									className="h-auto p-1"
									onClick={() => {
										setExpanded(prev => !prev);
									}}
								>
									<MoreHorizontal className="text-muted-foreground" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>View {expanded ? 'Less' : 'More'}</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
			</div>
		</div>
	);
}
