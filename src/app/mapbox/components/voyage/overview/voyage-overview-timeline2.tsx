import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleCheck, Droplets, Navigation2, Radio } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface Props {
	onOpenPortcallPanel?: () => void;
}

export default function VoyageOverviewTimeline2({ onOpenPortcallPanel }: Props) {
	return (
		<div className="flex w-full items-center gap-2 font-normal">
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" className="h-auto rounded-xl p-1" onClick={onOpenPortcallPanel}>
							<CircleCheck className="text-primary size-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						<div className="grid gap-2 p-1">
							<div className="flex items-center gap-2">
								<div className="text-sm font-medium">Rotterdam, NL</div>
								<Badge variant="outline" className="text-muted-foreground rounded-full">
									<span>Commencement</span>
									<span>•</span>
									<span>Ballast</span>
								</Badge>
							</div>
							<div className="text-muted-foreground grid w-full grid-cols-3 gap-y-2">
								<div>Departed:</div>
								<div>11 Feb, 06:00</div>
								<div>318 mts / 195 mts</div>
							</div>
						</div>
					</TooltipContent>
				</Tooltip>
				<Separator className="flex-1" />
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" className="h-auto rounded-xl p-1" onClick={onOpenPortcallPanel}>
							<CircleCheck className="text-primary size-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						<div className="grid gap-2 p-1">
							<div className="flex items-center gap-2">
								<div className="text-sm font-medium">Rotterdam, NL</div>
								<Badge variant="outline" className="text-muted-foreground rounded-full">
									<span>Loading</span>
									<span>•</span>
									<span>32,000 mts - Iron Ore</span>
								</Badge>
							</div>
							<div className="text-muted-foreground grid w-full grid-cols-3 gap-y-2">
								<div>Departed:</div>
								<div>11 Feb, 06:00</div>
								<div>318 mts / 195 mts</div>
								<div>Demurrage:</div>
								<div className="col-span-2 text-red-500">USD 22,000</div>
								<div>DA Cost:</div>
								<div className="col-span-2">USD 90,000</div>
							</div>
						</div>
					</TooltipContent>
				</Tooltip>
				<Separator className="flex-1" />
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" className="h-auto rounded-xl p-1" onClick={onOpenPortcallPanel}>
							<Navigation2 className="size-4 rotate-90 text-amber-500" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						<div className="grid gap-2 p-1">
							<div className="flex items-center gap-2">
								<div className="text-sm font-medium">En route</div>
								<Badge variant="outline" className="text-muted-foreground rounded-full">
									<Radio className="size-3" />
									<span>219°</span>
									<span>•</span>
									<span>10.4 kn</span>
									<span>•</span>
									<span>7.9m</span>
								</Badge>
							</div>
						</div>
					</TooltipContent>
				</Tooltip>
				<Separator className="flex-1" />
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" className="h-auto rounded-xl p-1" onClick={onOpenPortcallPanel}>
							<Droplets className="text-muted-foreground size-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>
						<div className="grid gap-2 p-1">
							<div className="flex items-center gap-2">
								<div className="text-sm font-medium">Las Palmas, ES</div>
								<Badge variant="outline" className="text-muted-foreground rounded-full">
									<span>Bunkering</span>
									<span>•</span>
									<span>Laden</span>
								</Badge>
							</div>
							<div className="text-muted-foreground grid w-full grid-cols-3 gap-y-2">
								<div>ETA:</div>
								<div>28 Feb, 12:00</div>
								<div>--- mts / --- mts</div>
							</div>
						</div>
					</TooltipContent>
				</Tooltip>
				<Separator className="flex-1" />
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" className="h-auto rounded-xl p-2" onClick={onOpenPortcallPanel}>
							<div className="">
								<div className="flex items-center gap-1">
									<div>Port Said, EG</div>
									<ArrowUpRight className="text-muted-foreground size-3.5" />
								</div>
								<div className="text-muted-foreground text-xs">ETA: 12 Mar, 17:00</div>
							</div>
						</Button>
					</TooltipTrigger>
					<TooltipContent>View port call</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		</div>
	);
}
