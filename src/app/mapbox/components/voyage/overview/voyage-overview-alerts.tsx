import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { TriangleAlert, ChevronDown, ChevronRight, CircleAlert } from 'lucide-react';
import { Button } from '@/components/ui/button';

const alerts = [
	{
		id: '1',
		alert: 'Delayed with 3hrs due to adverse weather conditions',
		type: 'alert'
	},
	{
		id: '2',
		alert: 'A strike is expected in Alappuzha on April 28th.',
		type: 'warning'
	}
];

interface Props {
	onOpenAIpanel?: () => void;
}

export default function VoyageOverviewAlerts({ onOpenAIpanel }: Props) {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="grid gap-2 px-6">
			<div className="text-muted-foreground text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Issues
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-2">
							{alerts.map(item => (
								<Button
									key={item.id}
									variant="outline"
									className="group h-auto justify-start bg-transparent px-2"
									onClick={onOpenAIpanel}
								>
									{item.type === 'warning' && <CircleAlert className="text-amber-500" />}
									{item.type === 'alert' && <TriangleAlert className="text-red-500" />}
									<div className="flex-1 text-left font-normal text-wrap">{item.alert}</div>
									<ChevronRight className="text-muted-foreground opacity-0 group-hover:opacity-100" />
								</Button>
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
