import VoyageOverviewChart2 from './voyage-overview-chart2';
import VoyageOverviewTimeline4 from './voyage-overview-timeline4';
import { Separator } from '@/components/ui/separator';

interface Props {
	onOpenPortcallPanel?: () => void;
	pnl?: {
		amount: string;
		currency: string;
	};
	nextPort?: string;
}

export default function VoyageOverviewGeneral({ onOpenPortcallPanel, pnl, nextPort }: Props) {
	return (
		<div className="px-6">
			<div className="bg-panel rounded-xl border">
				<VoyageOverviewTimeline4 nextPort={nextPort} onOpenPortcallPanel={onOpenPortcallPanel} />
				<Separator />
				<VoyageOverviewChart2 pnl={pnl} />
			</div>
		</div>
	);
}
