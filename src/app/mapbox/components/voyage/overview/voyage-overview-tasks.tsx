import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, Circle, CircleCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const tasks = [
	{
		id: '1',
		status: 'todo',
		task: 'Check PDA is paid',
		type: 'task'
	},
	{
		id: '2',
		status: 'todo',
		task: 'Check original BsL',
		type: 'task'
	},
	{
		id: '3',
		status: 'done',
		task: 'Daily update to the Charterers',
		type: 'task'
	}
];

interface Props {
	onOpenAIpanel?: () => void;
}

export default function VoyageOverviewTasks({ onOpenAIpanel }: Props) {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="grid gap-2 px-6">
			<div className="text-muted-foreground text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Next Tasks
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid">
							{tasks.map(item => (
								<Button
									key={item.id}
									variant="ghost"
									className="group h-auto justify-start bg-transparent px-2"
									onClick={onOpenAIpanel}
								>
									{item.status === 'done' ? (
										<CircleCheck className="text-background fill-primary" />
									) : (
										<Circle className="text-muted-foreground" />
									)}
									<div
										className={cn(
											`flex-1 text-left font-normal text-wrap`,
											item.status === 'done' ? 'text-muted-foreground line-through' : ''
										)}
									>
										{item.task}
									</div>
								</Button>
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
