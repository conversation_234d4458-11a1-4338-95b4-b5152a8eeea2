import { Area, AreaChart, XAxis } from 'recharts';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Button } from '@/components/ui/button';

const chartData = [
	{ day: 'Mon 17 Mar', actual: 2000, estimated: 20000 },
	{ day: 'Tue 18 Mar', actual: 12000, estimated: 20000 },
	{ day: 'Wed 19 Mar', actual: 45330, estimated: 20000 },
	{ day: 'Thu 20 Mar', actual: 32000, estimated: 20000 },
	{ day: 'Fri 21 Mar', actual: 14000, estimated: 20000 },
	{ day: 'Sat 22 Mar', actual: 39500, estimated: 20000 },
	{ day: 'Sun 23 Mar', actual: 45330, estimated: 20000 }
];

const chartConfig = {
	actual: {
		label: 'Actual',
		color: 'hsl(var(--chart-6))'
	},
	estimated: {
		label: 'Est.',
		color: 'hsl(var(--chart-3))'
	}
} satisfies ChartConfig;

export default function VoyageOverviewChart() {
	return (
		<div className="flex items-center gap-4">
			<div className="flex-1">
				<ChartContainer config={chartConfig} className="h-[64px] w-full">
					<AreaChart
						accessibilityLayer
						data={chartData}
						margin={{
							top: 8,
							left: 16,
							bottom: 8,
							right: 8
						}}
					>
						<XAxis dataKey="day" hide tickLine={false} axisLine={false} tickMargin={8} />
						<ChartTooltip cursor={false} content={<ChartTooltipContent />} />
						<defs>
							<linearGradient id="fillPnl" x1="0" y1="0" x2="0" y2="1">
								<stop offset="5%" stopColor="var(--color-actual)" stopOpacity={0.6} />
								<stop offset="95%" stopColor="var(--color-actual)" stopOpacity={0} />
							</linearGradient>
						</defs>
						<Area
							dataKey="actual"
							type="natural"
							fill="url(#fillPnl)"
							fillOpacity={0.4}
							stroke="var(--color-actual)"
							stackId="a"
							dot={{
								r: 1,
								fill: 'var(--color-actual)'
							}}
						/>
						<Area
							dataKey="estimated"
							type="linear"
							strokeWidth={0.5}
							strokeDasharray="1 3"
							fill="url(#fillPnl)"
							fillOpacity={0}
							stroke="var(--color-estimated)"
							stackId="b"
						/>
					</AreaChart>
				</ChartContainer>
			</div>
			<div className="flex justify-end p-2">
				<Button variant="ghost" className="h-auto flex-col gap-1 rounded-xl p-2">
					<div className="text-muted-foreground flex w-full justify-end text-xs font-normal">P&L</div>
					<div className="flex w-full items-end justify-end gap-2">
						<span className="text-muted-foreground text-sm">USD</span>
						<span className="text-xl leading-6 font-semibold text-red-500">-45,330.00</span>
					</div>
				</Button>
			</div>
		</div>
	);
}
