import { Area, AreaChart, XAxis } from 'recharts';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Button } from '@/components/ui/button';

const chartData = [
	{ day: 'Mon 17 Mar', actual: 43000, estimated: 32000 },
	{ day: 'Tue 18 Mar', actual: 54000, estimated: 32000 },
	{ day: 'Wed 19 Mar', actual: 22800, estimated: 32000 },
	{ day: 'Thu 20 Mar', actual: 47000, estimated: 32000 },
	{ day: 'Fri 21 Mar', actual: 36000, estimated: 32000 },
	{ day: 'Sat 22 Mar', actual: 53000, estimated: 32000 },
	{ day: 'Sun 23 Mar', actual: 59000, estimated: 32000 }
];

const chartConfig = {
	actual: {
		label: 'Actual',
		color: 'hsl(var(--chart-1))'
	},
	estimated: {
		label: 'Est.',
		color: 'hsl(var(--chart-3))'
	}
} satisfies ChartConfig;

interface VoyageOverviewChart2Props {
	pnl?: {
		amount: string;
		currency: string;
	};
}

export default function VoyageOverviewChart2({ pnl = { amount: '0', currency: 'USD' } }: VoyageOverviewChart2Props) {
	return (
		<div className="flex items-center gap-4 pb-2">
			<div className="flex-1">
				<ChartContainer config={chartConfig} className="h-[86px] w-full">
					<AreaChart
						accessibilityLayer
						data={chartData}
						margin={{
							top: 16,
							left: 16,
							bottom: 8,
							right: 8
						}}
					>
						<XAxis dataKey="day" hide tickLine={false} axisLine={false} tickMargin={8} />
						<ChartTooltip cursor={false} content={<ChartTooltipContent />} />
						<defs>
							<linearGradient id="fillPnl" x1="0" y1="0" x2="0" y2="1">
								<stop offset="5%" stopColor="var(--color-actual)" stopOpacity={0.6} />
								<stop offset="95%" stopColor="var(--color-actual)" stopOpacity={0} />
							</linearGradient>
						</defs>
						<Area
							dataKey="actual"
							type="step"
							fill="url(#fillPnl)"
							fillOpacity={0.4}
							stroke="var(--color-actual)"
							stackId="a"
							dot={{
								r: 1,
								fill: 'var(--color-actual)'
							}}
						/>
						<Area
							dataKey="estimated"
							type="linear"
							strokeWidth={0.5}
							strokeDasharray="1 3"
							fill="url(#fillPnl)"
							fillOpacity={0}
							stroke="var(--color-estimated)"
							stackId="b"
						/>
					</AreaChart>
				</ChartContainer>
			</div>
			<div className="flex justify-end p-2">
				<Button variant="ghost" className="h-auto flex-col gap-1 rounded-xl p-2">
					<div className="text-muted-foreground flex w-full justify-end text-xs font-normal">P&L</div>
					<div className="flex w-full items-end justify-end gap-2">
						<span className="text-muted-foreground text-xs">{pnl.currency}</span>
						<span className="text-base leading-5 font-semibold">{pnl.amount}</span>
					</div>
				</Button>
			</div>
		</div>
	);
}
