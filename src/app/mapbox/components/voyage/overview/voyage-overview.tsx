import VoyageOverviewGeneral from './voyage-overview-general';
import VoyageOverviewAlerts from './voyage-overview-alerts';
import VoyageOverviewTasks from './voyage-overview-tasks';
import VoyageOverviewSummary2 from './voyage-overview-summary2';
import { Separator } from '@/components/ui/separator';

interface Props {
	onOpenPortcallPanel?: () => void;
	onOpenAIPanel?: () => void;
	onOpenChatPanel?: (useDefault?: boolean) => void;
	pnl?: {
		amount: string;
		currency: string;
	};
	nextPort?: string;
}

export default function VoyageOverview({ onOpenPortcallPanel, onOpenAIPanel, onOpenChatPanel, pnl, nextPort }: Props) {
	return (
		<div className="grid gap-6 py-4">
			<VoyageOverviewGeneral pnl={pnl} nextPort={nextPort} onOpenPortcallPanel={onOpenPortcallPanel} />
			<VoyageOverviewAlerts onOpenAIpanel={onOpenAIPanel} />
			<VoyageOverviewTasks onOpenAIpanel={onOpenAIPanel} />
			<Separator className="bg-border/50" />
			<VoyageOverviewSummary2 onOpenAIpanel={onOpenAIPanel} onOpenChatPanel={onOpenChatPanel} />
		</div>
	);
}
