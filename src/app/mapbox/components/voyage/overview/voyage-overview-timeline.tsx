import { Anchor, Droplets, Navigation2, Radio } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface Props {
	onOpenPortcallPanel?: () => void;
}

export default function VoyageOverviewTimeline({ onOpenPortcallPanel }: Props) {
	return (
		<div className="grid gap-2">
			<div className="flex w-full items-center gap-2 font-normal">
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							{/* <Button variant="ghost" className="h-auto rounded-xl p-2" onClick={onOpenPortcallPanel}>
							<div className="">
								<div className="flex items-center gap-1">
									<div>Rotterdam, NL</div>
									<ArrowUpRight className="text-muted-foreground size-3.5" />
								</div>
								<div className="text-muted-foreground text-xs">ATD: 03 Mar, 08:00</div>
							</div>
						</Button> */}
							<Button
								variant="outline"
								className="h-auto rounded-full bg-transparent px-2 py-1 text-xs"
								onClick={onOpenPortcallPanel}
							>
								<Anchor className="text-muted-foreground size-3" />
								<span>Rotterdam, NL</span>
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">Rotterdam, NL</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<span>Loading</span>
										<span>•</span>
										<span>32,000 mts - Iron Ore</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Departed:</div>
									<div>28 Feb, 12:00</div>
									<div>HSFO:</div>
									<div>318 mts / 195 mts</div>
									<div>Demurrage:</div>
									<div className="text-red-500">USD 22,000</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="flex-1" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" className="h-auto rounded-xl p-1" onClick={onOpenPortcallPanel}>
								<Droplets className="text-muted-foreground size-4" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">Las Palmas, ES</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<span>Bunkering</span>
										<span>•</span>
										<span>Laden</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Departed:</div>
									<div>28 Feb, 12:00</div>
									<div>HSFO:</div>
									<div>195 mts / 425 mts</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="flex-1" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" className="h-auto rounded-xl p-1">
								<Navigation2 className="size-4 rotate-90 text-amber-500" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">En route</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<Radio className="size-3" />
										<span>219°</span>
										<span>•</span>
										<span>10.4 kn</span>
										<span>•</span>
										<span>7.9m</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Distance:</div>
									<div>2,450 nm</div>
									<div>Time:</div>
									<div>4,2 days</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="flex-1" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="h-auto rounded-full bg-transparent px-2 py-1 text-xs"
								onClick={onOpenPortcallPanel}
							>
								<Anchor className="text-muted-foreground size-3" />
								<span>Port Said, EG</span>
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">Port Said, EG</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<span>Disch.</span>
										<span>•</span>
										<span>32,000 mts - Iron Ore</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>ETA:</div>
									<div>17 Mar, 08:00</div>
									<div>HSFO:</div>
									<div>--- mts / --- mts</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>
			<div className="text-muted-foreground flex w-full items-center gap-2 pb-1 text-xs">
				<div className="flex-1 px-2">28 Feb, 12:00</div>
				<div className="flex-1 px-2 text-right">17 Mar, 08:00</div>
			</div>
		</div>
	);
}
