import { Droplets, Radio, Waves, Navigation2, CircleCheck, Circle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface Props {
	onOpenPortcallPanel?: () => void;
	nextPort?: string;
}

export default function VoyageOverviewTimeline3({ onOpenPortcallPanel, nextPort }: Props) {
	return (
		<div className="grid gap-2">
			<div className="flex w-full items-center font-normal">
				<TooltipProvider delayDuration={100}>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="text-muted-foreground h-auto rounded-full bg-transparent px-2 py-1 text-xs"
								onClick={onOpenPortcallPanel}
							>
								<CircleCheck className="text-primary size-3" />
								<span>Rotterdam, NL</span>
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">Rotterdam, NL</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<span>Loading</span>
										<span>•</span>
										<span>32,000 mts - Iron Ore</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Departed:</div>
									<div className="grid grid-cols-2 gap-2">
										<div>28 Feb, 12:00</div>
										<div>318 mts / 195 mts</div>
									</div>
									<div>Demurrage:</div>
									<div className="text-red-500">USD 22,000</div>
									<div>DA Cost:</div>
									<div>USD 90,000</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="bg-muted-foreground/50 ml-1 flex-1" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" className="text-muted-foreground/50 h-auto rounded-xl p-1.5">
								<Waves className="size-3" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">Commenced</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<Radio className="size-3" />
										<span>10.4 kn</span>
										<span>•</span>
										<span>7.9m</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Distance:</div>
									<div>2,450 nm&nbsp;&nbsp;•&nbsp;&nbsp;12 days</div>
									<div>ECA / non ECA:</div>
									<div>1,200 nm / 1,250 nm</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="bg-muted-foreground/50 flex-1" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" className="h-auto rounded-xl p-1.5" onClick={onOpenPortcallPanel}>
								<Droplets className="text-primary size-3" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">Las Palmas, ES</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<span>Bunkering</span>
										<span>•</span>
										<span>Laden</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Arrived:</div>
									<div className="grid grid-cols-2 gap-2">
										<div>11 Feb, 06:00</div>
										<div>318 mts / 195 mts</div>
									</div>
									<div>Departed:</div>
									<div className="grid grid-cols-2 gap-2">
										<div>11 Feb, 06:00</div>
										<div>318 mts / 195 mts</div>
									</div>
									<div>Demurrage:</div>
									<div className="text-red-500">USD 22,000</div>
									<div>DA Cost:</div>
									<div>USD 127,000</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="bg-muted-foreground/50 flex-1" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" className="h-auto rounded-xl p-1.5" onClick={onOpenPortcallPanel}>
								<Navigation2 className="size-3 rotate-90 text-amber-500" />
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">En route</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<Radio className="size-3" />
										<span>219°</span>
										<span>•</span>
										<span>10.4 kn</span>
										<span>•</span>
										<span>7.9m</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>Distance:</div>
									<div>2,450 nm&nbsp;&nbsp;•&nbsp;&nbsp;4,2 days</div>
									<div>ECA / non ECA:</div>
									<div>1,200 nm / 1,250 nm</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
					<Separator className="bg-dotted-amber mr-1 flex-2" />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								className="text-muted-foreground h-auto rounded-full border-dashed bg-transparent px-2 py-1 text-xs"
								onClick={onOpenPortcallPanel}
							>
								<Circle className="text-muted-foreground size-3" />
								<span>{nextPort}</span>
							</Button>
						</TooltipTrigger>
						<TooltipContent>
							<div className="grid gap-2 p-1">
								<div className="flex items-center gap-2">
									<div className="text-sm font-medium">{nextPort}</div>
									<Badge variant="outline" className="text-muted-foreground rounded-full">
										<span>Disch.</span>
										<span>•</span>
										<span>32,000 mts - Iron Ore</span>
									</Badge>
								</div>
								<div className="text-muted-foreground grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-2">
									<div>ETA:</div>
									<div className="grid grid-cols-2 gap-2">
										<div>17 Mar, 08:00</div>
										<div>--- mts / --- mts</div>
									</div>
								</div>
							</div>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>
			<div className="text-muted-foreground flex w-full items-center gap-2 pb-1 text-xs">
				<div className="flex-1 px-2">28 Feb, 12:00</div>
				<div className="flex-1 px-2 text-right">17 Mar, 08:00</div>
			</div>
		</div>
	);
}
