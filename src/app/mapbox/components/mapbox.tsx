import 'mapbox-gl/dist/mapbox-gl.css';
import MapGL, { MapRef, Source, Layer } from 'react-map-gl/mapbox';
import { useState, useRef, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { markers, routeData, routeLayer, portLayer, lightStyle, darkStyle, portPoints } from '../data/data';
import { MarkerData } from '../types';
import MapMarker from './map/map-marker';
import MapPopup from './map/map-popup';

interface Props {
	onMarkerClick: (options: {
		resetView: () => void;
		vesselName?: string;
		voyageId?: string;
		pnl?: { amount: string; currency: string };
		nextPort?: string;
	}) => void;

	onPanelClose?: () => void;
}

interface ViewState {
	longitude: number;
	latitude: number;
	zoom: number;
}

interface PopupInfo {
	longitude: number;
	latitude: number;
	vessel: string;
	voyageId: string;
	nextPort: string;
	eta: string;
	cargo: string;
	charterer: string;
	info: string;
	description: string;
	pnl: {
		amount: string;
		currency: string;
	};
}

export default function MapBox({ onMarkerClick, onPanelClose }: Props) {
	const mapboxAccessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
	const { theme } = useTheme();
	const defaultViewState = {
		longitude: -10,
		latitude: 20,
		zoom: 2
	};
	const [viewState, setViewState] = useState<ViewState>(defaultViewState);
	const [selectedMarker, setSelectedMarker] = useState<string | null>(null);
	const [popupInfo, setPopupInfo] = useState<PopupInfo | null>(null);

	const mapRef = useRef<MapRef>(null);
	const hoverTimerRef = useRef<NodeJS.Timeout | null>(null);

	const resetView = () => {
		mapRef.current?.flyTo({
			center: [defaultViewState.longitude, defaultViewState.latitude],
			zoom: defaultViewState.zoom,
			duration: 2000,
			essential: true,
			curve: 1.42,
			speed: 1.2,
			screenSpeed: 1.5,
			maxDuration: 5000
		});
		setPopupInfo(null);
		setSelectedMarker(null);
		setViewState(defaultViewState);
	};

	const createPopupData = (markerData: MarkerData): PopupInfo => {
		const pnlAmount = String(markerData.pnl?.amount || '0.00');
		const pnlCurrency = String(markerData.pnl?.currency || 'USD');

		const popupData: PopupInfo = {
			longitude: markerData.longitude,
			latitude: markerData.latitude,
			vessel: markerData.vessel,
			voyageId: markerData.voyageId,
			nextPort: markerData.nextPort,
			eta: markerData.eta,
			cargo: markerData.cargo,
			charterer: markerData.charterer,
			info: markerData.info,
			description: markerData.info,
			pnl: {
				amount: pnlAmount,
				currency: pnlCurrency
			}
		};

		return popupData;
	};

	const handleMarkerClick = (longitude: number, latitude: number, vesselId: string) => {
		setSelectedMarker(vesselId);

		const clickedMarker = markers.find(marker => marker.id === vesselId);
		if (clickedMarker) {
			const popupData = createPopupData(clickedMarker);
			setPopupInfo(popupData);

			mapRef.current?.flyTo({
				center: [longitude, latitude],
				zoom: 4,
				duration: 2000,
				essential: true,
				curve: 1.42,
				speed: 1.2,
				screenSpeed: 1.5,
				maxDuration: 5000
			});

			onMarkerClick({
				resetView,
				vesselName: clickedMarker.vessel,
				voyageId: clickedMarker.voyageId,
				pnl: clickedMarker.pnl,
				nextPort: clickedMarker.nextPort
			});
		}
	};

	const handleMarkerHover = (longitude: number, latitude: number, vesselId: string) => {
		if (hoverTimerRef.current) {
			clearTimeout(hoverTimerRef.current);
		}

		hoverTimerRef.current = setTimeout(() => {
			const hoveredMarker = markers.find(marker => marker.id === vesselId);
			if (hoveredMarker) {
				const popupData = createPopupData(hoveredMarker);
				setPopupInfo(popupData);
			}
		}, 300);
	};

	const handleMarkerLeave = () => {
		if (hoverTimerRef.current) {
			clearTimeout(hoverTimerRef.current);
			hoverTimerRef.current = null;
		}

		if (!selectedMarker) {
			setPopupInfo(null);
		}
	};

	const handlePopupClose = () => {
		setPopupInfo(null);
		setSelectedMarker(null);
		// resetView();

		if (onPanelClose) {
			onPanelClose();
		}
	};

	useEffect(
		() => () => {
			if (hoverTimerRef.current) {
				clearTimeout(hoverTimerRef.current);
			}
		},
		[]
	);

	// Function to load custom icon for port markers
	const onMapLoad = () => {
		if (!mapRef.current) return;

		// Load a custom port icon image
		// You can replace this URL with your own custom icon
		mapRef.current.loadImage('/static/media/pin.png', (error, image) => {
			if (error) throw error;

			// Add the image to the map style with the id referenced in portLayer
			if (image && mapRef.current && !mapRef.current.hasImage('port-icon')) {
				mapRef.current.addImage('port-icon', image);
			}
		});
	};

	return (
		<MapGL
			ref={mapRef}
			mapboxAccessToken={mapboxAccessToken}
			mapStyle={theme === 'light' ? lightStyle : darkStyle}
			{...viewState}
			onMove={evt => setViewState(evt.viewState)}
			onLoad={onMapLoad}
		>
			<Source id="route-source" type="geojson" data={routeData as GeoJSON.FeatureCollection}>
				<Layer {...routeLayer} />
			</Source>

			<Source id="port-points-source" type="geojson" data={portPoints as GeoJSON.FeatureCollection}>
				<Layer {...portLayer} />
			</Source>

			{markers.map(marker => (
				<MapMarker
					key={marker.id}
					marker={marker}
					isSelected={selectedMarker === marker.id}
					onClick={handleMarkerClick}
					onMouseEnter={() => handleMarkerHover(marker.longitude, marker.latitude, marker.id)}
					onMouseLeave={handleMarkerLeave}
				/>
			))}

			{popupInfo && <MapPopup popupInfo={popupInfo} onClose={handlePopupClose} />}
		</MapGL>
	);
}
