import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { toast } from 'sonner';
import { Clock, TriangleAlert, Sparkle, Sparkles, Route, CircleAlert, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogClose,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger
} from '@/components/ui/dialog';
import { UploadField } from '@/app/operations/voyages/components/upload-field';

interface MapAISummaryProps {
	minimized?: boolean;
	setMinimized: (minimized: boolean) => void;
	onOpenAlertsPanel?: () => void;
	onOpenChatPanel?: (useDefault?: boolean) => void;
}

export default function MapAISummary({ minimized = false, setMinimized, onOpenChatPanel }: MapAISummaryProps) {
	const [cpTermsFile, setCpTermsFile] = useState<{ url: string; name: string; uuid: string } | null>(null);
	const [isUploading, setIsUploading] = useState(false);

	const handleUploadStateChange = (uploading: boolean) => {
		setIsUploading(_ => {
			if (uploading) return true;
			return false;
		});
	};

	const handlePlan = () => {
		const documents = [
			cpTermsFile && {
				url: cpTermsFile.url,
				name: 'C/P terms',
				type: cpTermsFile.name.split('.').pop(),
				uuid: cpTermsFile.uuid
			}
		].filter(Boolean);
		toast('New voyage created', {
			description: documents[0]?.name
		});
	};

	const isPlanDisabled = isUploading || !cpTermsFile;

	return (
		<Dialog>
			{' '}
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Plan your next voyage</DialogTitle>
					<DialogDescription>Create a new voyage plan and upload relevant documents</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4 py-4">
					<p className="px-2 text-sm">Upload documents</p>
					<div className="flex flex-col items-center gap-2 rounded-lg border-2 border-dashed p-8">
						<UploadField
							title="C/P terms"
							onUpload={(_, fileData) =>
								setCpTermsFile({
									url: fileData.url,
									name: fileData.originalName,
									uuid: fileData.uuid
								})
							}
							onUploadStateChange={handleUploadStateChange}
						/>
					</div>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button size="sm" variant="secondary">
							Cancel
						</Button>
					</DialogClose>
					<Button disabled={isPlanDisabled} size="sm" onClick={handlePlan}>
						{isUploading ? 'Uploading...' : 'Plan'}
					</Button>
				</DialogFooter>
			</DialogContent>
			<div className="bg-panel/60 absolute right-4 bottom-4 z-10 max-w-xs min-w-xs overflow-hidden rounded-xl border shadow-2xl backdrop-blur-lg">
				<div className="flex items-center gap-2 px-3 py-3">
					<Sparkles className="text-primary mx-1 size-4" />
					<div className="flex-1 font-semibold">Operator One</div>
					<Button
						variant="ghost"
						size="icon"
						className="text-muted-foreground h-8 w-8"
						onClick={() => setMinimized(!minimized)}
					>
						<ChevronUp
							className={`size-4 transform transition-transform duration-200 ${minimized ? 'rotate-180' : ''}`}
						/>
					</Button>
				</div>
				<AnimatePresence mode="popLayout">
					{minimized && (
						<motion.div
							initial={{ height: 0, overflow: 'hidden' }}
							animate={{ height: 'auto', overflow: 'visible' }}
							exit={{ height: 0, overflow: 'hidden' }}
							transition={{ duration: 0.15 }}
						>
							<div className="flex h-full origin-top flex-col gap-3 px-4 pb-4">
								<div className="text-muted-foreground text-sm leading-7">
									You have <span className="text-inherit">18 vessels</span> in your fleet, with{' '}
									<Button
										variant="outline"
										className="hover:bg-accent/50 h-auto gap-1 px-1 py-0.5 text-sm font-normal"
									>
										<CircleAlert className="size-3.5 text-amber-500" />4 warnings
									</Button>
									,{' '}
									<Button
										variant="outline"
										className="hover:bg-accent/50 h-auto gap-1 px-1 py-0.5 text-sm font-normal"
									>
										<TriangleAlert className="size-3.5 text-red-500" />2 urgent
									</Button>{' '}
									alerts, and{' '}
									<Button
										variant="outline"
										className="hover:bg-accent/50 h-auto gap-1 px-1 py-0.5 text-sm font-normal"
									>
										<Clock className="text-muted-foreground size-3.5" />2 pending
									</Button>{' '}
									tasks.
								</div>
								<Button
									variant="outline"
									className="hover:bg-accent/50 text-link justify-start border px-2 py-4 font-normal"
									onClick={() => onOpenChatPanel && onOpenChatPanel(false)}
								>
									<Sparkle />
									Summarize my voyages
								</Button>
								<div></div>
								<div className="text-xs font-semibold">Plan</div>
								<DialogTrigger asChild>
									<Button
										variant="outline"
										className="hover:bg-accent/50 justify-start border px-2 py-4 font-normal"
									>
										<Route className="text-muted-foreground" />
										Plan my new voyage
									</Button>
								</DialogTrigger>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</Dialog>
	);
}
