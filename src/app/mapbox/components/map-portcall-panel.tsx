import { <PERSON><PERSON>eft, X, Hash, MapPin, Box } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

interface Props {
	onClose: () => void;
	onOpenVoyagePanel?: () => void;
}

export default function MapPortcallPanel({ onClose, onOpenVoyagePanel }: Props) {
	return (
		<div className="bg-background flex h-full flex-col">
			<div className="flex items-center gap-3 px-4 py-6">
				<Button
					variant="ghost"
					size="icon"
					className="size-8 self-start"
					onClick={() => {
						if (onOpenVoyagePanel) {
							onOpenVoyagePanel();
						}
					}}
				>
					<MoveLeft className="h-4 w-4" />
				</Button>
				<div className="flex flex-1 flex-col gap-1">
					<div className="leading-none font-semibold">mv Mediterranean Falcon</div>
					<div className="text-muted-foreground text-xs">
						Rotterdam, NL&nbsp;&nbsp;•&nbsp;&nbsp;ATD: 12 Mar, 08:00
					</div>
				</div>
				<Button variant="ghost" size="icon" className="size-8 self-start" onClick={onClose}>
					<X className="h-4 w-4" />
				</Button>
			</div>
			<div className="grid-4 grid flex-1 overflow-auto">
				<Tabs defaultValue="overview">
					<TabsList className="mx-6 flex border bg-transparent px-0.5">
						<TabsTrigger
							value="overview"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Overview
						</TabsTrigger>
						<TabsTrigger
							value="docs"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Docs
						</TabsTrigger>
						<TabsTrigger
							value="financial"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Financial
						</TabsTrigger>
						<TabsTrigger
							value="laytime"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Laytime
						</TabsTrigger>
						<TabsTrigger
							value="insights"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Insights
						</TabsTrigger>
					</TabsList>
					<TabsContent value="overview" className="grid gap-6 py-4">
						<div className="grid grid-cols-2 gap-4 px-6">
							<Button
								variant="outline"
								className="hover:bg-accent/50 bg-panel h-auto flex-col items-start rounded-xl"
							>
								<div className="text-muted-foreground w-full text-right text-xs font-normal">
									Balance
								</div>
								<div className="flex w-full items-end justify-end gap-2">
									<span className="text-muted-foreground text-sm">USD</span>
									<span className="text-xl leading-6 font-semibold">183,987.00</span>
								</div>
							</Button>
							<Button
								variant="outline"
								className="hover:bg-accent/50 bg-panel h-auto flex-col items-start rounded-xl"
							>
								<div className="text-muted-foreground w-full text-right text-xs font-normal">
									Demurrage
								</div>
								<div className="flex w-full items-end justify-end gap-2">
									<span className="text-muted-foreground text-sm">USD</span>
									<span className="text-xl leading-6 font-semibold">23,344.00</span>
								</div>
							</Button>
						</div>
						<div className="px-6">
							<div className="rounded-xl border p-4">
								<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
									<div className="text-muted-foreground flex items-center gap-2 text-sm">
										<Hash className="size-3.5" />
										Port call ID
									</div>
									<div className="px-1">#ABX-1234</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<MapPin className="size-3.5" />
										Port
									</div>
									<div className="flex items-center gap-2">
										<Button variant="ghost" className="h-auto p-1">
											<CircleFlag countryCode="nl" className="h-4" />
											Rotterdam, NL
										</Button>
										<Separator className="w-4" />
										<Badge variant="outline" className="rounded-full">
											<Box className="text-primary h-3 w-3" />
											Cargo ops
										</Badge>
									</div>
									<div className="text-muted-foreground flex items-center gap-2">
										<Box className="size-3.5" />
										Cargo
									</div>
									<div className="flex items-center gap-2">
										<Button variant="ghost" className="h-auto p-1">
											2,500.000 mts
										</Button>
										<Separator className="w-4" />
										<Button variant="ghost" className="h-auto p-1">
											Iron Ore
										</Button>
									</div>
								</div>
							</div>
						</div>
					</TabsContent>
					<TabsContent value="docs">
						<div className="p-6">Docs</div>
					</TabsContent>
					<TabsContent value="financial">
						<div className="p-6">Financial</div>
					</TabsContent>
					<TabsContent value="laytime">
						<div className="p-6">Laytime</div>
					</TabsContent>
					<TabsContent value="insights">
						<div className="p-6">Insights</div>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
}
