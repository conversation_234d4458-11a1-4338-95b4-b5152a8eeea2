import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Suggestion {
	id: string;
	refResponse: string;
	text: string;
	icon?: string;
	color?: string;
}

interface ChatInitialSuggestionProps {
	suggestions: Suggestion[];
	onSuggestionClick: (refResponse: string) => void;
}

export default function ChatInitialSuggestions({ suggestions, onSuggestionClick }: ChatInitialSuggestionProps) {
	return (
		<div className="flex flex-wrap justify-center gap-2 pb-6">
			{suggestions.map(item => (
				<Button
					key={item.id}
					variant="outline"
					className="width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
					onClick={() => onSuggestionClick(item.refResponse)}
				>
					{item.icon && React.createElement(item.icon, { className: cn('size-3', item.color) })}
					{item.text}
				</Button>
			))}
		</div>
	);
}
