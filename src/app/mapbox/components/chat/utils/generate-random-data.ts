import { ChatData } from '../context/chat-context';

// Function to generate a random integer between min and max (inclusive)
const getRandomInt = (min: number, max: number): number => Math.floor(Math.random() * (max - min + 1)) + min;

// Function to get a random item from an array
const getRandomItem = <T>(array: T[]): T => array[Math.floor(Math.random() * array.length)];

// Sample data for random generation
const voyageNames = [
	'Mediterranean Voyage',
	'Atlantic Crossing',
	'Pacific Route',
	'Baltic Journey',
	'Caribbean Tour',
	'Suez Transit',
	'Panama Passage',
	'North Sea Route',
	'Indian Ocean Crossing',
	'South China Sea Passage'
];

const portNames = [
	'Rotterdam, NL',
	'Singapore, SG',
	'Shanghai, CN',
	'Antwerp, BE',
	'Los Angeles, US',
	'Hamburg, DE',
	'Dubai, AE',
	'New York, US',
	'Mumbai, IN',
	'Tokyo, JP'
];

const vesselNames = [
	'MV Atlantic Star',
	'MV Pacific Explorer',
	'MV Northern Light',
	'MV Southern Cross',
	'MV Eastern Wind',
	'MV Western Wave',
	'MV Global Voyager',
	'MV Ocean Navigator',
	'MV Sea Pioneer',
	'MV Horizon Seeker'
];

const weatherConditions = [
	'clear skies',
	'light rain',
	'heavy storms',
	'high winds',
	'fog',
	'choppy seas',
	'calm waters',
	'moderate swells',
	'tropical conditions',
	'winter conditions'
];

const cargoTypes = [
	'containers',
	'bulk grain',
	'crude oil',
	'LNG',
	'vehicles',
	'machinery',
	'steel products',
	'timber',
	'chemicals',
	'refrigerated goods'
];

// Generate random voyage summary for a single voyage
const generateSingleVoyageSummary = (): { vessel: string; voyage: string; summary: string } => {
	const vessel = getRandomItem(vesselNames);
	const voyage = getRandomItem(voyageNames);
	const originPort = getRandomItem(portNames);
	const destinationPort = getRandomItem(portNames.filter(port => port !== originPort));
	const weather = getRandomItem(weatherConditions);
	const cargo = getRandomItem(cargoTypes);
	const daysAtSea = getRandomInt(5, 30);
	const fuelConsumption = (getRandomInt(150, 300) / 10).toFixed(1);
	const eta = getRandomInt(1, 7);

	const summary = `
**Voyage**: ${voyage}
**Route**: ${originPort} to ${destinationPort}
**Days at Sea**: ${daysAtSea} days
**Cargo**: ${cargo}
**Weather Conditions**: ${weather}

**Key Metrics**:
- Fuel Consumption: ${fuelConsumption} mt/day
- ETA: ${eta} days
- Average Speed: ${getRandomInt(12, 18)} knots

**Recent Events**:
${getRandomInt(0, 1) ? '- Encountered ' + getRandomItem(weatherConditions) + ' which caused a delay of ' + getRandomInt(2, 12) + ' hours' : ''}
${getRandomInt(0, 1) ? '- Received updated instructions from charterer regarding ' + getRandomItem(cargoTypes) : ''}
${getRandomInt(0, 1) ? '- Maintenance performed on main engine' : ''}
${getRandomInt(0, 1) ? '- Crew change scheduled at next port' : ''}
  `;

	return { vessel, voyage, summary };
};

// Generate summary for multiple voyages
const generateVoyageSummary = (): string => {
	const numberOfVoyages = getRandomInt(3, 5);
	const voyages = [];

	for (let i = 0; i < numberOfVoyages; i++) {
		voyages.push(generateSingleVoyageSummary());
	}

	// Group by vessel
	const vesselGroups: Record<string, { vessel: string; voyage: string; summary: string }[]> = {};
	voyages.forEach(voyage => {
		if (!vesselGroups[voyage.vessel]) {
			vesselGroups[voyage.vessel] = [];
		}
		vesselGroups[voyage.vessel].push(voyage);
	});

	let result = '# Summary of All Active Voyages\n\n';

	Object.keys(vesselGroups).forEach(vessel => {
		result += `## ${vessel}\n\n`;
		vesselGroups[vessel].forEach(voyage => {
			result += voyage.summary + '\n\n';
		});
	});

	return result;
};

// Generate random voyage statistics
const generateVoyageStatistics = (): string => {
	const fleetSize = getRandomInt(3, 6);
	const activeVoyages = getRandomInt(fleetSize - 1, fleetSize);
	const completedVoyages = getRandomInt(10, 20);
	const totalCargoVolume = getRandomInt(100000, 500000);
	const avgFuelConsumption = (getRandomInt(150, 300) / 10).toFixed(1);
	const onTimeDelivery = getRandomInt(85, 98);
	const totalCrewMembers = getRandomInt(15, 25) * fleetSize;

	return `
# Fleet Statistics

## Overall Performance
- Fleet Size: ${fleetSize} vessels
- Active Voyages: ${activeVoyages}
- Completed Voyages (YTD): ${completedVoyages}
- On-Time Delivery Rate: ${onTimeDelivery}%
- Average Voyage Duration: ${getRandomInt(15, 45)} days

## Cargo & Fuel Metrics
- Total Cargo Volume (YTD): ${totalCargoVolume.toLocaleString()} MT
- Average Cargo Utilization: ${getRandomInt(85, 99)}%
- Average Fuel Consumption: ${avgFuelConsumption} MT/day per vessel
- CO₂ Emissions Reduction: ${getRandomInt(5, 15)}% compared to previous year

## Financial Overview
- Average Daily Operating Cost: $${getRandomInt(8000, 15000).toLocaleString()}/vessel
- Fuel Cost as % of Total: ${getRandomInt(40, 60)}%
- Port Fees as % of Total: ${getRandomInt(15, 25)}%
- Maintenance as % of Total: ${getRandomInt(10, 20)}%

## Crew Information
- Total Crew Members: ${totalCrewMembers}
- Crew Nationalities: ${getRandomInt(8, 15)}
- Crew Changes Completed (YTD): ${getRandomInt(fleetSize * 2, fleetSize * 4)}
  `;
};

// Generate random voyage recommendations
const generateVoyageRecommendations = (): string => {
	// Generate a list of potential recommendations
	const operationalRecommendations = [
		'Implement fleet-wide speed optimization to reduce fuel consumption by up to 15%',
		'Standardize maintenance schedules across the fleet to reduce downtime',
		'Consider alternative routing for vessels in the Pacific region due to forecasted storms',
		'Consolidate bunker purchases to take advantage of volume discounts',
		'Review crew rotation schedules to minimize travel costs and disruption'
	];

	const financialRecommendations = [
		'Hedge bunker fuel prices for Q3 and Q4 to mitigate market volatility',
		'Renegotiate port fees at frequently visited ports to secure volume discounts',
		'Consider time charter options for vessels currently on voyage charter to stabilize revenue',
		'Review insurance coverage to ensure optimal protection while avoiding premium increases',
		'Implement digital solutions for documentation to reduce administrative costs'
	];

	const sustainabilityRecommendations = [
		'Accelerate fleet renewal program to improve fuel efficiency and reduce emissions',
		'Explore biofuel options for vessels operating in Emission Control Areas',
		'Implement energy-saving operational measures across the fleet',
		'Prepare for upcoming carbon intensity regulations with fleet-wide assessment',
		'Consider installation of energy-saving devices during next scheduled dry-dockings'
	];

	// Select random recommendations from each category
	const selectRandomRecommendations = (recommendations: string[], count: number): string[] => {
		const selected: string[] = [];
		const indices = new Set<number>();

		while (indices.size < count && indices.size < recommendations.length) {
			indices.add(Math.floor(Math.random() * recommendations.length));
		}

		indices.forEach(index => selected.push(recommendations[index]));
		return selected;
	};

	const selectedOperational = selectRandomRecommendations(operationalRecommendations, 2);
	const selectedFinancial = selectRandomRecommendations(financialRecommendations, 2);
	const selectedSustainability = selectRandomRecommendations(sustainabilityRecommendations, 1);

	return `
# Fleet Optimization Recommendations

## Operational Improvements
1. ${selectedOperational[0]}
2. ${selectedOperational[1]}

## Financial Optimization
1. ${selectedFinancial[0]}
2. ${selectedFinancial[1]}

## Sustainability Initiatives
1. ${selectedSustainability[0]}

## Immediate Actions
- Review and implement the most cost-effective recommendations within the next 30 days
- Schedule a follow-up assessment in 90 days to measure impact
- Consider a comprehensive fleet optimization study to identify additional opportunities
  `;
};

// Generate random chat data
export const generateRandomChatData = (): ChatData => {
	const voyageSummary = generateVoyageSummary();
	const voyageStatistics = generateVoyageStatistics();
	const voyageRecommendations = generateVoyageRecommendations();

	return {
		responses: [
			{
				id: '1',
				text: "I've analyzed data from all your active voyages. Here's a comprehensive overview of your fleet operations. What would you like to explore further?",
				type: 'greeting',
				suggestions: [
					{
						id: '1',
						refResponse: 'voyage-summary',
						text: 'Show all voyages summary'
					},
					{
						id: '2',
						refResponse: 'voyage-statistics',
						text: 'Show fleet statistics'
					},
					{
						id: '3',
						refResponse: 'voyage-recommendations',
						text: 'Show fleet optimization recommendations'
					}
				]
			},
			{
				id: 'voyage-summary',
				text: voyageSummary,
				suggestions: [
					{
						id: '1',
						refResponse: 'voyage-statistics',
						text: 'Show voyage statistics'
					},
					{
						id: '2',
						refResponse: 'voyage-recommendations',
						text: 'Show recommendations'
					}
				]
			},
			{
				id: 'voyage-statistics',
				text: voyageStatistics,
				suggestions: [
					{
						id: '1',
						refResponse: 'voyage-summary',
						text: 'Back to voyage summary'
					},
					{
						id: '2',
						refResponse: 'voyage-recommendations',
						text: 'Show recommendations'
					}
				]
			},
			{
				id: 'voyage-recommendations',
				text: voyageRecommendations,
				suggestions: [
					{
						id: '1',
						refResponse: 'voyage-summary',
						text: 'Back to voyage summary'
					},
					{
						id: '2',
						refResponse: 'voyage-statistics',
						text: 'Show voyage statistics'
					}
				]
			}
		],
		snippets: [
			{
				id: 'vessel-details',
				name: 'Vessel Details',
				content: `**Vessel details**:
- IMO: ${getRandomInt(9000000, 9999999)}
- Flag: ${getRandomItem(['Panama', 'Liberia', 'Marshall Islands', 'Singapore', 'Malta'])}
- Year built: ${getRandomInt(2000, 2020)}
- DWT: ${getRandomInt(50000, 150000)} MT`
			},
			{
				id: 'cargo-details',
				name: 'Cargo Details',
				content: `**Cargo details**:
- Type: ${getRandomItem(cargoTypes)}
- Quantity: ${getRandomInt(30000, 100000)} MT
- Stowage factor: ${(getRandomInt(40, 60) / 100).toFixed(2)} m3/MT`
			}
		],
		signature: {
			content: `Best regards,
Voyage Management System
Abraxa Maritime`
		},
		email: {
			templates: [
				{
					id: 'voyage-update',
					name: 'Voyage Update',
					subject: 'Voyage Update - Current Status and ETA',
					content: `Dear Operations Team,

This is an automated update regarding our current voyage.

Vessel: ${getRandomItem(vesselNames)}
Current Position: Lat ${getRandomInt(0, 90)}°${getRandomInt(0, 1) ? 'N' : 'S'}, Long ${getRandomInt(0, 180)}°${getRandomInt(0, 1) ? 'E' : 'W'}
ETA at ${getRandomItem(portNames)}: ${getRandomInt(1, 30)} ${getRandomItem(['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'])} ${new Date().getFullYear()}

Please let me know if you require any additional information.

Best regards,
Captain`
				}
			]
		}
	};
};
