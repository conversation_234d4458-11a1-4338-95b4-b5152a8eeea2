import React, { createContext, useContext, useReducer, ReactNode, useCallback } from 'react';
import { data as defaultData } from '../data/data';

// Types
export interface ChatResponse {
	id: string;
	text: string;
	type?: string;
	issues?: {
		id: string;
		refResponse: string;
		title: string;
		type: string;
		amount?: string;
		excerpt?: string;
		text?: string;
	}[];
	tasks?: {
		id: string;
		refResponse: string;
		text: string;
		status: string;
	}[];
	suggestions?: {
		id: string;
		refResponse: string;
		text: string;
	}[];
}

export interface ChatData {
	responses: ChatResponse[];
	snippets?: {
		id: string;
		name: string;
		content: string;
	}[];
	signature?: {
		content: string;
	};
	email?: {
		templates: {
			id: string;
			name: string;
			subject: string;
			content: string;
		}[];
	};
}

interface ChatState {
	data: ChatData;
	showInitialResponse: boolean;
	visibleResponses: string[];
	loadingResponses: string[];
	chatId?: string;
	chatTitle?: string;
}

type ChatAction =
	| { type: 'SET_DATA'; payload: ChatData }
	| { type: 'SET_INITIAL_RESPONSE'; payload: boolean }
	| { type: 'ADD_VISIBLE_RESPONSE'; payload: string }
	| { type: 'ADD_LOADING_RESPONSE'; payload: string }
	| { type: 'REMOVE_LOADING_RESPONSE'; payload: string }
	| { type: 'SET_CHAT_ID'; payload: string }
	| { type: 'SET_CHAT_TITLE'; payload: string };

interface ChatContextType {
	state: ChatState;
	dispatch: React.Dispatch<ChatAction>;
	getResponseById: (id: string) => ChatResponse | undefined;
	handleSuggestionClick: (refResponse: string) => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

function chatReducer(state: ChatState, action: ChatAction): ChatState {
	switch (action.type) {
		case 'SET_DATA':
			return {
				...state,
				data: action.payload
			};
		case 'SET_INITIAL_RESPONSE':
			return {
				...state,
				showInitialResponse: action.payload
			};
		case 'ADD_VISIBLE_RESPONSE':
			return {
				...state,
				visibleResponses: [...state.visibleResponses, action.payload]
			};
		case 'ADD_LOADING_RESPONSE':
			return {
				...state,
				loadingResponses: [...state.loadingResponses, action.payload]
			};
		case 'REMOVE_LOADING_RESPONSE':
			return {
				...state,
				loadingResponses: state.loadingResponses.filter(id => id !== action.payload)
			};
		case 'SET_CHAT_ID':
			return {
				...state,
				chatId: action.payload
			};
		case 'SET_CHAT_TITLE':
			return {
				...state,
				chatTitle: action.payload
			};
		default:
			return state;
	}
}

interface ChatProviderProps {
	children: ReactNode;
	initialData?: ChatData;
	chatId?: string;
	chatTitle?: string;
}

export function ChatProvider({ children, initialData = defaultData, chatId, chatTitle }: ChatProviderProps) {
	const initialState: ChatState = {
		data: initialData,
		showInitialResponse: false,
		visibleResponses: ['1'],
		loadingResponses: [],
		chatId,
		chatTitle
	};

	const [state, dispatch] = useReducer(chatReducer, initialState);

	const getResponseById = useCallback(
		(id: string) => state.data.responses.find(response => response.id === id),
		[state.data.responses]
	);

	const scrollToElement = useCallback((elementId: string, block: ScrollLogicalPosition = 'start') => {
		setTimeout(() => {
			const element = document.getElementById(elementId);
			if (element) {
				element.scrollIntoView({ behavior: 'smooth', block });
			}
		}, 100);
	}, []);

	const handleSuggestionClick = useCallback(
		(refResponse: string) => {
			dispatch({ type: 'ADD_LOADING_RESPONSE', payload: refResponse });
			scrollToElement('anchor-end', 'end');

			// Simulate API delay
			setTimeout(() => {
				dispatch({ type: 'REMOVE_LOADING_RESPONSE', payload: refResponse });
				dispatch({ type: 'ADD_VISIBLE_RESPONSE', payload: refResponse });
				scrollToElement(`response-${refResponse}`);
			}, 1500);
		},
		[scrollToElement]
	);

	// Set initial response after a delay
	React.useEffect(() => {
		const timer = setTimeout(() => {
			dispatch({ type: 'SET_INITIAL_RESPONSE', payload: true });
		}, 1000);

		return () => clearTimeout(timer);
	}, []);

	// Set chat ID and title if provided
	React.useEffect(() => {
		if (chatId) {
			dispatch({ type: 'SET_CHAT_ID', payload: chatId });
		}
		if (chatTitle) {
			dispatch({ type: 'SET_CHAT_TITLE', payload: chatTitle });
		}
	}, [chatId, chatTitle]);

	return (
		<ChatContext.Provider value={{ state, dispatch, getResponseById, handleSuggestionClick }}>
			{children}
		</ChatContext.Provider>
	);
}

export function useChatContext() {
	const context = useContext(ChatContext);
	if (context === undefined) {
		throw new Error('useChatContext must be used within a ChatProvider');
	}
	return context;
}
