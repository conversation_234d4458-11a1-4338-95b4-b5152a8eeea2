import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>t, <PERSON><PERSON><PERSON>, CheckCheck, Repeat2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

interface Issue {
	id: string;
	refResponse: string;
	title: string;
	type: string;
	amount?: string;
	excerpt?: string;
	text?: string;
}

interface ChatIssueProps {
	issues: Issue[];
	onIssueClick: (refResponse: string) => void;
	onOpenRefPanel?: () => void;
}

export default function ChatIssues({ issues, onIssueClick, onOpenRefPanel }: ChatIssueProps) {
	return (
		<div className="grid gap-4">
			{issues.map(item => (
				<div key={item.id}>
					<Button
						variant="outline"
						className="group/issue bg-accent/50 h-auto w-full flex-col overflow-hidden px-2"
						onClick={() => {
							onIssueClick(item.refResponse);
							onOpenRefPanel?.();
						}}
					>
						<div className="flex w-full items-center gap-2">
							{item.type === 'warning' && <CircleAlert className="text-amber-500" />}
							{item.type === 'alert' && <TriangleAlert className="text-red-500" />}
							{item.type === 'checked' && <CheckCheck className="text-emerald-500" />}
							{item.type === 'recoverable' && <Repeat2 className="text-emerald-500" />}
							<div className="flex-1 text-left">{item.title}</div>
							{item.amount && <div>${item.amount}</div>}
							<ChevronRight className="text-muted-foreground opacity-50 transition-opacity group-hover/issue:opacity-100" />
						</div>
						{item.excerpt && (
							<>
								<Separator />
								<div className="text-muted-foreground w-full text-left text-xs font-normal text-wrap">
									{item.excerpt}
								</div>
							</>
						)}
					</Button>
					{item.text && (
						<div className="ai-markdown text-md/6 pt-4 pb-2">
							<ReactMarkdown remarkPlugins={[remarkGfm]}>{item.text}</ReactMarkdown>
						</div>
					)}
				</div>
			))}
		</div>
	);
}
