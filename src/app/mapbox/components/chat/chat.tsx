import { ChatProvider, ChatData } from './context/chat-context';
import ChatContainer from './chat-container';
import { data as defaultData } from './data/data';

interface Props {
	onOpenVoyagePanel?: () => void;
	onExpandPanel?: () => void;
	onOpenRefPanel?: () => void;
	chatHeader?: boolean;
	data?: ChatData;
	chatId?: string;
	chatTitle?: string;
	backButton?: boolean;
	collapseButton?: boolean;
	expandButton?: boolean;
}

export default function Chat({
	onOpenVoyagePanel,
	onExpandPanel,
	onOpenRefPanel,
	data = defaultData,
	chatId,
	chatHeader = true,
	chatTitle,
	backButton,
	collapseButton,
	expandButton
}: Props) {
	return (
		<ChatProvider initialData={data} chatId={chatId} chatTitle={chatTitle}>
			<ChatContainer
				chatId={chatId}
				chatHeader={chatHeader}
				chatTitle={chatTitle}
				backButton={backButton}
				collapseButton={collapseButton}
				expandButton={expandButton}
				onExpandPanel={onExpandPanel}
				onOpenVoyagePanel={onOpenVoyagePanel}
				onOpenRefPanel={onOpenRefPanel}
			/>
		</ChatProvider>
	);
}
