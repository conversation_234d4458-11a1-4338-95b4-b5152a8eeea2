import { data } from '../data/data';
import { Table, TableRow, TableBody, TableCell } from '@/components/ui/table';

export default function WidgetWeather() {
	const weather = data.weather;

	return (
		<Table className="text-sm">
			<TableBody>
				{weather.map(day => (
					<TableRow key={day.date}>
						<TableCell className="text-muted-foreground pl-0">{day.date}</TableCell>
						<TableCell>
							<div className="flex items-center gap-2">
								<span className="text-xl">{day.icon}</span> <span>{day.forecast}</span>
							</div>
						</TableCell>
						<TableCell>{day.high}</TableCell>
						<TableCell className="text-muted-foreground">{day.low}</TableCell>
					</TableRow>
				))}
			</TableBody>
		</Table>
	);
}
