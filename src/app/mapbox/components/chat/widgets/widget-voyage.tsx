import { ArrowR<PERSON>, Box, Circle } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

export default function WidgetVoyage() {
	return (
		<div className="bg-panel grid gap-2 rounded-xl border p-4">
			<div className="flex w-full items-center gap-2 pb-3">
				<div className="text-foreground flex-1 px-2 text-base font-semibold">mv Suez Navigator</div>
				<Badge variant="secondary" className="rounded-full text-xs">
					<Box className="text-primary size-3" />
					<span className="text-muted-foreground">35,000mts - Iron Ore</span>
				</Badge>
			</div>
			<div className="flex w-full items-center gap-2 font-normal">
				<Button variant="outline" className="h-auto rounded-full bg-transparent px-2 py-1 text-xs">
					<Circle className="text-muted-foreground size-3" />
					<span>Rotterdam, NL</span>
				</Button>
				<Separator className="flex-1" />
				<ArrowRight className="text-muted-foreground size-3" />
				<Separator className="flex-1" />
				<Button variant="outline" className="h-auto rounded-full bg-transparent px-2 py-1 text-xs">
					<Circle className="text-muted-foreground size-3" />
					<span>Port Said, EG</span>
				</Button>
			</div>
			<div className="text-muted-foreground flex w-full items-center gap-2 text-xs">
				<div className="flex-1 px-2">28 Feb, 12:00</div>
				<div className="flex-1 px-2 text-right">17 Mar, 08:00</div>
			</div>
		</div>
	);
}
