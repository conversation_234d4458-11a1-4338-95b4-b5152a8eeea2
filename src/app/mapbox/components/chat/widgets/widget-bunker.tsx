import { TrendingUp } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const data = {
	bunkers: [
		{
			fuel: 'IFO',
			last24h: '2,056',
			today: '2,490',
			variance: '434',
			unit: 'mt'
		},
		{
			fuel: 'HSFO',
			last24h: '325.3',
			today: '384.7',
			variance: '60.4',
			unit: 'mt'
		}
	]
};

export default function WidgetBunker() {
	return (
		<Table className="text-sm">
			<TableHeader>
				<TableRow>
					<TableHead className="pl-0">Fuel</TableHead>
					<TableHead className="text-right">Last 24h</TableHead>
					<TableHead className="text-right">Today</TableHead>
					<TableHead className="text-right">Variance</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{data.bunkers.map(bunker => (
					<TableRow key={bunker.fuel}>
						<TableCell className="pl-0">{bunker.fuel}</TableCell>
						<TableCell className="text-right">
							{bunker.last24h} {bunker.unit}
						</TableCell>
						<TableCell className="text-right">
							{bunker.today} {bunker.unit}
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-2">
								<TrendingUp className="size-4 text-red-400" />
								<span className="text-red-400">
									{bunker.variance} {bunker.unit}
								</span>
							</div>
						</TableCell>
					</TableRow>
				))}
			</TableBody>
		</Table>
	);
}
