import { useRef, useState } from 'react';
import { toast } from 'sonner';
import { Files, Loader2, Upload, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useFileUpload } from '@/hooks/signedUrl';
import { Badge } from '@/components/ui/badge';

export function WidgetUpload({
	title,
	onUpload,
	onUploadStateChange
}: {
	title: string;
	onUpload: (file: File, fileData: { url: string; originalName: string; uuid: string }) => void;
	onUploadStateChange: (isUploading: boolean) => void;
}) {
	const fileInputRef = useRef<HTMLInputElement | null>(null);
	const [file, setFile] = useState<File | null>(null);
	const [uploading, setUploading] = useState(false);
	const { uploadFile } = useFileUpload();

	const handleButtonClick = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.files && event.target.files.length > 0) {
			const selectedFile = event.target.files[0];

			setFile(selectedFile);
			setUploading(true);
			onUploadStateChange(true);

			uploadFile(selectedFile)
				.then(fileData => {
					onUpload(selectedFile, fileData);
					toast('Uploaded successfully', {
						duration: 3000
					});
				})
				.catch(error => {
					setFile(null);
					console.error('Upload failed:', error);
				})
				.finally(() => {
					setUploading(false);
					onUploadStateChange(false);
				});
		}
	};

	return (
		<div className="bg-panel rounded-xl border border-dashed p-4">
			<Input
				ref={fileInputRef}
				type="file"
				accept=".pdf,.xlsx,.docx"
				className="hidden"
				onChange={handleFileChange}
			/>
			{file && !uploading ? (
				<div className="flex items-center gap-3">
					<Badge variant="default" className="rounded-md p-2">
						<Files className="text-foreground size-4" />
					</Badge>
					<div className="flex-1 text-sm">{file.name}</div>
					<Button
						size="icon"
						variant="ghost"
						className="text-muted-foreground h-7 w-7"
						onClick={() => {
							setFile(null);
						}}
					>
						<X />
					</Button>
				</div>
			) : (
				<div className="flex flex-col items-center gap-4">
					<div className="text-muted-foreground text-sm">{title}</div>
					<Button size="sm" variant="secondary" className="text-muted-foreground" onClick={handleButtonClick}>
						{uploading ? (
							<>
								<Loader2 className="animate-spin" /> <span>Uploading...</span>
							</>
						) : (
							<>
								<Upload />
								<span>Upload</span>
							</>
						)}
					</Button>
				</div>
			)}
		</div>
	);
}
