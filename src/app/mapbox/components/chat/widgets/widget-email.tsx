import { <PERSON><PERSON>, <PERSON>c<PERSON>, Plus } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { useState } from 'react';
import { data } from '../data/data';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export default function WidgetEmail() {
	const [selectedTemplate, setSelectedTemplate] = useState(data.email.templates[0]);
	const [emailContent, setEmailContent] = useState(data.email.templates[0].content);
	const [signature, _] = useState(data.email.signature.content);

	const handleTemplateChange = (templateId: string) => {
		const template = data.email.templates.find(t => t.id === templateId);
		if (template) {
			setSelectedTemplate(template);
			setEmailContent(template.content);
		}
	};

	const handleAddSnippet = (snippetId: string) => {
		const snippet = data.email.snippets.find(s => s.id === snippetId);
		if (snippet) {
			setEmailContent(prev => `${prev}\n\n${snippet.content}`);
		}
	};

	return (
		<div className="bg-panel rounded-md border text-sm leading-6">
			<div className="flex items-center justify-between border-b px-2 py-2">
				<Select defaultValue={selectedTemplate.id} onValueChange={handleTemplateChange}>
					<SelectTrigger className="bg-accent h-auto w-[180px] border-none py-1.5">
						<SelectValue placeholder="Template" />
					</SelectTrigger>
					<SelectContent>
						{data.email.templates.map(template => (
							<SelectItem key={template.id} value={template.id}>
								{template.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<div className="flex items-center gap-1">
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="text-muted-foreground h-7 w-7"
									onClick={() => navigator.clipboard.writeText(emailContent)}
								>
									<Copy />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Copy</TooltipContent>
						</Tooltip>
						<DropdownMenu>
							<DropdownMenuTrigger>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
											<Plus />
										</Button>
									</TooltipTrigger>
									<TooltipContent>Add Snippet</TooltipContent>
								</Tooltip>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								{data.email.snippets.map(snippet => (
									<DropdownMenuItem key={snippet.id} onClick={() => handleAddSnippet(snippet.id)}>
										{snippet.name}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</TooltipProvider>
				</div>
			</div>
			<div className="ai-markdown px-4 py-2" contentEditable>
				<ReactMarkdown>{emailContent}</ReactMarkdown>
				<ReactMarkdown>{signature}</ReactMarkdown>
			</div>
			<div className="flex items-center justify-between border-t p-2">
				<TooltipProvider delayDuration={100}>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
								<Paperclip />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Attach</TooltipContent>
					</Tooltip>
				</TooltipProvider>

				<Button size="sm">Send</Button>
			</div>
		</div>
	);
}
