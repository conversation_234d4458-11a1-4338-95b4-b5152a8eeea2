import * as React from 'react';
import { useState } from 'react';
import { Anchor, Box, Calendar, Ship, Text } from 'lucide-react';
import { WidgetUpload } from './widget-upload';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Skeleton } from '@/components/ui/skeleton';

const data = {
	timeCharterer: [
		{
			id: '1',
			label: 'Disponent Owners',
			value: 'Sea Stallion Pool Inc. Majuro, The Marshall Islands'
		},
		{
			id: '2',
			label: 'Delivery place',
			value: 'AFSPS TOCOPILLA, CHILE ATDNSSHINC'
		},
		{
			id: '3',
			label: 'Laydays',
			value: 'ATDNSHINC 30 NOV 2015 00:01HRS LOCAL TIME'
		},
		{
			id: '4',
			label: 'Cancelling',
			value: 'ATDNSHINC 5 DECEMBER 2015 23:59 HRS LOCAL TIME'
		},
		{
			id: '5',
			label: 'CP type',
			value: 'ONE TCT VIA CHILE TO SOUTH AFRICA, TRADING ALWAYS VIA GSP/S GSB/S GSA/S ALWAYS AFLOAT ALWAYS W/I INL'
		},
		{
			id: '6',
			label: 'Cargo',
			value: ' ONLY CARGOES ALLOWED ARE CHILEAN NITRATES IN BULK AND BIG BAGS AS PER CHRTS CARGO SPECS'
		},
		{
			id: '7',
			label: 'Redelivery',
			value: 'AFSPS TOCOPILLA, CHILE ATDNSSHINC'
		},
		{
			id: '8',
			label: 'Hire',
			value: 'USD 7000 PER DAY PRORATA INCLOT. PAYABLE 15 DAYS IN ADVANCE'
		},
		{
			id: '9',
			label: 'Hire payment terms',
			value: 'FIRST 15 DAYS HIRE PAYABLE W/I 3 BANKING DAYS AFTER VESSELS DELIVERY TOGETHER WITH VALUE OF'
		},
		{
			id: '10',
			label: 'Bunker Clause',
			value: 'Clause 1'
		},
		{
			id: '11',
			label: 'Delivery messages',
			value: '10d Approximate + 7,5,3,2,1 days  Deffinite notices'
		}
	]
};

export default function WidgetForm() {
	const [, setSelectedItem] = React.useState('');
	const [file, setFile] = React.useState<{ url: string; name: string; uuid: string } | null>(null);
	const [uploading, setUploading] = useState(false);

	React.useEffect(() => {
		if (!uploading && file) {
			const element = document.getElementById('anchor-start');
			if (element) {
				element.scrollIntoView({ behavior: 'smooth', block: 'start' });
			}
		}
	}, [uploading, file]);

	return (
		<div className="grid gap-4" id="anchor-start">
			<WidgetUpload
				title="Drag & Drop C/P terms"
				onUpload={(_, fileData) =>
					setFile({ url: fileData.url, name: fileData.originalName, uuid: fileData.uuid })
				}
				onUploadStateChange={setUploading}
			/>
			{uploading ? (
				<div className="grid gap-2 py-1">
					<Skeleton className="h-2 w-2/3" />
					<Skeleton className="h-2 w-1/2" />
					<Skeleton className="h-2 w-1/3" />
				</div>
			) : file ? (
				<div className="grid gap-4">
					<div className="pt-4 pb-2">
						The uploaded document provides a detailed agreement outlining the charter party terms for{' '}
						<strong>EuroChem Agro GmbH.</strong>
					</div>
					<div className="bg-panel grid grid-cols-[130px_minmax(0px,1fr)] items-center gap-x-4 gap-y-2 rounded-xl border p-4 text-sm">
						<div className="text-muted-foreground text-sm">Vessel</div>
						<div className="flex">
							<ComboboxButtonInline
								data={[]}
								placeholder="mv Suez Navigator"
								icon={Ship}
								className="flex-1 font-normal"
								onSelect={value => setSelectedItem(value)}
							/>
						</div>
						<div className="text-muted-foreground text-sm">Load port</div>
						<div className="flex">
							<ComboboxButtonInline
								data={[]}
								placeholder="Rotterdam, NL"
								icon={Anchor}
								className="flex-1 font-normal"
								onSelect={value => setSelectedItem(value)}
							/>
						</div>
						<div className="text-muted-foreground text-sm">ETA</div>
						<div className="flex">
							<ComboboxButtonInline
								data={[]}
								placeholder="28 Feb, 12:00"
								icon={Calendar}
								className="flex-1 font-normal"
								onSelect={value => setSelectedItem(value)}
							/>
						</div>
						<div className="text-muted-foreground text-sm">Discharge port</div>
						<div className="flex">
							<ComboboxButtonInline
								data={[]}
								placeholder="Port Said, EG"
								icon={Anchor}
								className="flex-1 font-normal"
								onSelect={value => setSelectedItem(value)}
							/>
						</div>
						<div className="text-muted-foreground text-sm">ETA</div>
						<div className="flex">
							<ComboboxButtonInline
								data={[]}
								placeholder="17 Mar, 08:00"
								icon={Calendar}
								className="flex-1 font-normal"
								onSelect={value => setSelectedItem(value)}
							/>
						</div>
						<div className="text-muted-foreground text-sm">Cargo</div>
						<div className="flex">
							<ComboboxButtonInline
								data={[]}
								placeholder="35,000mts - Iron Ore"
								icon={Box}
								className="flex-1 font-normal"
								onSelect={value => setSelectedItem(value)}
							/>
						</div>
					</div>
					<div className="bg-panel grid grid-cols-[130px_minmax(0px,1fr)] items-center gap-x-4 gap-y-2 rounded-xl border p-4 text-sm">
						{data.timeCharterer.map(item => (
							<>
								<div className="text-muted-foreground text-sm">{item.label}</div>
								<div className="flex">
									<ComboboxButtonInline
										data={[...data.timeCharterer]}
										placeholder={item.label}
										icon={Text}
										className="flex-1 font-normal"
										onSelect={value => setSelectedItem(value)}
									/>
								</div>
							</>
						))}
					</div>
				</div>
			) : null}
		</div>
	);
}
