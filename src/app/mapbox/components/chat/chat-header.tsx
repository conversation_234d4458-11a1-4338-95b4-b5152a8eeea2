import { Maximize2, MoveLeft, PanelRight } from 'lucide-react';
import { useChatContext } from './context/chat-context';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface Props {
	onOpenVoyagePanel?: () => void;
	onExpandPanel?: () => void;
	chatId?: string;
	chatTitle?: string;
	backButton?: boolean;
	collapseButton?: boolean;
	expandButton?: boolean;
}

export default function ChatHeader({
	onOpenVoyagePanel,
	onExpandPanel,
	chatTitle,
	backButton = true,
	collapseButton = false,
	expandButton = false
}: Props) {
	const { state } = useChatContext();

	// Use provided props or fallback to context values
	//const displayChatId = chatId || state.chatId || 'VOY-12345';
	const displayChatTitle = chatTitle || state.chatTitle || 'Chat';

	return (
		<div className="flex h-16 items-center gap-3 p-4">
			{backButton && (
				<Button
					variant="ghost"
					size="icon"
					className="text-muted-foreground size-8 self-start"
					onClick={() => {
						if (onOpenVoyagePanel) {
							onOpenVoyagePanel();
						}
					}}
				>
					<MoveLeft className="h-4 w-4" />
				</Button>
			)}
			<div className="flex-1 px-2 leading-none font-semibold">{displayChatTitle}</div>
			<div className="flex items-center gap-2">
				<TooltipProvider delayDuration={100}>
					{expandButton && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="text-muted-foreground h-7 w-7"
									onClick={() => {
										if (onExpandPanel) {
											onExpandPanel();
										}
									}}
								>
									<Maximize2 />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Expand</TooltipContent>
						</Tooltip>
					)}
					{collapseButton && (
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="text-muted-foreground h-7 w-7"
									onClick={() => {
										if (onOpenVoyagePanel) {
											onOpenVoyagePanel();
										}
									}}
								>
									<PanelRight />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Collapse</TooltipContent>
						</Tooltip>
					)}
				</TooltipProvider>
			</div>
		</div>
	);
}
