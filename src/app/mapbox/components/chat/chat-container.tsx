import { useMemo } from 'react';
import { Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import ChatLoader from './chat-loader';
import ChatSuggestions from './chat-suggestions';
import ChatTasks from './chat-tasks';
import ChatIssues from './chat-issues';
import ChatHeader from './chat-header';
import WidgetWeather from './widgets/widget-weather';
import WidgetBunker from './widgets/widget-bunker';
import WidgetEmail from './widgets/widget-email';
import WidgetVoyage from './widgets/widget-voyage';
import WidgetForm from './widgets/widget-form';
import { useChat } from './hooks/use-chat';
import ChatInitialSuggestions from './chat-initial-suggestions';
import ChatbotMin from '@/components/chatbot/chatbot-min';
import { Separator } from '@/components/ui/separator';

interface Props {
	onOpenVoyagePanel?: () => void;
	onExpandPanel?: () => void;
	onOpenRefPanel?: () => void;
	chatHeader?: boolean;
	chatId?: string;
	chatTitle?: string;
	backButton?: boolean;
	collapseButton?: boolean;
	expandButton?: boolean;
}

// Motion config
const motionConfig = {
	initial: { height: 0, overflow: 'hidden' },
	animate: { height: 'auto', overflow: 'visible' },
	exit: { height: 0, overflow: 'hidden' },
	transition: { duration: 0.15 }
};

// Widget component
const Widget = {
	weather: WidgetWeather,
	bunker: WidgetBunker,
	email: WidgetEmail,
	form: WidgetForm,
	voyage: WidgetVoyage
};

export default function ChatContainer({
	onOpenVoyagePanel,
	onExpandPanel,
	onOpenRefPanel,
	chatHeader,
	chatId,
	chatTitle,
	backButton,
	collapseButton,
	expandButton
}: Props) {
	const { showInitialResponse, visibleResponses, loadingResponses, getResponseById, handleSuggestionClick } = useChat(
		{ onOpenVoyagePanel }
	);

	// Render the initial response
	const initialResponseContent = useMemo(() => {
		if (!showInitialResponse)
			return (
				<div className="flex w-full items-center justify-center">
					<ChatLoader />
				</div>
			);

		const initialResponse = getResponseById('1');

		if (!initialResponse) return null;

		return (
			<motion.div {...motionConfig} className="grid gap-6">
				{initialResponse.type === 'greeting' ? (
					<>
						<div className="grid gap-4 text-center">
							<ReactMarkdown remarkPlugins={[remarkGfm]}>{initialResponse.text}</ReactMarkdown>
						</div>
						{initialResponse.issues && initialResponse.issues.length > 0 && (
							<ChatIssues
								issues={initialResponse.issues}
								onIssueClick={handleSuggestionClick}
								onOpenRefPanel={onOpenRefPanel}
							/>
						)}
						{initialResponse.suggestions && initialResponse.suggestions.length > 0 && (
							<ChatInitialSuggestions
								suggestions={initialResponse.suggestions}
								onSuggestionClick={handleSuggestionClick}
							/>
						)}
					</>
				) : (
					<>
						<div className="grid gap-4">
							<ReactMarkdown remarkPlugins={[remarkGfm]}>{initialResponse.text}</ReactMarkdown>
						</div>
						{initialResponse.issues && initialResponse.issues.length > 0 && (
							<ChatIssues
								issues={initialResponse.issues}
								onIssueClick={handleSuggestionClick}
								onOpenRefPanel={onOpenRefPanel}
							/>
						)}
						{initialResponse.suggestions && initialResponse.suggestions.length > 0 && (
							<ChatSuggestions
								suggestions={initialResponse.suggestions}
								onSuggestionClick={handleSuggestionClick}
							/>
						)}
					</>
				)}
			</motion.div>
		);
	}, [showInitialResponse, handleSuggestionClick, onOpenRefPanel, getResponseById]);

	return (
		<div className="chat flex h-full flex-1 flex-col">
			{chatHeader && (
				<ChatHeader
					chatId={chatId}
					chatTitle={chatTitle}
					backButton={backButton}
					collapseButton={collapseButton}
					expandButton={expandButton}
					onExpandPanel={onExpandPanel}
					onOpenVoyagePanel={onOpenVoyagePanel}
				/>
			)}
			<div className="flex flex-1 flex-col items-center overflow-y-auto px-6">
				<div className="relative grid w-full max-w-3xl flex-1 gap-6">
					{/* Initial Response */}
					<div className="flex w-full flex-col items-start gap-4 self-center pt-6">
						<div className="flex w-full flex-col items-center justify-center gap-4">
							<Sparkles className="text-primary size-8" />
							<h2 className="text-xl font-semibold">Operator One</h2>
						</div>
						<div className="ai-markdown text-md/6 py-0.5">{initialResponseContent}</div>
					</div>

					{/* Visible Responses */}
					{visibleResponses.slice(1).map(responseId => {
						const response = getResponseById(responseId);
						if (!response) return null;

						// Get the prompt text from suggestions, issues, or tasks from any response
						const findPromptText = () => {
							// Search through all visible responses
							for (const visibleId of visibleResponses) {
								const resp = getResponseById(visibleId);
								if (!resp) continue;

								// Check suggestions
								const suggestionText = resp.suggestions?.find(
									suggestion => suggestion.refResponse === responseId
								)?.text;
								if (suggestionText) return suggestionText;

								// Check issues
								const issueTitle = resp.issues?.find(issue => issue.refResponse === responseId)?.title;
								if (issueTitle) return issueTitle;

								// Check tasks
								const taskText = resp.tasks?.find(task => task.refResponse === responseId)?.text;
								if (taskText) return taskText;
							}
							return undefined;
						};

						const promptText = findPromptText();

						// Dynamically select the widget component based on response type
						const WidgetComponent = response.type ? Widget[response.type as keyof typeof Widget] : null;

						return (
							<div key={`response-${responseId}`} className="grid gap-6" id={`response-${responseId}`}>
								<Separator className="bg-border/50" />
								{promptText && (
									<div className="w-full py-4">
										<div className="bg-secondary justify-self-end rounded-lg px-4 py-2">
											<div className="text-sm">{promptText}</div>
										</div>
									</div>
								)}
								<div className="flex w-full items-start gap-4">
									<div className="bg-primary/20 hidden rounded-full p-2">
										<Sparkles className="text-primary size-4" />
									</div>
									<div className="ai-markdown text-md/6 py-0.5">
										<motion.div {...motionConfig} className="grid gap-6">
											<div className="grid gap-4">
												<ReactMarkdown remarkPlugins={[remarkGfm]}>
													{response.text}
												</ReactMarkdown>
												{WidgetComponent && <WidgetComponent />}
											</div>
											{response.issues && response.issues.length > 0 && (
												<ChatIssues
													issues={response.issues}
													onIssueClick={handleSuggestionClick}
													onOpenRefPanel={onOpenRefPanel}
												/>
											)}
											{response.tasks && response.tasks.length > 0 && (
												<ChatTasks
													tasks={response.tasks.map(task => ({
														id: task.id,
														refResponse: task.refResponse,
														text: task.text,
														status: task.status
													}))}
													onTaskClick={handleSuggestionClick}
												/>
											)}
											{response.suggestions && response.suggestions.length > 0 && (
												<ChatSuggestions
													suggestions={response.suggestions}
													onSuggestionClick={handleSuggestionClick}
												/>
											)}
										</motion.div>
									</div>
								</div>
							</div>
						);
					})}

					{/* Loading Responses */}
					{loadingResponses.map(responseId => (
						<div key={`loading-${responseId}`} className="py-4">
							<motion.div {...motionConfig} className="grid gap-6">
								<Separator className="bg-border/50" />
								<div className="flex w-full items-start gap-4">
									<div className="bg-primary/20 hidden rounded-full p-2">
										<Sparkles className="text-primary size-4" />
									</div>
									<div className="ai-markdown text-md/6 py-0.5">
										<ChatLoader />
									</div>
								</div>
							</motion.div>
						</div>
					))}
				</div>
				<div id="anchor-end" />
			</div>
			<ChatbotMin />
		</div>
	);
}
