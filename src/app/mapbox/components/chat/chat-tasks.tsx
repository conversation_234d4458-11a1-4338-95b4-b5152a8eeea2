import { Circle, CircleCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Task {
	id: string;
	refResponse: string;
	text: string;
	status: string;
}

interface ChatTaskProps {
	tasks: Task[];
	onTaskClick: (refResponse: string) => void;
}

export default function ChatTasks({ tasks, onTaskClick }: ChatTaskProps) {
	return (
		<div className="grid gap-1 pb-0">
			<div className="py-2 text-xs font-semibold">Next Tasks</div>
			<div className="grid">
				{tasks.map(item => (
					<Button
						key={item.id}
						variant="ghost"
						className="group text-muted-foreground h-auto justify-start bg-transparent px-2"
						onClick={() => onTaskClick(item.refResponse)}
					>
						{item.status === 'done' ? (
							<CircleCheck className="text-background fill-primary" />
						) : (
							<Circle className="text-muted-foreground" />
						)}
						<div
							className={cn(
								`flex-1 text-left font-normal text-wrap`,
								item.status === 'done' ? 'line-through' : ''
							)}
						>
							{item.text}
						</div>
					</Button>
				))}
			</div>
		</div>
	);
}
