import { useState } from 'react';
import Chat from '../chat';
import { ChatData } from '../context/chat-context';
import { data as defaultData } from '../data/data';
import { Button } from '@/components/ui/button';

// Example of custom chat data
const customChatData: ChatData = {
	responses: [
		{
			id: '1',
			text: 'Hello! This is a **custom chat** example. You can customize this message and all the responses.',
			type: 'greeting',
			suggestions: [
				{
					id: '1',
					refResponse: 'custom-response-1',
					text: 'Show me custom response 1'
				},
				{
					id: '2',
					refResponse: 'custom-response-2',
					text: 'Show me custom response 2'
				}
			]
		},
		{
			id: 'custom-response-1',
			text: 'This is custom response 1. You can add any content here, including markdown formatting, links, etc.',
			type: 'voyage',
			suggestions: [
				{
					id: '1',
					refResponse: 'custom-response-2',
					text: 'Show me custom response 2'
				}
			]
		},
		{
			id: 'custom-response-2',
			text: 'This is custom response 2. You can also include tasks that the user can complete.',
			tasks: [
				{
					id: '1',
					refResponse: 'task-completed',
					text: 'Complete this example task',
					status: 'todo'
				}
			]
		},
		{
			id: 'task-completed',
			text: 'Great job! You completed the task. This demonstrates how you can create interactive chat flows.',
			suggestions: [
				{
					id: '1',
					refResponse: 'custom-response-1',
					text: 'Go back to response 1'
				}
			]
		}
	],
	snippets: [
		{
			id: 'example-snippet',
			name: 'Example Snippet',
			content: '**This is an example snippet**\nYou can include any markdown content here.'
		}
	],
	signature: {
		content: 'Best regards,\nCustom Chat Example'
	},
	email: {
		templates: [
			{
				id: 'example-email',
				name: 'Example Email',
				subject: 'Example Email Subject',
				content: 'This is an example email content.'
			}
		]
	}
};

export default function DynamicChatExample() {
	const [showChat, setShowChat] = useState(false);
	const [useCustomData, setUseCustomData] = useState(false);

	return (
		<div className="p-4">
			<h1 className="mb-4 text-2xl font-bold">Dynamic Chat Example</h1>

			<div className="mb-4 flex gap-4">
				<Button onClick={() => setShowChat(!showChat)}>{showChat ? 'Hide Chat' : 'Show Chat'}</Button>

				<Button variant="outline" disabled={!showChat} onClick={() => setUseCustomData(!useCustomData)}>
					{useCustomData ? 'Use Default Data' : 'Use Custom Data'}
				</Button>
			</div>

			{showChat && (
				<div className="h-[600px] overflow-hidden rounded-lg border">
					<Chat
						chatId={useCustomData ? 'CUSTOM-123' : 'VOY-12345'}
						chatTitle={useCustomData ? 'Custom Chat Example' : 'Default Chat'}
						data={useCustomData ? customChatData : defaultData}
						onOpenVoyagePanel={() => setShowChat(false)}
					/>
				</div>
			)}

			<div className="bg-muted mt-4 rounded-lg p-4">
				<h2 className="mb-2 text-lg font-semibold">How to use the Chat component:</h2>
				<pre className="bg-background overflow-auto rounded p-4">
					{`// Import the Chat component
import Chat from '@/app/mapbox/components/chat/chat';

// Use it in your component
<Chat
  chatId="YOUR-CHAT-ID"
  chatTitle="Your Chat Title"
  data={yourCustomData} // Optional - provide custom chat data
  onOpenVoyagePanel={() => {}} // Optional - handle back button click
/>
`}
				</pre>
			</div>
		</div>
	);
}
