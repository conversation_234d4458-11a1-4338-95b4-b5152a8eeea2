import { useCallback } from 'react';
import { useChatContext, ChatData } from '../context/chat-context';

interface UseChatOptions {
	onOpenVoyagePanel?: () => void;
}

export function useChat(options?: UseChatOptions) {
	const { state, dispatch, getResponseById, handleSuggestionClick } = useChatContext();
	const { onOpenVoyagePanel } = options || {};

	const setChatData = useCallback(
		(data: ChatData) => {
			dispatch({ type: 'SET_DATA', payload: data });
		},
		[dispatch]
	);

	const setChatId = useCallback(
		(id: string) => {
			dispatch({ type: 'SET_CHAT_ID', payload: id });
		},
		[dispatch]
	);

	const setChatTitle = useCallback(
		(title: string) => {
			dispatch({ type: 'SET_CHAT_TITLE', payload: title });
		},
		[dispatch]
	);

	return {
		chatData: state.data,
		showInitialResponse: state.showInitialResponse,
		visibleResponses: state.visibleResponses,
		loadingResponses: state.loadingResponses,
		chatId: state.chatId,
		chatTitle: state.chatTitle,
		getResponseById,
		handleSuggestionClick,
		setChatData,
		setChatId,
		setChatTitle,
		onOpenVoyagePanel
	};
}
