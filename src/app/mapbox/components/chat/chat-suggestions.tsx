import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Suggestion {
	id: string;
	refResponse: string;
	text: string;
	icon?: string;
	color?: string;
}

interface ChatSuggestionProps {
	suggestions: Suggestion[];
	onSuggestionClick: (refResponse: string) => void;
}

export default function ChatSuggestions({ suggestions, onSuggestionClick }: ChatSuggestionProps) {
	return (
		<div className="grid gap-2 pb-6">
			<div className="py-2 text-xs font-semibold">Suggestions</div>
			{suggestions.map(item => (
				<div key={item.id}>
					<Button
						variant="outline"
						className="width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
						onClick={() => onSuggestionClick(item.refResponse)}
					>
						{item.icon && React.createElement(item.icon, { className: cn('size-3', item.color) })}
						{item.text}
					</Button>
				</div>
			))}
		</div>
	);
}
