import { useState, useEffect } from 'react';
import Chat from './chat/chat';
import { generateRandomChatData } from './chat/utils/generate-random-data';
import { ChatData } from './chat/context/chat-context';

interface Props {
	onOpenVoyagePanel?: () => void;
	chatId?: string;
	chatTitle?: string;
	customData?: ChatData;
}

export default function MapChatPanel({ onOpenVoyagePanel, chatId, chatTitle, customData }: Props) {
	const [chatData, setChatData] = useState<ChatData | undefined>(customData);

	// Generate random data when the component mounts if no custom data is provided
	useEffect(() => {
		if (!customData) {
			const randomData = generateRandomChatData();
			setChatData(randomData);
		}
	}, [customData]);

	return (
		<div className="bg-background flex h-full flex-col">
			{chatData && (
				<Chat
					chatId={chatId || 'VOY-12345'}
					chatTitle={chatTitle || 'Voyage Summary'}
					data={chatData}
					onOpenVoyagePanel={onOpenVoyagePanel}
				/>
			)}
		</div>
	);
}
