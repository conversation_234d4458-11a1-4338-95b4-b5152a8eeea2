import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const LaytimePage = lazy(() => import('./laytime'));
const LaytimeDetailPage = lazy(() => import('./laytime/laytime-details/page'));
const LaytimeLayout = lazy(() => import('./laytime/laytime-details/layout'));

export default function Admin() {
	return (
		<Routes>
			<Route path="laytime">
				<Route index element={<LaytimePage />} />
				<Route path=":id" element={<LaytimeLayout />}>
					<Route index element={<LaytimeDetailPage />} />
				</Route>
			</Route>
		</Routes>
	);
}
