export type Amount = {
	value: number;
	type: string;
	currency: string;
};

export type Operator = {
	avatar: string;
	firstName: string;
	lastName: string;
};

export type Laytime = {
	id: string;
	uid: string;
	vessel: string;
	port: string;
	charterer: string;
	amount: Amount;
	status: string;
	comments: number;
	date: string;
	operator: Operator;
};

export type Delete = {
	events: string[];
	exclusions: string[];
};
