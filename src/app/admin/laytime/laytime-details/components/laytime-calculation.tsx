/* eslint-disable react-compiler/react-compiler */

import { Plus, Trash } from 'lucide-react';
import { useState } from 'react';
import { useLaytimeContext } from '../../contexts/laytime-context';
import { CalculationRow } from './laytime-calculation-row';
import { Calculation } from '@/hooks/laytime/types';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { LaytimeStatusEnum } from '@/graphql';

export function LaytimeCalculation() {
	const { state, dispatch } = useLaytimeContext();
	const laytime = state.laytimeData;
	const [selectedEvents, setSelectedEvents] = useState<string[]>([]);

	if (!laytime) {
		return null;
	}

	const handleInputChange = (index: number, field: keyof Calculation, value: string | boolean) => {
		dispatch({
			type: 'UPDATE_CALCULATION',
			payload: { index, field, value }
		});
	};

	const handleDateChange = (index: number, date: Date | undefined) => {
		if (!date) return;

		dispatch({
			type: 'UPDATE_CALCULATION_DATE',
			payload: { index, date }
		});
	};

	const handleAddCalculation = () => {
		dispatch({
			type: 'ADD_CALCULATION',
			payload: {
				event: 'Arrival',
				date: new Date().toISOString(),
				remarks: '',
				demurrageStarts: false,
				laytimeStarts: false,
				id: ''
			}
		});
	};

	const handleSelectEvent = (event: string) => {
		setSelectedEvents(prev => [...prev, event]);
	};

	const handleDeselectEvent = (event: string) => {
		setSelectedEvents(prev => prev.filter(e => e !== event));
	};

	const handleDeleteEvents = () => {
		dispatch({ type: 'MARK_EVENTS_FOR_DELETION', payload: selectedEvents });
		dispatch({
			type: 'DELETE_CALCULATIONS',
			payload: selectedEvents
		});
		setSelectedEvents([]);
	};
	const editable =
		laytime.general.status !== LaytimeStatusEnum.PendingApproval.toString() &&
		laytime.general.status !== LaytimeStatusEnum.Submitted.toString() &&
		laytime.general.status !== LaytimeStatusEnum.Approved.toString() &&
		laytime.general.status !== '' &&
		laytime.general.status !== null;

	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Calculation</h3>
			{laytime.calculation.length > 0 && (
				<Table>
					<TableHeader>
						<TableRow>
							{editable && <TableHead className="w-[32px]"></TableHead>}
							<TableHead className="w-[240px]">Event</TableHead>
							<TableHead className="w-[120px]">Date</TableHead>
							<TableHead className="w-[85px]">Time</TableHead>
							<TableHead>Remarks</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{laytime.calculation.map((calculation, index) => (
							<CalculationRow
								key={`${laytime.id}-${calculation.date}}`}
								selectable={editable}
								selectedEvents={selectedEvents}
								calculation={calculation}
								index={index}
								laytimeUid={`${laytime.id}-${calculation.date}}`}
								onInputChange={handleInputChange}
								onDateChange={handleDateChange}
								onSelectEvent={handleSelectEvent}
								onDeselectEvent={handleDeselectEvent}
							/>
						))}
					</TableBody>
				</Table>
			)}
			{editable && (
				<div className="flex flex-row gap-2">
					<Button variant="outline" size="sm" className="w-fit" onClick={handleAddCalculation}>
						<Plus className="h-4 w-4" />
						Add Event
					</Button>
					{selectedEvents.length > 0 && (
						<Button variant="destructive" size="sm" className="w-fit" onClick={handleDeleteEvents}>
							<Trash className="h-4 w-4" />
							Delete {selectedEvents.length} Event{selectedEvents.length > 1 ? 's' : ''}
						</Button>
					)}
				</div>
			)}
		</div>
	);
}
