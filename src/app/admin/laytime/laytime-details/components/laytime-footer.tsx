import { useState } from 'react';
import { useLaytimeContext } from '../../contexts/laytime-context';
import { LaytimeDialogApproval } from './laytime-dialog-approve';
import { LaytimeDialogSubmit } from './laytime-dialog-submit';
import { Button } from '@/components/ui/button';
import { useUpdateLaytime } from '@/hooks/laytime/use-update-laytime';
import { LaytimeStatusEnum } from '@/graphql';

const ActionButtons = () => {
	const {
		state: { laytimeData: laytime, delete: deleteItems }
	} = useLaytimeContext();
	const [openApprove, setOpenApprove] = useState(false);
	const [openSubmit, setOpenSubmit] = useState(false);
	const { updateLaytime, deleteCalculations, deleteExclusions } = useUpdateLaytime();
	const handleSubmitForApproval = () => {
		switch (laytime?.general.status) {
			case LaytimeStatusEnum.Draft:
				setOpenApprove(open => !open);
				break;
			case LaytimeStatusEnum.ChangesRequested:
				setOpenSubmit(open => !open);
				break;
			default:
				break;
		}
	};

	const handleSaveAsDraft = async () => {
		if (laytime) {
			await updateLaytime(laytime);
		}
		if (deleteItems.events.length > 0) {
			await deleteCalculations(deleteItems.events);
		}
		if (deleteItems.exclusions.length > 0) {
			await deleteExclusions(deleteItems.exclusions);
		}
	};

	const disabled =
		laytime?.general.status === LaytimeStatusEnum.PendingApproval ||
		laytime?.general.status === LaytimeStatusEnum.Submitted ||
		laytime?.general.status === LaytimeStatusEnum.Approved ||
		laytime?.general.status === '' ||
		!laytime?.general.status;

	return (
		<>
			<LaytimeDialogApproval open={openApprove} onOpenChange={setOpenApprove} />
			<LaytimeDialogSubmit open={openSubmit} onOpenChange={setOpenSubmit} />
			<>
				{laytime?.general.status === LaytimeStatusEnum.Draft && (
					<Button disabled={disabled} variant="secondary" size="sm" onClick={() => void handleSaveAsDraft()}>
						Save as draft
					</Button>
				)}
				<Button disabled={disabled} size="sm" onClick={handleSubmitForApproval}>
					Submit for approval
				</Button>
			</>
		</>
	);
};

export function LaytimeFooter() {
	return (
		<div className="bg-panel flex w-full flex-col items-center border-t shadow-2xl">
			<div className="w-full max-w-4xl px-4">
				<div className="flex flex-row items-center justify-end gap-2 py-2">
					<ActionButtons />
				</div>
			</div>
		</div>
	);
}
