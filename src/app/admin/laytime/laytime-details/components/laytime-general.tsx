import { useMemo } from 'react';
import { useLaytimeContext } from '../../contexts/laytime-context';
import {
	VoyageIdField,
	PortField,
	OperationField,
	CargoField,
	CounterpartyField,
	CPDateField,
	LaycanField,
	RateQuantityField,
	RateAmountField
} from './laytime-input-fields';
import { LaytimeHeader } from './laytime-header';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { cargoTypes, laytimeTypes, operations } from '@/constants';

export function LaytimeGeneral() {
	const { state, dispatch } = useLaytimeContext();
	const laytime = state.laytimeData;

	const laytimeTypeObj = useMemo(
		() => laytimeTypes.find(l => l.value === laytime?.summary.laytimeType),
		[laytime?.summary.laytimeType]
	);

	if (!laytime) {
		return null; // Or some loading state
	}

	return (
		<>
			<LaytimeHeader />
			<div className="flex flex-col items-center gap-6 lg:flex-row">
				<div className="w-full flex-1">
					<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-2 text-sm">
						<VoyageIdField
							value={laytime.general.id}
							onChange={value => dispatch({ type: 'UPDATE_VOYAGE_ID', payload: value })}
						/>

						<PortField
							value={laytime.general.port.name}
							options={state.ports}
							onChange={value =>
								dispatch({
									type: 'UPDATE_PORT',
									payload: state.ports.find(p => p.name === value) || {
										name: value,
										id: '',
										countryCode: ''
									}
								})
							}
						/>

						<OperationField
							value={laytime.general.operation}
							options={operations}
							onChange={value => dispatch({ type: 'UPDATE_OPERATION', payload: value })}
						/>

						<CargoField
							quantity={laytime.general.cargo.quantity}
							unit={laytime.general.cargo.unit}
							cargoType={laytime.general.cargo.type}
							cargoOptions={cargoTypes}
							onQuantityChange={value => dispatch({ type: 'UPDATE_CARGO_QUANTITY', payload: value })}
							onCargoTypeChange={value => dispatch({ type: 'UPDATE_CARGO_TYPE', payload: value })}
						/>

						<CounterpartyField
							value={laytime.general.party.name}
							options={state.charterers}
							onChange={value =>
								dispatch({
									type: 'UPDATE_COUNTERPARTY',
									payload: state.charterers.find(c => c.name === value) || { name: value, id: '' }
								})
							}
						/>

						<CPDateField
							date={new Date(laytime.general.cpDate)}
							setDate={date => date && dispatch({ type: 'UPDATE_CP_DATE', payload: date })}
						/>
						{laytime.general.laycanFrom || laytime.general.laycanTo ? (
							<LaycanField
								dateRange={{
									from: new Date(laytime.general.laycanFrom),
									to: new Date(laytime.general.laycanTo)
								}}
								setDateRange={range =>
									range &&
									dispatch({
										type: 'UPDATE_LAYCAN_DATE_RANGE',
										payload: range as { from: Date; to: Date }
									})
								}
							/>
						) : null}
					</div>
				</div>
				<div className="w-full flex-1">
					<Card>
						<CardHeader>
							<CardTitle>On {laytime.summary.laytimeType}</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-[160px_minmax(0px,1fr)] gap-y-2 text-sm">
								<RateQuantityField
									value={laytime.summary.rateQty}
									suffix={laytime.summary.rateSuf}
									onChange={value => dispatch({ type: 'UPDATE_RATE_QTY', payload: value })}
								/>

								<RateAmountField
									value={laytime.summary.rateAmount}
									currency={laytime.summary.rateCurrency}
									onChange={value => dispatch({ type: 'UPDATE_RATE_AMOUNT', payload: value })}
								/>

								<div>Time allowed</div>
								<div className="text-right">{laytime.summary.timeAllowed}</div>
								<div></div>
								<Separator orientation="horizontal" />
								<div>Time used</div>
								<div className="text-right">{laytime.summary.timeUsed}</div>
								<div></div>
								<Separator orientation="horizontal" />
								<div>On {laytime.summary.laytimeType}</div>
								<div className={cn('text-right', laytimeTypeObj?.color)}>
									{laytime.summary.varianceTime}
								</div>
								<Separator orientation="horizontal" />
								<Separator orientation="horizontal" />
								<div className="text-lg">Amount due</div>
								<div className="text-right">
									<span className="text-muted-foreground text-xs">
										{laytime.summary.rateCurrency}
									</span>
									&nbsp;
									<span className={cn('text-lg font-medium', laytimeTypeObj?.color)}>
										{laytime.summary.amountDue}
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</>
	);
}
