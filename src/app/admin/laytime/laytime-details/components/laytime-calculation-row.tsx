import * as React from 'react';
import { format } from 'date-fns';
import { useState } from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { Calculation } from '@/hooks/laytime/types';
import { eventTypes } from '@/constants';
import { Checkbox } from '@/components/ui/checkbox';
import { Combobox } from '@/components/ui/combobox';

interface CalculationRowProps {
	calculation: Calculation;
	index: number;
	laytimeUid: string;
	onInputChange: (index: number, field: keyof Calculation, value: string | boolean) => void;
	onDateChange: (index: number, date: Date | undefined) => void;
	selectedEvents: string[];
	onSelectEvent: (event: string) => void;
	onDeselectEvent: (event: string) => void;
	selectable: boolean;
}

export function CalculationRow({
	calculation,
	index,
	laytimeUid,
	onInputChange,
	onDateChange,
	selectedEvents,
	onSelectEvent,
	onDeselectEvent,
	selectable
}: CalculationRowProps) {
	const [time, setTime] = useState(format(new Date(calculation.date), 'HH:mm'));
	const addTimeToDate = (time: string) => {
		const [hours, minutes] = time.split(':').map(Number);
		return new Date(
			new Date(calculation.date).getFullYear(),
			new Date(calculation.date).getMonth(),
			new Date(calculation.date).getDate(),
			hours,
			minutes
		);
	};

	const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setTime(e.target.value);
		onDateChange(index, addTimeToDate(e.target.value));
	};

	const renderEventRow = () => (
		<TableRow key={`${laytimeUid}-${calculation.event}-${calculation.date}-event`}>
			{selectable && (
				<TableCell>
					<div className="flex items-center gap-2">
						<Checkbox
							checked={selectedEvents.includes(calculation.id)}
							onCheckedChange={checked => {
								if (checked) {
									onSelectEvent(calculation.id);
								} else {
									onDeselectEvent(calculation.id);
								}
							}}
						/>
					</div>
				</TableCell>
			)}
			<TableCell>
				<div className="flex items-center gap-2">
					<Combobox
						initialValue={calculation.event}
						data={eventTypes.map(event => ({ value: event, label: event }))}
						placeholder="Select event"
						className="h-8 font-normal"
						onSelect={value => onInputChange(index, 'event', value)}
					/>
				</div>
			</TableCell>
			<TableCell>
				<DatePicker date={new Date(calculation.date)} setDate={date => onDateChange(index, date)} />
			</TableCell>
			<TableCell>
				<Input className="h-8 font-medium" value={time} onChange={handleTimeChange} />
			</TableCell>
			<TableCell>
				<Input
					className="text-muted-foreground h-8 italic"
					value={calculation.remarks}
					onChange={e => onInputChange(index, 'remarks', e.target.value)}
				/>
			</TableCell>
		</TableRow>
	);

	const renderLaytimeStartsRow = () => (
		<TableRow key={`${laytimeUid}-${calculation.event}-${calculation.date}-laytime`}>
			<TableCell colSpan={5} className="text-center">
				<span className="bg-primary rounded-full px-4 py-1.5 text-xs text-white">
					<b>Laytime Starts</b>&nbsp;&nbsp;
					{format(new Date(calculation.date), 'd MMM yy')} @ {format(new Date(calculation.date), 'HH:mm')}
				</span>
			</TableCell>
		</TableRow>
	);

	const renderDemurrageStartsRow = () => (
		<TableRow key={`${laytimeUid}-${calculation.event}-${calculation.date}-demurrage`}>
			<TableCell colSpan={5} className="text-center">
				<span className="rounded-full bg-red-700 px-4 py-1.5 text-xs text-white">
					<b>On demurrage</b>&nbsp;&nbsp;
					{format(new Date(calculation.date), 'd MMM yy')} @ {format(new Date(calculation.date), 'HH:mm')}
				</span>
			</TableCell>
		</TableRow>
	);

	if (calculation.laytimeStarts) {
		return (
			<React.Fragment key={`${laytimeUid}-${calculation.event}-${calculation.date}-group`}>
				{renderEventRow()}
				{renderLaytimeStartsRow()}
			</React.Fragment>
		);
	} else if (calculation.demurrageStarts) {
		return (
			<React.Fragment key={`${laytimeUid}-${calculation.event}-${calculation.date}-group`}>
				{renderEventRow()}
				{renderDemurrageStartsRow()}
			</React.Fragment>
		);
	} else {
		return renderEventRow();
	}
}
