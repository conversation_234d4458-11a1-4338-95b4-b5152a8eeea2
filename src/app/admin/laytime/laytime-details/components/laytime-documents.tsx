import { File } from 'lucide-react';
import { useLaytimeContext } from '../../contexts/laytime-context';
import { Button } from '@/components/ui/button';

export function LaytimeDocuments() {
	const { state } = useLaytimeContext();
	const laytime = state.laytimeData;

	if (!laytime?.documents) {
		return null;
	}

	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Documents</h3>
			<div className="flex flex-col gap-2 p-2 sm:flex-row">
				{laytime.documents.map(document => (
					<Button key={document.name} variant="outline" size="sm">
						<File />
						{document.name}.{document.type}
					</Button>
				))}
			</div>
		</div>
	);
}
