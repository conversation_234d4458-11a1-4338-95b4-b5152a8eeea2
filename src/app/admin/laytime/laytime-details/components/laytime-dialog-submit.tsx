import { useState } from 'react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useLaytimeContext } from '../../contexts/laytime-context';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from '@/components/ui/dialog';
import { CommentStatusEnum, LaytimeStatusEnum } from '@/graphql';
import { useInsertComment } from '@/hooks/comment';
import { useUpdateLaytime } from '@/hooks/laytime';

interface DialogApproveProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

const status = LaytimeStatusEnum.Submitted;
const operatorId = '10d8a966-ff8a-4c18-9b02-2d003ab1895d';
export function LaytimeDialogSubmit({ open, onOpenChange }: DialogApproveProps) {
	const {
		state: { laytimeData: laytime, delete: deleteItems },
		dispatch
	} = useLaytimeContext();
	const { insertComment } = useInsertComment();
	const { updateLaytime, deleteCalculations, deleteExclusions } = useUpdateLaytime();
	const [message, setMessage] = useState('');

	const handleSubmit = async () => {
		onOpenChange(false);
		if (laytime?.id) {
			await insertComment({
				laytimeId: laytime?.id,
				status: status as unknown as CommentStatusEnum,
				message,
				userId: operatorId
			}).then(async () => {
				await updateLaytime({
					...laytime,
					general: {
						...laytime.general,
						status
					}
				}).then(() => {
					dispatch({
						type: 'UPDATE_LAYTIME_STATUS',
						payload: status
					});
					dispatch({
						type: 'ADD_ACTIVITY_LOG',
						payload: {
							comment: message,
							operator: {
								avatar: 'https://cdn.jsdelivr.net/gh/faker-js/assets-person-portrait/male/512/2.jpg',
								firstName: 'Chanelle',
								lastName: 'Schiller',
								id: operatorId
							},
							status,
							date: new Date().toISOString()
						}
					});
					toast('The laytime has been sent for approval!', {
						duration: 3000,
						description: format(new Date(), 'd MMM yy HH:mm a'),
						action: {
							label: 'Undo',
							onClick: () => console.log('Undo')
						}
					});
				});
			});
		}
		if (deleteItems.events.length > 0) {
			await deleteCalculations(deleteItems.events);
		}
		if (deleteItems.exclusions.length > 0) {
			await deleteExclusions(deleteItems.exclusions);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Submit changes</DialogTitle>
					<DialogDescription>Submit the changes you have made to the laytime.</DialogDescription>
				</DialogHeader>
				<Textarea
					placeholder="Add any notes for the approver (optional)"
					value={message}
					onChange={e => setMessage(e.target.value)}
				/>
				<DialogFooter>
					<Button type="submit" variant="secondary" onClick={() => onOpenChange(false)}>
						Cancel
					</Button>
					<Button type="submit" onClick={() => void handleSubmit()}>
						Submit
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
