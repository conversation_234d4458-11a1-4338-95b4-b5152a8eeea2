// eslint-disable-next-line react-compiler/react-compiler
/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect } from 'react';
import { useParams } from 'react-router';
import { useLaytimeContext } from '../../contexts/laytime-context';
import { LaytimeDocuments } from './laytime-documents';
import { LaytimeGeneral } from './laytime-general';
import { LaytimeSkeleton } from './laytime-skeleton';
import { LaytimeFooter } from './laytime-footer';
import { LaytimeCalculation } from './laytime-calculation';
import { LaytimeActivity } from './laytime-activity';
import { LaytimeExclusions } from './laytime-exclusions';
import { Separator } from '@/components/ui/separator';
import { useLaytime } from '@/hooks/laytime/index';
import { useCharterers } from '@/hooks/charterer';
import { usePorts } from '@/hooks/port';

export function Laytime() {
	const params = useParams();
	const slug = params.id as string;

	const { laytime, loading: laytimeLoading } = useLaytime(slug);
	const { charterers, loading: charterersLoading } = useCharterers();
	const { ports, loading: portsLoading } = usePorts();
	const { dispatch } = useLaytimeContext();

	useEffect(() => {
		if (!laytimeLoading && !charterersLoading && !portsLoading) {
			dispatch({
				type: 'INITIALIZE_STATE',
				payload: {
					laytime: {
						...laytime,
						documents: [
							{
								name: 'Notice of readines',
								type: 'docx'
							},
							{
								name: 'Statement of facts',
								type: 'pdf'
							},
							{
								name: 'C/P-Terms2025',
								type: 'pdf'
							}
						]
					},
					charterers: charterers || [],
					ports: ports || []
				}
			});
		}
	}, [laytimeLoading, charterersLoading, portsLoading]);

	if (laytimeLoading || charterersLoading || portsLoading || !charterers) {
		return <LaytimeSkeleton />;
	}

	return (
		<div className="flex w-full flex-col items-center justify-between">
			<div className="flex w-full flex-col items-center overflow-auto">
				<div className="w-full max-w-4xl px-4">
					<div className="flex flex-col gap-4">
						<LaytimeGeneral />
						<Separator />
						<LaytimeCalculation />
						<Separator />
						<LaytimeExclusions />
						<Separator />
						<LaytimeDocuments />
						<Separator />
						<LaytimeActivity />
					</div>
				</div>
			</div>
			<LaytimeFooter />
		</div>
	);
}
