import { <PERSON>h, Anchor, ArrowRightLeft, Box, BriefcaseBusiness, Calendar } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectGroup, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker, DatePickerWithRange } from '@/components/ui/date-picker';
import { Charterer } from '@/hooks/charterer/types';
import { Port } from '@/hooks/port';
import { Combobox } from '@/components/ui/combobox';

// Voyage ID Field
export function VoyageIdField({ value, onChange }: { value: string; onChange?: (value: string) => void }) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<Hash className="h-4 w-4" />
				Voyage ID
			</div>
			<div>
				<Input className="h-8" value={value} onChange={e => onChange?.(e.target.value)} />
			</div>
		</>
	);
}

// Port Field
export function PortField({
	value,
	onChange,
	options
}: {
	value: string;
	onChange?: (value: string) => void;
	options: Port[];
}) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<Anchor className="h-4 w-4" />
				Port
			</div>
			<div>
				<Combobox
					initialValue={value}
					data={options.map(option => ({
						value: option.name,
						label: `${option.name}, ${option.countryCode}`
					}))}
					placeholder="Select port"
					className="h-8 font-normal"
					onSelect={onChange ? onChange : () => {}}
				/>
			</div>
		</>
	);
}

// Operation Field
export function OperationField({
	value,
	onChange,
	options
}: {
	value: string;
	onChange?: (value: string) => void;
	options: Array<{ value: string; label: string }>;
}) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<ArrowRightLeft className="h-4 w-4" />
				Operation
			</div>
			<div>
				<Select value={value} onValueChange={onChange}>
					<SelectTrigger className="h-8">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectGroup>
							{options.map(option => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectGroup>
					</SelectContent>
				</Select>
			</div>
		</>
	);
}

// Cargo Field
export function CargoField({
	quantity,
	unit,
	cargoType,
	onQuantityChange,
	onCargoTypeChange,
	cargoOptions
}: {
	quantity: string;
	unit: string;
	cargoType: string;
	onQuantityChange?: (value: string) => void;
	onCargoTypeChange?: (value: string) => void;
	cargoOptions: string[];
}) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<Box className="h-4 w-4" />
				Cargo
			</div>
			<div className="flex items-center gap-2">
				<div className="flex-1">
					<Input
						className="h-8"
						value={quantity}
						suffix={unit}
						onChange={e => onQuantityChange?.(e.target.value)}
					/>
				</div>
				<div className="flex-1">
					<Combobox
						initialValue={cargoType}
						data={cargoOptions.map(option => ({ value: option, label: option }))}
						placeholder="Select cargo type"
						className="h-8 font-normal"
						onSelect={onCargoTypeChange ? onCargoTypeChange : () => {}}
					/>
				</div>
			</div>
		</>
	);
}

// Counterparty Field
export function CounterpartyField({
	value,
	onChange,
	options
}: {
	value: string;
	onChange?: (value: string) => void;
	options: Charterer[];
}) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<BriefcaseBusiness className="h-4 w-4" />
				Counterparty
			</div>
			<div>
				<Combobox
					initialValue={value}
					data={options.map(option => ({ value: option.name, label: option.name }))}
					placeholder="Select counterparty"
					className="h-8 font-normal"
					onSelect={onChange ? onChange : () => {}}
				/>
			</div>
		</>
	);
}

// CP Date Field
export function CPDateField({ date, setDate }: { date: Date; setDate: (date: Date | undefined) => void }) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<Calendar className="h-4 w-4" />
				C/P Date
			</div>
			<div>
				<DatePicker date={date} setDate={setDate} />
			</div>
		</>
	);
}

// Laycan Field
export function LaycanField({
	dateRange,
	setDateRange
}: {
	dateRange: { from: Date; to: Date };
	setDateRange: (range: { from: Date; to: Date }) => void;
}) {
	return (
		<>
			<div className="text-muted-foreground flex items-center gap-2">
				<Calendar className="h-4 w-4" />
				Laycan
			</div>
			<div>
				<DatePickerWithRange
					date={dateRange}
					setDate={date => {
						if (date.from && date.to) {
							setDateRange({ from: date.from, to: date.to });
						}
					}}
				/>
			</div>
		</>
	);
}

// Rate Quantity Field
export function RateQuantityField({
	value,
	suffix,
	onChange
}: {
	value: string;
	suffix: string;
	onChange?: (value: string) => void;
}) {
	return (
		<>
			<div>Operation rate</div>
			<div className="text-right">
				<Input className="h-8" value={value} suffix={suffix} onChange={e => onChange?.(e.target.value)} />
			</div>
		</>
	);
}

// Rate Amount Field
export function RateAmountField({
	value,
	currency,
	onChange
}: {
	value: string;
	currency: string;
	onChange?: (value: string) => void;
}) {
	return (
		<>
			<div>
				<span className="capitalize">Demurrage</span> rate
			</div>
			<div className="text-right">
				<Input className="h-8" value={value} suffix={currency} onChange={e => onChange?.(e.target.value)} />
			</div>
		</>
	);
}
