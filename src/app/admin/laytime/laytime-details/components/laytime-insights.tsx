import { useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, XAxis, Line, LineChart } from 'recharts';
import { Calendar, Dot } from 'lucide-react';
import { LaytimeInsightsSkeleton } from './laytime-insights-skeleton';
import { Separator } from '@/components/ui/separator';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

interface InsightsData {
	sofDataKey: string;
	sofData: { event: string; time: number; hrs: number; min: number; percentage: number; fill: string }[];
	sofConfig: { time: { label: string } };
	cargoDataKey: string;
	cargoData: { day: string; cargo1: number; cargo2: number; date: string; rate: string }[];
	cargoConfig: { cargo1: { label: string; color: string }; cargo2: { label: string; color: string } };
}

export function LaytimeInsights({ open }: { open: boolean }) {
	const [insights, setInsights] = useState<InsightsData | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (!open) return;

		async function fetchData() {
			setLoading(true);
			try {
				const response = await import('../../../../../api/laytime/insights.json');
				const data: InsightsData = response as InsightsData;

				setInsights(data);
			} catch (error) {
				console.error('Error fetching laytime:', error);
			}
			setLoading(false);
		}

		void fetchData();
	}, [open]);

	if (loading || !insights) {
		return <LaytimeInsightsSkeleton />;
	}

	return (
		<div className="flex flex-1 flex-col gap-2 overflow-auto">
			<div className="flex flex-col gap-4 px-4">
				<ChartContainer config={insights.sofConfig}>
					<BarChart accessibilityLayer data={insights.sofData} barSize={5}>
						<CartesianGrid vertical={false} />
						<XAxis
							dataKey={insights.sofDataKey}
							tickLine={false}
							tickMargin={10}
							axisLine={false}
							tickFormatter={(value: string) => value.slice(0, 8)}
						/>
						<ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dashed" />} />
						<Bar dataKey="time" fill="var(--color-time)" radius={4} />
					</BarChart>
				</ChartContainer>
				<Table className="text-xs">
					<TableBody>
						{insights.sofData.map(insights => (
							<TableRow key={insights.time}>
								<TableCell>
									<div className="flex items-center">
										<Dot stroke={insights.fill} className="-ml-2" />
										{insights.event} ({insights.percentage}%)
									</div>
								</TableCell>
								<TableCell className="text-right">
									{insights.hrs} hrs : {insights.min} min
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
			<Separator />
			<div className="flex flex-col gap-4 px-4">
				<div className="py-6">
					<h3 className="text-base font-semibold">Cargo Progress</h3>
				</div>
				<ChartContainer config={insights.cargoConfig}>
					<LineChart
						accessibilityLayer
						data={insights.cargoData}
						margin={{
							left: 6,
							right: 6
						}}
					>
						<CartesianGrid vertical={false} />
						<XAxis
							dataKey={insights.cargoDataKey}
							tickLine={false}
							axisLine={false}
							tickMargin={8}
							tickFormatter={(value: string) => value.slice(0, 5)}
						/>
						<ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
						<Line
							dataKey="cargo1"
							type="linear"
							stroke="var(--color-cargo1)"
							strokeWidth={1.5}
							dot={{
								fill: 'var(--color-cargo1)'
							}}
							activeDot={{
								r: 6
							}}
						/>
						<Line
							dataKey="cargo2"
							type="linear"
							stroke="var(--color-cargo2)"
							strokeWidth={1.5}
							dot={{
								fill: 'var(--color-cargo2)'
							}}
							activeDot={{
								r: 6
							}}
						/>
					</LineChart>
				</ChartContainer>
				<Table className="text-xs">
					<TableBody>
						{insights.cargoData.map(insights => (
							<TableRow key={insights.date}>
								<TableCell>
									<div className="flex items-center gap-2 py-1">
										<Calendar className="h-3 w-3" />
										{insights.date}
									</div>
								</TableCell>
								<TableCell className="text-right">
									{insights.cargo1 + insights.cargo2}.000 mts
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
