import { Plus, Trash } from 'lucide-react';
import { useState } from 'react';
import { useLaytimeContext } from '../../contexts/laytime-context';
import { LaytimeExclusionRow } from './laytime-exclusion-row';
import { Exclusion } from '@/hooks/laytime/types';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { LaytimeStatusEnum } from '@/graphql';

export function LaytimeExclusions() {
	const { state, dispatch } = useLaytimeContext();
	const laytime = state.laytimeData;
	const [selectedExclusions, setSelectedExclusions] = useState<string[]>([]);

	if (!laytime) {
		return null;
	}

	const handleInputChange = (index: number, field: keyof Exclusion, value: string) => {
		dispatch({
			type: 'UPDATE_EXCLUSION',
			payload: { index, field, value }
		});
	};

	const handleDateChange = (index: number, field: 'dateFrom' | 'dateTo', date: Date | undefined) => {
		if (!date) return;

		dispatch({
			type: 'UPDATE_EXCLUSION_DATE',
			payload: { index, field, date }
		});
	};

	const handleAddExclusion = () => {
		dispatch({
			type: 'ADD_EXCLUSION',
			payload: {
				id: '',
				dateFrom: new Date().toISOString(),
				dateTo: new Date().toISOString(),
				event: '',
				duration: 0,
				percentage: '0',
				timeUsed: 0
			}
		});
	};

	const handleSelectExclusion = (exclusion: string) => {
		setSelectedExclusions(prev => [...prev, exclusion]);
	};

	const handleDeselectExclusion = (exclusion: string) => {
		setSelectedExclusions(prev => prev.filter(e => e !== exclusion));
	};

	const handleDeleteExclusions = () => {
		dispatch({ type: 'MARK_EXCLUSIONS_FOR_DELETION', payload: selectedExclusions });
		dispatch({
			type: 'DELETE_EXCLUSIONS',
			payload: selectedExclusions
		});
		setSelectedExclusions([]);
	};

	const editable =
		laytime.general.status !== LaytimeStatusEnum.PendingApproval.toString() &&
		laytime.general.status !== LaytimeStatusEnum.Submitted.toString() &&
		laytime.general.status !== LaytimeStatusEnum.Approved.toString() &&
		laytime.general.status !== '' &&
		laytime.general.status !== null;

	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Exclusions</h3>
			{laytime.exclusions.length > 0 && (
				<Table>
					<TableHeader>
						<TableRow>
							{editable && <TableHead className="w-[32px]"></TableHead>}
							<TableHead className="w-[110px]">Date from</TableHead>
							<TableHead className="w-[85px]">From</TableHead>
							<TableHead className="w-[110px]">Date to</TableHead>
							<TableHead className="w-[85px]">To</TableHead>
							<TableHead>Event</TableHead>
							<TableHead className="w-[80px] text-right">Duration</TableHead>
							<TableHead className="w-[80px] text-center">%</TableHead>
							<TableHead className="w-[80px] text-right">Time used</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{laytime.exclusions.map((exclusion, index) => (
							<LaytimeExclusionRow
								key={`exclusion-${exclusion.dateFrom}-${exclusion.dateTo}-${exclusion.id}`}
								selectable={editable}
								selectedExclusions={selectedExclusions}
								exclusion={exclusion}
								index={index}
								laytimeUid={laytime.id}
								onInputChange={handleInputChange}
								onDateChange={handleDateChange}
								onSelectExclusion={handleSelectExclusion}
								onDeselectExclusion={handleDeselectExclusion}
							/>
						))}
					</TableBody>
				</Table>
			)}
			{editable && (
				<div className="flex flex-row gap-2">
					<Button variant="outline" size="sm" className="w-fit" onClick={handleAddExclusion}>
						<Plus className="h-4 w-4" />
						Add Exclusion
					</Button>
					{selectedExclusions.length > 0 && (
						<Button variant="destructive" size="sm" className="w-fit" onClick={handleDeleteExclusions}>
							<Trash className="h-4 w-4" />
							Delete {selectedExclusions.length} Exclusion{selectedExclusions.length > 1 ? 's' : ''}
						</Button>
					)}
				</div>
			)}
		</div>
	);
}
