import * as React from 'react';
import { format } from 'date-fns';
import { useState } from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { Combobox } from '@/components/ui/combobox';
import { Exclusion } from '@/hooks/laytime/types';
import { eventTypes } from '@/constants';
import { Checkbox } from '@/components/ui/checkbox';

interface ExclusionRowProps {
	selectedExclusions: string[];
	selectable: boolean;
	exclusion: Exclusion;
	index: number;
	laytimeUid: string;
	onInputChange: (index: number, field: keyof Exclusion, value: string) => void;
	onDateChange: (index: number, field: 'dateFrom' | 'dateTo', date: Date | undefined) => void;
	onSelectExclusion: (exclusion: string) => void;
	onDeselectExclusion: (exclusion: string) => void;
}

export function LaytimeExclusionRow({
	selectedExclusions,
	selectable,
	exclusion,
	index,
	laytimeUid,
	onInputChange,
	onDateChange,
	onSelectExclusion,
	onDeselectExclusion
}: ExclusionRowProps) {
	const [fromTime, setFromTime] = useState(format(new Date(exclusion.dateFrom), 'HH:mm'));
	const [toTime, setToTime] = useState(format(new Date(exclusion.dateTo), 'HH:mm'));

	const addTimeToDate = (date: string, time: string) => {
		const [hours, minutes] = time.split(':').map(Number);
		return new Date(
			new Date(date).getFullYear(),
			new Date(date).getMonth(),
			new Date(date).getDate(),
			hours,
			minutes
		);
	};

	const handleFromTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setFromTime(e.target.value);
		onDateChange(index, 'dateFrom', addTimeToDate(exclusion.dateFrom, e.target.value));
	};

	const handleToTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setToTime(e.target.value);
		onDateChange(index, 'dateTo', addTimeToDate(exclusion.dateTo, e.target.value));
	};

	return (
		<TableRow key={`${laytimeUid}-exclusion-${index}`}>
			{selectable && (
				<TableCell>
					<Checkbox
						checked={selectedExclusions.includes(exclusion.id)}
						onCheckedChange={checked => {
							if (checked) {
								onSelectExclusion(exclusion.id);
							} else {
								onDeselectExclusion(exclusion.id);
							}
						}}
					/>
				</TableCell>
			)}
			<TableCell>
				<DatePicker
					date={new Date(exclusion.dateFrom)}
					setDate={date => onDateChange(index, 'dateFrom', date)}
				/>
			</TableCell>
			<TableCell>
				<Input className="h-8 font-medium" value={fromTime} onChange={handleFromTimeChange} />
			</TableCell>
			<TableCell>
				<DatePicker date={new Date(exclusion.dateTo)} setDate={date => onDateChange(index, 'dateTo', date)} />
			</TableCell>
			<TableCell>
				<Input className="h-8 font-medium" value={toTime} onChange={handleToTimeChange} />
			</TableCell>
			<TableCell>
				<Combobox
					initialValue={exclusion.event}
					data={eventTypes.map(event => ({ value: event, label: event }))}
					placeholder="Select event"
					className="h-8 font-normal"
					onSelect={value => onInputChange(index, 'event', value)}
				/>
			</TableCell>
			<TableCell className="text-right">
				<Input
					className="h-8 text-right"
					value={exclusion.duration}
					onChange={e => onInputChange(index, 'duration', e.target.value)}
				/>
			</TableCell>
			<TableCell className="text-center">
				<Input
					className="h-8 text-center"
					value={exclusion.percentage}
					onChange={e => onInputChange(index, 'percentage', e.target.value)}
				/>
			</TableCell>
			<TableCell className="text-right">
				<Input
					className="h-8 text-right"
					value={exclusion.timeUsed}
					onChange={e => onInputChange(index, 'timeUsed', e.target.value)}
				/>
			</TableCell>
		</TableRow>
	);
}
