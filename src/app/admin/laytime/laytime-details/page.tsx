import { useState } from 'react';
import { Bar<PERSON><PERSON>Big } from 'lucide-react';
import { Link } from 'react-router';
import { useLaytimeContext } from '../contexts/laytime-context';
import { LaytimeInsights } from './components/laytime-insights';
import { Laytime } from './components/laytime';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';

import { Separator } from '@/components/ui/separator';
import { Sidebar, SidebarContent, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';

export default function LaytimePage() {
	const { state } = useLaytimeContext();
	const [open, setOpen] = useState(false);

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<Link to="../">
									<BreadcrumbLink>Laytime</BreadcrumbLink>
								</Link>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>
									mv {state.laytimeData?.general.vesselName} ({state.laytimeData?.general.id})
								</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex gap-2 px-4">
					<ModeToggle />
					<Button variant="outline" onClick={() => setOpen(open => !open)}>
						<BarChartBig />
						<span>Insights</span>
					</Button>
				</div>
			</header>
			<SidebarProvider
				style={{ '--sidebar-width': '25rem' } as React.CSSProperties}
				open={open}
				className="relative flex min-h-0 flex-1 flex-row overflow-hidden pt-4"
				onOpenChange={setOpen}
			>
				<Laytime />
				<Sidebar variant="floating" side="right">
					<SidebarContent className="bg-panel flex flex-col gap-0">
						<div className="flex flex-row items-center p-6">
							<h3 className="flex-1 text-base font-semibold">Insights</h3>
							<SidebarTrigger className="-ml-1" />
						</div>
						<LaytimeInsights open={open} />
					</SidebarContent>
				</Sidebar>
			</SidebarProvider>
		</>
	);
}
