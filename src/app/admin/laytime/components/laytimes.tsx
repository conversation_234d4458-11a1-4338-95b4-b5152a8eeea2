import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { useLaytimes } from '@/hooks/laytime/index';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';

export default function Laytimes() {
	const { laytime, loading } = useLaytimes();

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={laytime} columns={columns} />;
}
