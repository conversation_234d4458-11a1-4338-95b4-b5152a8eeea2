import { ColumnDef } from '@tanstack/react-table';
import { MessageSquare } from 'lucide-react';
import { amountTypes, statuses } from '@/constants';
import { Laytime, Operator } from '@/hooks/laytime/types';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { Port } from '@/hooks/port';
import { Charterer } from '@/hooks/charterer/types';

export const columns: ColumnDef<Laytime>[] = [
	{
		accessorKey: 'id',
		meta: {
			label: 'Voyage ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Voyage ID" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('id')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'vessel',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" />,
		cell: ({ row }) => <div className="font-medium">{row.getValue('vessel')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'port',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" />,
		cell: ({ row }) => {
			const port: Port = row.getValue('port');
			return <div>{`${port?.name}, ${port?.countryCode}`}</div>;
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'charterer',
		meta: {
			label: 'Charterer'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Charterer" />,
		cell: ({ row }) => {
			const charterer: Charterer = row.getValue('charterer');
			return <div className="max-w-[160px] truncate">{`${charterer?.name}`}</div>;
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'amount.value',
		meta: {
			label: 'Amount'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" reverse />,
		cell: ({ row }) => {
			const amount = row.original.amount;
			const type = amountTypes.find(type => type.name === amount.type);

			// Format the amount as a dollar amount
			const formatted = new Intl.NumberFormat('en-US', {
				style: 'currency',
				currency: amount.currency
			}).format(amount.value);

			return (
				<div className="flex items-center gap-2 justify-self-end font-medium">
					{formatted}
					<Badge variant="outline" className={cn('w-9 px-1 py-0.5', type?.color)}>
						{type?.label}
					</Badge>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = statuses.find(status => status.value === row.getValue('status'));

			if (!status) {
				return null;
			}

			return (
				<Badge variant="outline" className="rounded-full">
					{status.icon && <status.icon className={cn('h-3 w-3', status.color)} />}
					<span>{status.label}</span>
				</Badge>
			);
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'date',
		meta: {
			label: 'Requested on'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Requested on" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('date')}</div>,
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'comments',
		meta: {
			label: 'Comments'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Comments" />,
		cell: ({ row }) => (
			<div>
				{row.getValue('comments') ? (
					<Badge variant="outline" className="text-muted-foreground rounded-full">
						<MessageSquare className="h-3 w-3" />
						{row.getValue('comments')}
					</Badge>
				) : (
					''
				)}
			</div>
		),
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'operator',
		meta: {
			label: 'Operator'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Operator" />,
		cell: ({ row }) => {
			const operator: Operator = row.getValue('operator');

			if (!operator.firstName || !operator.lastName) {
				return null;
			}

			return (
				<div className="flex items-center gap-2">
					<Avatar className="h-5 w-5 rounded-sm">
						<AvatarImage src={operator.avatar} alt={operator.lastName} />
						<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
							{operator.lastName.charAt(0)}
						</AvatarFallback>
					</Avatar>
					<span className="text-muted-foreground max-w-[90px] truncate text-sm">{`${operator.firstName[0]}. ${operator.lastName}`}</span>
				</div>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	}
];
