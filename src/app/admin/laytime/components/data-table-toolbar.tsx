import { Table } from '@tanstack/react-table';
import { Plus, Upload, X } from 'lucide-react';
import { toast } from 'sonner';

import { useRef, useState } from 'react';
import { statuses } from '@/constants';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTableViewOptions } from '@/components/data-table/data-table-view-options';
import {
	DialogClose,
	DialogDescription,
	DialogFooter,
	DialogTitle,
	DialogTrigger,
	DialogHeader,
	DialogContent,
	Dialog
} from '@/components/ui/dialog';
import { DataTableFacetedFilter } from '@/components/data-table/data-table-faceted-filter';
import { useFileUpload } from '@/hooks/signedUrl';
import { useInsertLaytime } from '@/hooks/laytime/use-insert-laytime';
import { useInsertPort } from '@/hooks/laytime/use-insert-port';
import { useInsertCargo } from '@/hooks/laytime/use-insert-cargo';
import { useInsertCharterPartyTerms } from '@/hooks/laytime/use-insert-charter-party-terms';
import { useInsertSof } from '@/hooks/laytime/use-insert-sof';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

function UploadField({
	title,
	onUpload,
	onUploadStateChange
}: {
	title: string;
	onUpload: (file: File, fileData: { url: string; originalName: string; uuid: string }) => void;
	onUploadStateChange: (isUploading: boolean) => void;
}) {
	const fileInputRef = useRef<HTMLInputElement | null>(null);
	const [file, setFile] = useState<File | null>(null);
	const [uploading, setUploading] = useState(false);
	const { uploadFile } = useFileUpload();

	const handleButtonClick = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.files && event.target.files.length > 0) {
			const selectedFile = event.target.files[0];

			setFile(selectedFile);
			setUploading(true);
			onUploadStateChange(true);

			uploadFile(selectedFile)
				.then(fileData => {
					onUpload(selectedFile, fileData);
					toast('Upload Successful', {
						description: `${selectedFile.name} has been uploaded successfully.`,
						duration: 3000
					});
				})
				.catch(error => {
					setFile(null);
					console.error('Upload failed:', error);
				})
				.finally(() => {
					setUploading(false);
					onUploadStateChange(false);
				});
		}
	};

	return (
		<>
			<div className="flex h-full w-full items-center justify-between">
				<p className="text-sm">{file ? file.name : title}</p>
				<Input
					ref={fileInputRef}
					type="file"
					accept=".pdf,.xlsx,.docx"
					className="hidden"
					onChange={handleFileChange}
				/>
				{uploading ? (
					<div className="text-sm">Uploading...</div>
				) : file ? (
					<Button
						size="icon"
						variant="ghost"
						onClick={() => {
							setFile(null);
						}}
					>
						<X />
					</Button>
				) : (
					<Button size="sm" variant="secondary" onClick={handleButtonClick}>
						<Upload /> Upload
					</Button>
				)}
			</div>
			<hr className="w-full" />
		</>
	);
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const [norFile, setNorFile] = useState<{ url: string; name: string; uuid: string } | null>(null);
	const [cpTermsFile, setCpTermsFile] = useState<{ url: string; name: string; uuid: string } | null>(null);
	const [sofFile, setSofFile] = useState<{ url: string; name: string; uuid: string } | null>(null);
	const [isUploading, setIsUploading] = useState(false);
	const [isCalculating, setIsCalculating] = useState(false);
	const { insertLaytime } = useInsertLaytime();
	const { insertPort } = useInsertPort();
	const { insertCargo } = useInsertCargo();
	const { insertCharterPartyTerms } = useInsertCharterPartyTerms();
	const { insertSof } = useInsertSof();

	const handleUploadStateChange = (uploading: boolean) => {
		setIsUploading(_ => {
			if (uploading) return true;
			return false;
		});
	};

	const handleCalculate = async () => {
		try {
			setIsCalculating(true);

			//TODO: Implement via a single transaction
			const port = await insertPort({
				name: 'Example Port',
				countryCode: 'US'
			});

			const cargo = await insertCargo({
				type: 'Bulk',
				quantity: 1000,
				unit: 'MT'
			});

			const charterPartyTerms = await insertCharterPartyTerms({
				laytimeAllowed: 72,
				loadingRate: 1000,
				dischargingRate: 1000,
				demurrageRate: 10000
			});

			const sof = await insertSof({
				cargoId: cargo!.id,
				portId: port!.id,
				charterPartyTermsId: charterPartyTerms!.id,
				vesselId: '8393de3e-04ca-4376-83d4-7268b11c4d91',
				chartererId: 'd4f90abf-5762-40cf-ab86-fc7d15492a13'
			});

			const result = await insertLaytime({
				operation: 'loading',
				operatorId: '07ed39ce-de96-442f-86f3-556f21d5e9c7', //hardcoding the operator again..
				sofId: sof!.id,
				status: 'draft',
				laytimeAllowed: 72,
				norTendered: norFile ? new Date().toISOString() : undefined,
				norAccepted: norFile ? new Date().toISOString() : undefined,
				laytimeStarted: new Date().toISOString(),
				laytimeUsed: 0,
				amount: JSON.stringify({
					type: 'demurrage',
					value: 0,
					currency: 'USD'
				})
			});

			if (!result) {
				throw new Error('Failed to create laytime');
			}

			setIsCalculating(false);
			toast.success('Laytime was successfully calculated', {
				description: 'View your newly created laytime now'
			});
		} catch (error) {
			toast.error('Error Creating Laytime', {
				description: error instanceof Error ? error.message : 'An error occurred while creating laytime.'
			});
		}
	};

	const isCalculateDisabled = isUploading || !cpTermsFile || !sofFile || isCalculating;

	return (
		<Dialog>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Calculate Laytime</DialogTitle>
					<DialogDescription>{`Let's get you started with a new Laytime`}</DialogDescription>
				</DialogHeader>
				<div className="grid gap-4 py-4">
					<p className="px-2 text-sm">Upload documents</p>
					<div className="flex flex-col items-center gap-2 rounded-lg border-2 border-dashed p-8">
						<UploadField
							title="Notice of readiness (Optional)"
							onUpload={(_, fileData) =>
								setNorFile({
									url: fileData.url,
									name: fileData.originalName,
									uuid: fileData.uuid
								})
							}
							onUploadStateChange={handleUploadStateChange}
						/>
						<UploadField
							title="C/P terms *"
							onUpload={(_, fileData) =>
								setCpTermsFile({
									url: fileData.url,
									name: fileData.originalName,
									uuid: fileData.uuid
								})
							}
							onUploadStateChange={handleUploadStateChange}
						/>
						<UploadField
							title="Statement of facts *"
							onUpload={(_, fileData) =>
								setSofFile({
									url: fileData.url,
									name: fileData.originalName,
									uuid: fileData.uuid
								})
							}
							onUploadStateChange={handleUploadStateChange}
						/>
					</div>
					<p className="text-muted-foreground px-2 text-xs">* Required fields</p>
				</div>
				<DialogFooter>
					<DialogClose asChild>
						<Button size="sm" variant="secondary">
							Cancel
						</Button>
					</DialogClose>
					<CalculateButton
						isUploading={isUploading}
						isCalculateDisabled={isCalculateDisabled}
						isCalculating={isCalculating}
						onClick={() => void handleCalculate()}
					/>
				</DialogFooter>
			</DialogContent>

			<div className="flex items-center justify-between border-t border-b py-2">
				<div className="flex flex-1 items-center space-x-2">
					<DialogTrigger asChild>
						<Button size="sm">
							<Plus /> Laytime
						</Button>
					</DialogTrigger>
					{table.getColumn('status') && (
						<DataTableFacetedFilter column={table.getColumn('status')} title="Status" options={statuses} />
					)}
				</div>
				<div className="flex items-center gap-2">
					<Input
						placeholder="Search..."
						value={(table.getState().globalFilter as string) ?? ''}
						className="h-8 w-[150px] lg:w-[250px]"
						onChange={event => table.setGlobalFilter(event.target.value)}
					/>
					<DataTableViewOptions table={table} />
				</div>
			</div>
		</Dialog>
	);
}

type CalculateButtonProps = {
	isUploading: boolean;
	isCalculateDisabled: boolean;
	onClick: () => void;
	isCalculating: boolean;
};

function CalculateButton({ isUploading, isCalculateDisabled, onClick, isCalculating }: CalculateButtonProps) {
	let text = 'Calculate';
	if (isUploading) {
		text = 'Uploading...';
	}
	if (isCalculating) {
		text = 'Calculating...';
	}
	return (
		<Button disabled={isCalculateDisabled} size="sm" onClick={onClick}>
			{text}
		</Button>
	);
}
