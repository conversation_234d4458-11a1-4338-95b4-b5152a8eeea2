import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { format } from 'date-fns';
import { Delete } from '../types';
import { LaytimeData, Calculation, Activity, Exclusion } from '@/hooks/laytime/types';
import { Charterer } from '@/hooks/charterer/types';
import { Port } from '@/hooks/port/types';
import { LaytimeStatusEnum } from '@/graphql';

export interface LaytimeContextState {
	laytimeData: LaytimeData | null;
	charterers: Charterer[];
	ports: Port[];
	delete: Delete;
}

type LaytimeAction =
	| { type: 'INITIALIZE_STATE'; payload: { laytime: LaytimeData; charterers: Charterer[]; ports: Port[] } }
	| { type: 'UPDATE_VOYAGE_ID'; payload: string }
	| { type: 'UPDATE_PORT'; payload: Port }
	| { type: 'UPDATE_OPERATION'; payload: string }
	| { type: 'UPDATE_CARGO_QUANTITY'; payload: string }
	| { type: 'UPDATE_CARGO_TYPE'; payload: string }
	| { type: 'UPDATE_COUNTERPARTY'; payload: Charterer }
	| { type: 'UPDATE_CP_DATE'; payload: Date }
	| { type: 'UPDATE_LAYCAN_DATE_RANGE'; payload: { from: Date; to: Date } }
	| { type: 'UPDATE_RATE_QTY'; payload: string }
	| { type: 'UPDATE_RATE_AMOUNT'; payload: string }
	| { type: 'UPDATE_CALCULATION'; payload: { index: number; field: keyof Calculation; value: string | boolean } }
	| { type: 'UPDATE_CALCULATION_DATE'; payload: { index: number; date: Date } }
	| { type: 'UPDATE_LAYTIME_STATUS'; payload: LaytimeStatusEnum }
	| { type: 'ADD_ACTIVITY_LOG'; payload: Activity }
	| { type: 'ADD_CALCULATION'; payload: Calculation }
	| { type: 'DELETE_CALCULATIONS'; payload: string[] }
	| { type: 'UPDATE_EXCLUSION'; payload: { index: number; field: keyof Exclusion; value: string } }
	| { type: 'UPDATE_EXCLUSION_DATE'; payload: { index: number; field: 'dateFrom' | 'dateTo'; date: Date } }
	| { type: 'ADD_EXCLUSION'; payload: Exclusion }
	| { type: 'DELETE_EXCLUSIONS'; payload: string[] }
	| { type: 'MARK_EVENTS_FOR_DELETION'; payload: string[] }
	| { type: 'CLEAR_EVENTS_TO_DELETE' }
	| { type: 'MARK_EXCLUSIONS_FOR_DELETION'; payload: string[] }
	| { type: 'CLEAR_EXCLUSIONS_TO_DELETE' };

interface LaytimeContextType {
	state: LaytimeContextState;
	dispatch: React.Dispatch<LaytimeAction>;
}

const LaytimeContext = createContext<LaytimeContextType | undefined>(undefined);

function laytimeReducer(state: LaytimeContextState, action: LaytimeAction): LaytimeContextState {
	switch (action.type) {
		case 'INITIALIZE_STATE':
			return {
				laytimeData: action.payload.laytime,
				charterers: action.payload.charterers,
				ports: action.payload.ports,
				delete: {
					events: [],
					exclusions: []
				}
			};
		case 'UPDATE_VOYAGE_ID':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						id: action.payload
					}
				}
			};
		case 'UPDATE_PORT':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						port: action.payload
					}
				}
			};
		case 'UPDATE_OPERATION':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						operation: action.payload
					}
				}
			};
		case 'UPDATE_CARGO_QUANTITY':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						cargo: {
							...state.laytimeData.general.cargo,
							quantity: action.payload
						}
					}
				}
			};
		case 'UPDATE_CARGO_TYPE':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						cargo: {
							...state.laytimeData.general.cargo,
							type: action.payload
						}
					}
				}
			};
		case 'UPDATE_COUNTERPARTY':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						party: action.payload
					}
				}
			};
		case 'UPDATE_CP_DATE':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						cpDate: action.payload.toISOString()
					}
				}
			};
		case 'UPDATE_LAYCAN_DATE_RANGE':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					general: {
						...state.laytimeData.general,
						laycanFrom: action.payload.from.toISOString(),
						laycanTo: action.payload.to.toISOString()
					}
				}
			};
		case 'UPDATE_RATE_QTY':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					summary: {
						...state.laytimeData.summary,
						rateQty: action.payload
					}
				}
			};
		case 'UPDATE_RATE_AMOUNT':
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					summary: {
						...state.laytimeData.summary,
						rateAmount: action.payload
					}
				}
			};
		case 'UPDATE_CALCULATION': {
			if (!state.laytimeData) return state;
			const { index, field, value } = action.payload;
			const updatedCalculations = state.laytimeData.calculation;
			updatedCalculations[index] = {
				...updatedCalculations[index],
				[field]: value
			};
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					calculation: updatedCalculations
				}
			};
		}
		case 'UPDATE_CALCULATION_DATE': {
			if (!state.laytimeData) return state;
			const { index, date } = action.payload;
			const updatedCalculations = state.laytimeData.calculation;
			updatedCalculations[index] = {
				...updatedCalculations[index],
				date: date.toISOString()
			};
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					calculation: updatedCalculations
				}
			};
		}
		case 'DELETE_CALCULATIONS': {
			if (!state.laytimeData) return state;
			const updatedCalculations = state.laytimeData.calculation.filter(c => !action.payload.includes(c.id));
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					calculation: updatedCalculations
				}
			};
		}
		case 'UPDATE_LAYTIME_STATUS': {
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: { ...state.laytimeData, general: { ...state.laytimeData.general, status: action.payload } }
			};
		}
		case 'ADD_ACTIVITY_LOG': {
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					activity: [...state.laytimeData.activity, action.payload]
				}
			};
		}
		case 'ADD_CALCULATION': {
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: { ...state.laytimeData, calculation: [...state.laytimeData.calculation, action.payload] }
			};
		}
		case 'UPDATE_EXCLUSION': {
			if (!state.laytimeData) return state;
			const { index, field, value } = action.payload;
			const updatedExclusions = state.laytimeData.exclusions;
			updatedExclusions[index] = {
				...updatedExclusions[index],
				[field]: value
			};
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					exclusions: updatedExclusions
				}
			};
		}
		case 'UPDATE_EXCLUSION_DATE': {
			if (!state.laytimeData) return state;
			const { index, field, date } = action.payload;
			const updatedExclusions = state.laytimeData.exclusions;
			updatedExclusions[index] = {
				...updatedExclusions[index],
				[field]: date.toISOString(),
				[field === 'dateFrom' ? 'from' : 'to']: format(date, 'HH:mm')
			};
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					exclusions: updatedExclusions
				}
			};
		}
		case 'ADD_EXCLUSION': {
			if (!state.laytimeData) return state;
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					exclusions: [...state.laytimeData.exclusions, action.payload]
				}
			};
		}
		case 'DELETE_EXCLUSIONS': {
			if (!state.laytimeData) return state;
			const updatedExclusions = state.laytimeData.exclusions.filter(e => !action.payload.includes(e.id));
			return {
				...state,
				laytimeData: {
					...state.laytimeData,
					exclusions: updatedExclusions
				}
			};
		}
		case 'MARK_EVENTS_FOR_DELETION': {
			return {
				...state,
				delete: {
					...state.delete,
					events: [...state.delete.events, ...action.payload]
				}
			};
		}
		case 'CLEAR_EVENTS_TO_DELETE': {
			return {
				...state,
				delete: {
					...state.delete,
					events: []
				}
			};
		}
		case 'MARK_EXCLUSIONS_FOR_DELETION': {
			return {
				...state,
				delete: {
					...state.delete,
					exclusions: [...state.delete.exclusions, ...action.payload]
				}
			};
		}
		case 'CLEAR_EXCLUSIONS_TO_DELETE': {
			return {
				...state,
				delete: {
					...state.delete,
					exclusions: []
				}
			};
		}
		default:
			return state;
	}
}

interface LaytimeProviderProps {
	children: ReactNode;
}

export function LaytimeProvider({ children }: LaytimeProviderProps) {
	const initialState: LaytimeContextState = {
		laytimeData: null,
		charterers: [],
		ports: [],
		delete: {
			events: [],
			exclusions: []
		}
	};

	const [state, dispatch] = useReducer(laytimeReducer, initialState);

	return <LaytimeContext.Provider value={{ state, dispatch }}>{children}</LaytimeContext.Provider>;
}

export function useLaytimeContext() {
	const context = useContext(LaytimeContext);
	if (context === undefined) {
		throw new Error('useLaytimeContext must be used within a LaytimeProvider');
	}
	return context;
}
