import { DollarSign, Scale, Timer } from 'lucide-react';
import PendingDaLean from './content/pending-da-lean';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function ApprovalsWidget2({ className = '' }) {
	return (
		<Card className={className}>
			<CardContent className="p-0">
				<div className="grid grid-cols-12">
					<div className="col-span-12 flex gap-0 border-r xl:col-span-4 xl:flex-col">
						<div className="truncate px-6 py-6 font-medium">Pending approvals</div>
						<div className="grid grid-cols-3 items-center gap-2 px-4 py-2 xl:grid-cols-1">
							<Button
								variant="ghost"
								className="border-primary justify-start gap-4 rounded-xl border-2 bg-transparent px-4 py-4 xl:h-auto"
							>
								<div className="flex flex-1 items-center gap-3">
									<div className="text-xl leading-none">5</div>
									<Separator orientation="vertical" className="h-4" />
									<DollarSign className="text-muted-foreground" />
									<div className="text-muted-foreground hidden text-left text-xs xl:block">
										Disbursements
									</div>
								</div>
							</Button>
							<Button
								variant="ghost"
								className="justify-start gap-4 rounded-xl bg-transparent px-4 py-4 xl:h-auto"
							>
								<div className="flex flex-1 items-center gap-3">
									<div className="text-xl leading-none">2</div>
									<Separator orientation="vertical" className="h-4" />
									<Scale className="text-muted-foreground" />
									<div className="text-muted-foreground hidden text-left text-xs xl:block">
										Laytimes
									</div>
								</div>
							</Button>
							<Button
								variant="ghost"
								className="justify-start gap-4 rounded-xl bg-transparent px-4 py-4 xl:h-auto"
							>
								<div className="flex flex-1 items-center gap-3">
									<div className="text-xl leading-none">2</div>
									<Separator orientation="vertical" className="h-4" />
									<Timer className="text-muted-foreground" />
									<div className="text-muted-foreground hidden text-left text-xs xl:block">SOFs</div>
								</div>
							</Button>
						</div>
					</div>
					<div className="col-span-12 px-4 py-4 xl:col-span-8">
						<div className="text-muted-foreground px-2 py-2 text-xs font-medium">DAs Pending Approval</div>
						<PendingDaLean hasApprove />
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
