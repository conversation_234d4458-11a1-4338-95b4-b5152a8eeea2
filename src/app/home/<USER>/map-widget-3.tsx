import * as React from 'react';

import {
	Anchor,
	BriefcaseBusiness,
	CheckCircle,
	FileSpreadsheet,
	MoveLeft,
	Navigation,
	Plus,
	ShieldCheck
} from 'lucide-react';
import MapSidePanel1 from './map/map-side-panel-1';
import MapApprovals from './map/map-approvals';
import MapFleetProgress from './map/map-fleet-progress';
import MapPortNews from './map/map-port-news';
import MapVesselDetails from './map/map-vessel-details';
import MapPortDetails from './map/map-port-details';
import MapCompanyDetails from './map/map-company-details';
import MapSearch from './map/map-search';
import HeadingWidget1 from './heading-widget-1';
import MapStatusChanges from './map/map-status-changes';
import MapArrivalsDepartures from './map/map-arrivals-departures';
import MapStoppages from './map/map-stoppages';
import MapDelays from './map/map-delays';
import MapDisbursements from './map/map-disbursements';
import MapLaytimes from './map/map-laytimes';
import MapSOFs from './map/map-sofs';
import MapToolbarPrimary from './map/map-toolbar-primary';
import MapToolbarSecondary from './map/map-toolbar-secondary';
import MapPins from './map/map-pins';
import MapVesselDetails1 from './map/map-vessel-details1';
import mapImage from '@/static/media/img-map-full-bg.svg';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { AspectRatio } from '@/components/ui/aspect-ratio';

export default function MapWidget3({ className = '' }) {
	const [approvalsOpen, setApprovalsOpen] = React.useState(false);
	const [fleetOpen, setFleetOpen] = React.useState(false);
	const [newsOpen, setNewsOpen] = React.useState(false);
	const [vesselOpen, setVesselOpen] = React.useState(false);
	const [vessel1Open, setVessel1Open] = React.useState(false);
	const [portOpen, setPortOpen] = React.useState(false);
	const [companyOpen, setCompanyOpen] = React.useState(false);
	const [disbursementsOpen, setDisbursementsOpen] = React.useState(false);
	const [laytimesOpen, setLaytimesOpen] = React.useState(false);
	const [sofsOpen, setSofsOpen] = React.useState(false);
	const [statusOpen, setStatusOpen] = React.useState(false);
	const [arrivalsOpen, setArrivalsOpen] = React.useState(false);
	const [stoppagesOpen, setStoppagesOpen] = React.useState(false);
	const [delaysOpen, setDelaysOpen] = React.useState(false);

	const closeAllPanels = React.useCallback(() => {
		setApprovalsOpen(false);
		setFleetOpen(false);
		setNewsOpen(false);
		setVesselOpen(false);
		setVessel1Open(false);
		setPortOpen(false);
		setCompanyOpen(false);
		setDisbursementsOpen(false);
		setLaytimesOpen(false);
		setSofsOpen(false);
		setStatusOpen(false);
		setArrivalsOpen(false);
		setStoppagesOpen(false);
		setDelaysOpen(false);
	}, []);

	const openPanel = React.useCallback(
		(setter: (value: boolean) => void) => {
			closeAllPanels();
			setter(true);
		},
		[closeAllPanels]
	);

	return (
		<div className={`relative h-screen ${className}`}>
			<AspectRatio
				ratio={16 / 9}
				className={`overflow-hidden rounded-xl bg-[url(${mapImage})] bg-cover bg-center`}
			>
				<HeadingWidget1 className="from-background to-background/0 bg-linear-to-b from-30% pb-8" />
				<div className="grid w-full grid-cols-3 gap-2 px-6">
					<div className="flex items-center gap-2">
						<Button variant="ghost" size="xs" className="bg-accent/50 border">
							<BriefcaseBusiness className="text-primary" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">9</span>
								<span className="text-muted-foreground hidden xl:block">Active</span>
							</div>
						</Button>
						<Button variant="ghost" size="xs">
							<Navigation className="text-amber-400" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">6</span>
								<span className="text-muted-foreground hidden xl:block">En route</span>
							</div>
						</Button>
						<Button variant="ghost" size="xs">
							<Anchor className="text-sky-400" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">3</span>
								<span className="text-muted-foreground hidden xl:block">In port</span>
							</div>
						</Button>
					</div>
					<div className="grid grid-cols-12 gap-2">
						<div className="col-span-1"></div>
						<MapSearch
							setVesselOpen={open => open && openPanel(() => setVesselOpen(open))}
							setPortOpen={open => open && openPanel(() => setPortOpen(open))}
							setCompanyOpen={open => open && openPanel(() => setCompanyOpen(open))}
							setApprovalsOpen={open => open && openPanel(() => setApprovalsOpen(open))}
							// setFleetOpen={(open) => open && openPanel(() => setFleetOpen(open))}
							setNewsOpen={open => open && openPanel(() => setNewsOpen(open))}
							setStatusOpen={open => open && openPanel(() => setStatusOpen(open))}
							setArrivalsOpen={open => open && openPanel(() => setArrivalsOpen(open))}
							setStoppagesOpen={open => open && openPanel(() => setStoppagesOpen(open))}
							setDelaysOpen={open => open && openPanel(() => setDelaysOpen(open))}
							setVessel1Open={open => open && openPanel(() => setVessel1Open(open))}
						/>

						<div className="col-span-1 text-center">
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button variant="outline" size="icon" className="text-primary-foreground">
										<Plus />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-[150px]">
									<DropdownMenuItem>
										<CheckCircle />
										Appointment
									</DropdownMenuItem>
									<DropdownMenuItem>
										<FileSpreadsheet />
										Quick Quote
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					</div>
					<div></div>
				</div>

				<MapPins
					setVesselOpen={open => open && openPanel(() => setVesselOpen(open))}
					setVessel1Open={open => open && openPanel(() => setVessel1Open(open))}
				/>

				<MapToolbarPrimary
					setPortOpen={open => open && openPanel(() => setPortOpen(open))}
					setApprovalsOpen={open => open && openPanel(() => setApprovalsOpen(open))}
					setVesselOpen={open => open && openPanel(() => setVesselOpen(open))}
					setStatusOpen={open => open && openPanel(() => setStatusOpen(open))}
					setArrivalsOpen={open => open && openPanel(() => setArrivalsOpen(open))}
					setStoppagesOpen={open => open && openPanel(() => setStoppagesOpen(open))}
					setDelaysOpen={open => open && openPanel(() => setDelaysOpen(open))}
					setNewsOpen={open => open && openPanel(() => setNewsOpen(open))}
					setCompanyOpen={open => open && openPanel(() => setCompanyOpen(open))}
				/>
				<MapToolbarSecondary />

				{/* Approvals Panel */}
				<MapSidePanel1 title="Pending Approvals" isOpen={approvalsOpen} onClose={() => setApprovalsOpen(false)}>
					<MapApprovals
						setDisbursementsOpen={open => open && openPanel(() => setDisbursementsOpen(open))}
						setLaytimesOpen={open => open && openPanel(() => setLaytimesOpen(open))}
						setSofsOpen={open => open && openPanel(() => setSofsOpen(open))}
						setApprovalsOpen={open => open && openPanel(() => setApprovalsOpen(open))}
					/>
				</MapSidePanel1>

				{/* Fleet Progress Panel */}
				<MapSidePanel1 title="Fleet Progress" isOpen={fleetOpen} onClose={() => setFleetOpen(false)}>
					<MapFleetProgress
						setStatusOpen={open => open && openPanel(() => setStatusOpen(open))}
						setArrivalsOpen={open => open && openPanel(() => setArrivalsOpen(open))}
						setStoppagesOpen={open => open && openPanel(() => setStoppagesOpen(open))}
						setDelaysOpen={open => open && openPanel(() => setDelaysOpen(open))}
						setVesselOpen={open => open && openPanel(() => setVesselOpen(open))}
						setPortOpen={open => open && openPanel(() => setPortOpen(open))}
					/>
				</MapSidePanel1>

				{/* Vessel Details En Route Panel */}
				<MapSidePanel1
					title={
						<div className="flex items-center gap-4">
							<Navigation className="size-4 text-amber-500" />
							<div className="">
								<div>mv Los Testigos</div>
								<div className="text-muted-foreground text-xs font-normal">IMO 9385881</div>
							</div>
						</div>
					}
					isOpen={vesselOpen}
					onClose={() => setVesselOpen(false)}
				>
					<MapVesselDetails setPortOpen={open => open && openPanel(() => setPortOpen(open))} />
				</MapSidePanel1>

				{/* Vessel Details In Port Panel */}
				<MapSidePanel1
					title={
						<div className="flex items-center gap-4">
							<Anchor className="size-4 text-sky-500" />
							<div className="">
								<div>mv Discoverer</div>
								<div className="text-muted-foreground text-xs font-normal">IMO 9385881</div>
							</div>
						</div>
					}
					isOpen={vessel1Open}
					onClose={() => setVessel1Open(false)}
				>
					<MapVesselDetails1 setPortOpen={open => open && openPanel(() => setPortOpen(open))} />
				</MapSidePanel1>

				{/* Port Details Panel */}
				<MapSidePanel1
					title={
						<div className="flex items-center gap-4">
							<Anchor className="text-primary size-4" />
							<div className="">
								<div>Alappuzha, IN</div>
								<div className="text-muted-foreground text-xs font-normal">INDALPZA</div>
							</div>
						</div>
					}
					isOpen={portOpen}
					onClose={() => setPortOpen(false)}
				>
					<MapPortDetails setNewsOpen={open => open && openPanel(() => setNewsOpen(open))} />
				</MapSidePanel1>

				{/* Company Details Panel */}
				<MapSidePanel1
					title={
						<div className="flex items-center gap-4">
							<ShieldCheck className="size-4 text-emerald-500" />
							<div>Wilhelmsen Port Services</div>
						</div>
					}
					isOpen={companyOpen}
					onClose={() => setCompanyOpen(false)}
				>
					<MapCompanyDetails />
				</MapSidePanel1>

				{/* Disbursements Panel */}
				<MapSidePanel1
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setDisbursementsOpen(false);
									setApprovalsOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Pending Disbursements</div>
						</div>
					}
					isOpen={disbursementsOpen}
					onClose={() => setDisbursementsOpen(false)}
				>
					<MapDisbursements />
				</MapSidePanel1>

				{/* Laytimes Panel */}
				<MapSidePanel1
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setLaytimesOpen(false);
									setApprovalsOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Pending Laytimes</div>
						</div>
					}
					isOpen={laytimesOpen}
					onClose={() => setLaytimesOpen(false)}
				>
					<MapLaytimes />
				</MapSidePanel1>

				{/* SOFs Panel */}
				<MapSidePanel1
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setSofsOpen(false);
									setApprovalsOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Pending SOFs</div>
						</div>
					}
					isOpen={sofsOpen}
					onClose={() => setSofsOpen(false)}
				>
					<MapSOFs />
				</MapSidePanel1>

				{/* Status Changes Panel */}
				<MapSidePanel1 title="Status Changes" isOpen={statusOpen} onClose={() => setStatusOpen(false)}>
					<MapStatusChanges />
				</MapSidePanel1>

				{/* 48hrs Arrivals/Departures Panel */}
				<MapSidePanel1
					title="48 hrs arrivals / departures"
					isOpen={arrivalsOpen}
					onClose={() => setArrivalsOpen(false)}
				>
					<MapArrivalsDepartures />
				</MapSidePanel1>

				{/* Stoppages Panel */}
				<MapSidePanel1 title="Latest stoppages" isOpen={stoppagesOpen} onClose={() => setStoppagesOpen(false)}>
					<MapStoppages />
				</MapSidePanel1>

				{/* Delays Panel */}
				<MapSidePanel1 title="Delays" isOpen={delaysOpen} onClose={() => setDelaysOpen(false)}>
					<MapDelays />
				</MapSidePanel1>

				{/* Port News Panel */}
				<MapSidePanel1 title="Port News" isOpen={newsOpen} onClose={() => setNewsOpen(false)}>
					<MapPortNews />
				</MapSidePanel1>
			</AspectRatio>
		</div>
	);
}
