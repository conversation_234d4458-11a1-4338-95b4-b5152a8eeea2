import * as React from 'react';

import { Anchor, BriefcaseBusiness, Clock, MoveLeft, Navigation, Route, Rss, ShieldCheck } from 'lucide-react';
import MapSidePanel from './map/map-side-panel';
import MapApprovals from './map/map-approvals';
import MapFleetProgress from './map/map-fleet-progress';
import MapPortNews from './map/map-port-news';
import MapVesselDetails from './map/map-vessel-details';
import MapPortDetails from './map/map-port-details';
import MapCompanyDetails from './map/map-company-details';
import MapSearch from './map/map-search';
import HeadingWidget1 from './heading-widget-1';
import MapStatusChanges from './map/map-status-changes';
import MapArrivalsDepartures from './map/map-arrivals-departures';
import MapStoppages from './map/map-stoppages';
import MapDelays from './map/map-delays';
import MapDisbursements from './map/map-disbursements';
import MapLaytimes from './map/map-laytimes';
import MapSOFs from './map/map-sofs';
import mapImage from '@/static/media/img-map-full.svg';
import { Button } from '@/components/ui/button';
import { AspectRatio } from '@/components/ui/aspect-ratio';

export default function MapWidget2({ className = '' }) {
	const [approvalsOpen, setApprovalsOpen] = React.useState(false);
	const [fleetOpen, setFleetOpen] = React.useState(false);
	const [newsOpen, setNewsOpen] = React.useState(false);
	const [vesselOpen, setVesselOpen] = React.useState(false);
	const [_, setVessel1Open] = React.useState(false);
	const [portOpen, setPortOpen] = React.useState(false);
	const [companyOpen, setCompanyOpen] = React.useState(false);
	const [disbursementsOpen, setDisbursementsOpen] = React.useState(false);
	const [laytimesOpen, setLaytimesOpen] = React.useState(false);
	const [sofsOpen, setSofsOpen] = React.useState(false);
	const [statusOpen, setStatusOpen] = React.useState(false);
	const [arrivalsOpen, setArrivalsOpen] = React.useState(false);
	const [stoppagesOpen, setStoppagesOpen] = React.useState(false);
	const [delaysOpen, setDelaysOpen] = React.useState(false);

	return (
		<div className={`relative h-screen ${className}`}>
			<AspectRatio
				ratio={16 / 9}
				className={`overflow-hidden rounded-xl bg-[url(${mapImage})] bg-cover bg-center`}
			>
				<HeadingWidget1 className="from-background to-background/0 bg-linear-to-b from-30% pb-8" />
				<div className="grid w-full grid-cols-3 px-6">
					<div className="flex items-center gap-2">
						<Button variant="ghost" size="xs" className="bg-accent/50 border">
							<BriefcaseBusiness className="text-primary" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">16</span>
								<span className="text-muted-foreground hidden xl:block">Active</span>
							</div>
						</Button>
						<Button variant="ghost" size="xs">
							<Navigation className="text-amber-400" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">7</span>
								<span className="text-muted-foreground hidden xl:block">En route</span>
							</div>
						</Button>
						<Button variant="ghost" size="xs">
							<Anchor className="text-sky-400" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">9</span>
								<span className="text-muted-foreground hidden xl:block">In port</span>
							</div>
						</Button>
					</div>
					<div className="grid grid-cols-12">
						<div className="col-span-1"></div>
						<MapSearch
							setVesselOpen={setVesselOpen}
							setVessel1Open={setVessel1Open}
							setPortOpen={setPortOpen}
							setCompanyOpen={setCompanyOpen}
							setApprovalsOpen={setApprovalsOpen}
							// setFleetOpen={setFleetOpen}
							setNewsOpen={setNewsOpen}
							setStatusOpen={setStatusOpen}
							setArrivalsOpen={setArrivalsOpen}
							setStoppagesOpen={setStoppagesOpen}
							setDelaysOpen={setDelaysOpen}
						/>
						<div className="col-span-1"></div>
					</div>
					<div className="flex items-center justify-end gap-2">
						<Button variant="ghost" size="xs" onClick={() => setApprovalsOpen(true)}>
							<Clock className="text-muted-foreground" />
							<div className="flex items-center gap-1">
								<span className="text-sm font-bold">9</span>
								<span className="text-muted-foreground hidden xl:block">Approvals</span>
							</div>
						</Button>
						<Button variant="ghost" size="xs" onClick={() => setFleetOpen(true)}>
							<Route className="text-muted-foreground" />
							<div className="flex items-center">
								<span className="text-muted-foreground hidden xl:block">Fleet progress</span>
							</div>
						</Button>
						<Button variant="ghost" size="xs" className="relative" onClick={() => setNewsOpen(true)}>
							<Rss className="text-muted-foreground" />
							<div className="flex items-center">
								<span className="text-muted-foreground hidden xl:block">News</span>
							</div>
						</Button>
					</div>
				</div>

				{/* Approvals Panel */}
				<MapSidePanel title="Pending Approvals" isOpen={approvalsOpen} onClose={() => setApprovalsOpen(false)}>
					<MapApprovals
						setDisbursementsOpen={setDisbursementsOpen}
						setLaytimesOpen={setLaytimesOpen}
						setSofsOpen={setSofsOpen}
						setApprovalsOpen={setApprovalsOpen}
					/>
				</MapSidePanel>

				{/* Fleet Progress Panel */}
				<MapSidePanel title="Fleet Progress" isOpen={fleetOpen} onClose={() => setFleetOpen(false)}>
					<MapFleetProgress
						setStatusOpen={setStatusOpen}
						setArrivalsOpen={setArrivalsOpen}
						setStoppagesOpen={setStoppagesOpen}
						setDelaysOpen={setDelaysOpen}
						setVesselOpen={setVesselOpen}
						setPortOpen={setPortOpen}
					/>
				</MapSidePanel>

				{/* Vessel Details Panel */}
				<MapSidePanel
					title={
						<div className="flex items-center gap-4">
							<Navigation className="size-4 text-amber-500" />
							<div className="">
								<div>mv Los Testigos</div>
								<div className="text-muted-foreground text-xs font-normal">IMO 9385881</div>
							</div>
						</div>
					}
					isOpen={vesselOpen}
					onClose={() => setVesselOpen(false)}
				>
					<MapVesselDetails setPortOpen={setPortOpen} />
				</MapSidePanel>

				{/* Port Details Panel */}
				<MapSidePanel
					title={
						<div className="flex items-center gap-4">
							<Anchor className="text-primary size-4" />
							<div className="">
								<div>Alappuzha, IN</div>
								<div className="text-muted-foreground text-xs font-normal">INDALPZA</div>
							</div>
						</div>
					}
					isOpen={portOpen}
					onClose={() => setPortOpen(false)}
				>
					<MapPortDetails setNewsOpen={setNewsOpen} />
				</MapSidePanel>

				{/* Company Details Panel */}
				<MapSidePanel
					title={
						<div className="flex items-center gap-4">
							<ShieldCheck className="size-4 text-emerald-500" />
							<div>Wilhelmsen Port Services</div>
						</div>
					}
					isOpen={companyOpen}
					onClose={() => setCompanyOpen(false)}
				>
					<MapCompanyDetails />
				</MapSidePanel>

				{/* Disbursements Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setDisbursementsOpen(false);
									setApprovalsOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Pending Disbursements</div>
						</div>
					}
					isOpen={disbursementsOpen}
					onClose={() => setDisbursementsOpen(false)}
				>
					<MapDisbursements />
				</MapSidePanel>

				{/* Laytimes Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setLaytimesOpen(false);
									setApprovalsOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Pending Laytimes</div>
						</div>
					}
					isOpen={laytimesOpen}
					onClose={() => setLaytimesOpen(false)}
				>
					<MapLaytimes />
				</MapSidePanel>

				{/* SOFs Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setSofsOpen(false);
									setApprovalsOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Pending SOFs</div>
						</div>
					}
					isOpen={sofsOpen}
					onClose={() => setSofsOpen(false)}
				>
					<MapSOFs />
				</MapSidePanel>

				{/* Status Changes Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setStatusOpen(false);
									setFleetOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Status Changes</div>
						</div>
					}
					isOpen={statusOpen}
					onClose={() => setStatusOpen(false)}
				>
					<MapStatusChanges />
				</MapSidePanel>

				{/* 48 Arrivals/Departures Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setArrivalsOpen(false);
									setFleetOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>48 hrs arrivals / departures</div>
						</div>
					}
					isOpen={arrivalsOpen}
					onClose={() => setArrivalsOpen(false)}
				>
					<MapArrivalsDepartures />
				</MapSidePanel>

				{/* Stoppages Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setStoppagesOpen(false);
									setFleetOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Latest stoppages</div>
						</div>
					}
					isOpen={stoppagesOpen}
					onClose={() => setStoppagesOpen(false)}
				>
					<MapStoppages />
				</MapSidePanel>

				{/* Delays Panel */}
				<MapSidePanel
					hasClose={false}
					title={
						<div className="-mx-2 flex items-center gap-2">
							<Button
								variant="ghost"
								size="xs"
								onClick={() => {
									setDelaysOpen(false);
									setFleetOpen(true);
								}}
							>
								<MoveLeft className="text-muted-foreground" />
							</Button>
							<div>Delays</div>
						</div>
					}
					isOpen={delaysOpen}
					onClose={() => setDelaysOpen(false)}
				>
					<MapDelays />
				</MapSidePanel>

				{/* Port News Panel */}
				<MapSidePanel title="Port News" isOpen={newsOpen} onClose={() => setNewsOpen(false)}>
					<MapPortNews />
				</MapSidePanel>
			</AspectRatio>
		</div>
	);
}
