import { Anchor, BriefcaseBusiness, Navigation, Sailboat } from 'lucide-react';
import mapImage from '@/static/media/img-map.svg';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function MapWidget1({ className = '' }) {
	return (
		<Card className={className}>
			<CardHeader className="flex flex-1 flex-row items-center justify-between gap-2 space-y-0 border-b py-4">
				<CardTitle className="flex-1">Port Calls</CardTitle>
				<Button variant="ghost" size="xs" className="">
					<BriefcaseBusiness className="text-primary" />
					<div className="flex items-center gap-1">
						<span className="text-sm font-bold">37</span>
						<span className="text-muted-foreground hidden md:block">Port calls</span>
					</div>
				</Button>
				<Button variant="ghost" size="xs" className="">
					<Navigation className="text-amber-400" />
					<div className="flex items-center gap-1">
						<span className="text-sm font-bold">6</span>
						<span className="text-muted-foreground hidden md:block">En route</span>
					</div>
				</Button>
				<Button variant="ghost" size="xs" className="">
					<Anchor className="text-sky-400" />
					<div className="flex items-center gap-1">
						<span className="text-sm font-bold">8</span>
						<span className="text-muted-foreground hidden md:block">In port</span>
					</div>
				</Button>
				<Button variant="ghost" size="xs" className="">
					<Sailboat className="text-emerald-400" />
					<div className="flex items-center gap-1">
						<span className="text-sm font-bold">23</span>
						<span className="text-muted-foreground hidden md:block">Sailed</span>
					</div>
				</Button>
			</CardHeader>
			<CardContent className="p-0">
				<AspectRatio ratio={20 / 9}>
					<img src={mapImage} alt="map" className="rounded-b-xl object-cover" />
				</AspectRatio>
			</CardContent>
		</Card>
	);
}
