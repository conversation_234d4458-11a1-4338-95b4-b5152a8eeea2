import PendingDaLean from './content/pending-da-lean';
import PendingLaytimeLean from './content/pending-laytime-lean';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

export default function ApprovalsWidget3({ className = '' }) {
	return (
		<Card className={className}>
			<Tabs defaultValue="da">
				<CardHeader className="flex flex-row items-center space-y-0 py-4">
					<CardTitle className="flex-1">Pending approvals</CardTitle>
					<TabsList className="flex h-auto gap-2 bg-transparent p-0">
						<TabsTrigger
							value="da"
							className="hover:bg-accent data-[state=active]:bg-secondary h-auto gap-2 border border-transparent px-2 py-1 text-xs data-[state=active]:border-slate-700"
						>
							<Badge variant="secondary" className="px-1.5 py-0 text-sm">
								5
							</Badge>
							Disbursements
						</TabsTrigger>
						<TabsTrigger
							value="laytime"
							className="hover:bg-accent data-[state=active]:bg-secondary h-auto gap-2 border border-transparent px-2 py-1 text-xs data-[state=active]:border-slate-700"
						>
							<Badge variant="secondary" className="px-1.5 py-0 text-sm">
								3
							</Badge>
							Laytimes
						</TabsTrigger>
					</TabsList>
				</CardHeader>
				<CardContent className="px-4">
					<TabsContent value="da">
						<PendingDaLean hasApprove />
					</TabsContent>
					<TabsContent value="laytime">
						<PendingLaytimeLean hasApprove />
					</TabsContent>
				</CardContent>
			</Tabs>
		</Card>
	);
}
