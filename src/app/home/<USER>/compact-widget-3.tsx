import { CheckCircle, FileSpreadsheet, Plus, Search } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export default function CompactWidget3({ className = '' }) {
	return (
		<div className={className}>
			<Card className="border-none bg-transparent shadow-none">
				<CardContent className="p-0">
					<div className="flex flex-1 items-center justify-end gap-2">
						<Button
							variant="secondary"
							size="sm"
							className="text-muted-foreground flex-1 justify-start border"
						>
							<Search />
							Search or Аsk AI
						</Button>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button size="sm">
									<Plus />
									New
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end" className="w-[150px]">
								<DropdownMenuItem>
									<CheckCircle />
									Appointment
								</DropdownMenuItem>
								<DropdownMenuItem>
									<FileSpreadsheet />
									Quick Quote
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
