import { Timer } from 'lucide-react';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';

interface PendingWidgetProps {
	hasApprove?: boolean;
	bordered?: boolean;
}

export default function PendingSOFLean({ hasApprove = true, bordered = true }: PendingWidgetProps) {
	return (
		<Table className="text-xs">
			<TableBody>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="flex flex-row items-center gap-2">
							<div className="text-nowrap">mv Ultra Quality</div>
						</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center gap-2">
							<Timer className="text-muted-foreground size-4"></Timer>
							<span className="text-xs text-nowrap">3d : 12h : 35m</span>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="text-nowrap">mv Meridiaan</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center gap-2">
							<Timer className="text-muted-foreground size-4"></Timer>
							<span className="text-xs text-nowrap">3d : 12h : 35m</span>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
			</TableBody>
		</Table>
	);
}
