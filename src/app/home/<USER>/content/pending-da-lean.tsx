import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface PendingWidgetProps {
	hasApprove?: boolean;
	bordered?: boolean;
}

export default function PendingDaLean({ hasApprove = true, bordered = true }: PendingWidgetProps) {
	return (
		<Table className="text-xs">
			<TableBody>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="flex flex-row items-center gap-2">
							<div className="text-nowrap">mv Ultra Quality</div>
						</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center justify-end gap-2">
							<span className="text-muted-foreground text-xs uppercase">usd</span>
							<span className="font-medium">26,522.00</span>
							<Badge variant="outline" className="rounded-full border-amber-500">
								<span>PDA</span>
							</Badge>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="text-nowrap">mv Meridiaan</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center justify-end gap-2">
							<span className="text-muted-foreground text-xs uppercase">usd</span>
							<span className="font-medium">4,980.00</span>
							<Badge variant="outline" className="rounded-full border-blue-500">
								<span>SDA</span>
							</Badge>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="text-nowrap">mv Oceanic Atlantic</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center justify-end gap-2">
							<span className="text-muted-foreground text-xs uppercase">usd</span>
							<span className="font-medium">41,500.50</span>
							<Badge variant="outline" className="rounded-full border-emerald-500">
								<span>FDA</span>
							</Badge>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="flex flex-row items-center gap-2">
							<div className="text-nowrap">mv Horizon</div>
						</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center justify-end gap-2">
							<span className="text-muted-foreground text-xs uppercase">usd</span>
							<span className="font-medium">28,745.80</span>
							<Badge variant="outline" className="rounded-full border-indigo-500">
								<span>DDA</span>
							</Badge>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
				<TableRow className={bordered ? 'border-b' : 'border-b-0'}>
					<TableCell className="font-medium">
						<div className="text-nowrap">mv Oceanic Atlantic</div>
					</TableCell>
					<TableCell>&nbsp;</TableCell>
					<TableCell>
						<div className="flex items-center justify-end gap-2">
							<span className="text-muted-foreground text-xs uppercase">usd</span>
							<span className="font-medium">41,500.50</span>
							<Badge variant="outline" className="rounded-full border-emerald-500">
								<span>FDA</span>
							</Badge>
						</div>
					</TableCell>
					{hasApprove && (
						<TableCell className="text-right">
							<Button size="xs" variant="secondary">
								Approve
							</Button>
						</TableCell>
					)}
				</TableRow>
			</TableBody>
		</Table>
	);
}
