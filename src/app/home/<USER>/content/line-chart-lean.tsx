import { Line, LineChart } from 'recharts';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';

const data = [
	{
		average: 400000,
		today: 240000
	},
	{
		average: 300000,
		today: 139000
	},
	{
		average: 200000,
		today: 480000
	},
	{
		average: 278000,
		today: 390000
	},
	{
		average: 189000,
		today: 480000
	},
	{
		average: 239000,
		today: 380000
	},
	{
		average: 349000,
		today: 430000
	}
];

const chartConfig = {
	today: {
		label: 'Today',
		color: 'hsl(var(--primary))'
	},
	average: {
		label: 'Avg.',
		color: 'hsl(var(--chart-3))'
	}
} satisfies ChartConfig;

export default function LineChartLean({ className = '' }) {
	return (
		<ChartContainer config={chartConfig} className={className}>
			<LineChart
				data={data}
				margin={{
					top: 5,
					right: 10,
					left: 10,
					bottom: 0
				}}
			>
				<Line
					type="monotone"
					strokeWidth={1.5}
					dataKey="average"
					stroke="var(--color-average)"
					dot={{
						r: 2,
						fill: 'var(--color-average)'
					}}
					activeDot={{
						r: 4
					}}
				/>
				<Line
					type="monotone"
					dataKey="today"
					strokeWidth={1.5}
					stroke="var(--color-today)"
					dot={{
						r: 2,
						style: { fill: 'var(--color-today)' }
					}}
					activeDot={{
						r: 4
					}}
				/>
				<ChartTooltip content={<ChartTooltipContent />} />
			</LineChart>
		</ChartContainer>
	);
}
