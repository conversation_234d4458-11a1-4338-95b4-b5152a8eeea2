import { Check<PERSON><PERSON><PERSON>, Clock, FileSpreadsheet, Plus, Search } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { But<PERSON> } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export default function CompactWidget1({ className = '' }) {
	return (
		<div className={className}>
			<Card>
				<CardContent className="px-4 py-3">
					<div className="flex h-6 items-center gap-3">
						<div className="flex flex-1 items-center gap-2">
							<Clock className="size-4 text-amber-400" />
							<div className="font-medium">Pending approval</div>
							<Separator className="flex-1" />
						</div>
						<Button variant="ghost">
							<span className="text-xl font-bold">4</span>
							<span className="text-muted-foreground">DA&apos;s</span>
						</Button>
						<Separator orientation="vertical" />
						<Button variant="ghost">
							<span className="text-xl font-bold">2</span>
							<span className="text-muted-foreground">SOF&apos;s</span>
						</Button>
						<Separator orientation="vertical" />
						<Button variant="ghost">
							<span className="text-xl font-bold">3</span>
							<span className="text-muted-foreground">Laytime</span>
						</Button>
						<div className="flex flex-1 items-center justify-end gap-2">
							<Separator className="flex-1 bg-transparent" />
							<Button variant="secondary" size="sm" className="text-mute">
								<Search />
								Search or Аsk AI
							</Button>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button size="sm">
										<Plus />
										New
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-[150px]">
									<DropdownMenuItem>
										<CheckCircle />
										Appointment
									</DropdownMenuItem>
									<DropdownMenuItem>
										<FileSpreadsheet />
										Quick Quote
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
