import { ReactNode } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

interface AmountWidgetProps {
	className?: string;
	title?: string;
	amount?: string | number;
	subtitle?: string;
	children?: ReactNode;
}

export default function AmountWidget1({
	className = '',
	title = '',
	amount = '',
	subtitle = '',
	children
}: AmountWidgetProps) {
	return (
		<Card className={className}>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="text-2xl font-bold">{amount}</div>
				<p className="text-muted-foreground text-xs">{subtitle}</p>
				{children && <div className="mt-2">{children}</div>}
			</CardContent>
		</Card>
	);
}
