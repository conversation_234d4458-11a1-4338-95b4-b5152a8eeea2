import { Check<PERSON><PERSON><PERSON>, Clock, FileSpreadsheet, Plus, Search } from 'lucide-react';
import PendingLaytimeLean from './content/pending-laytime-lean';
import PendingDaLean from './content/pending-da-lean';
import PendingSOFLean from './content/pending-sof-lean';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuList,
	NavigationMenuTrigger
} from '@/components/ui/navigation-menu';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export default function CompactWidget2({ className = '' }) {
	return (
		<div className={className}>
			<Card>
				<CardContent className="px-4 py-2">
					<div className="flex flex-wrap items-center gap-3">
						<div className="flex flex-1 items-center gap-2">
							<Clock className="size-4 text-amber-400" />
							<div className="font-medium">Pending approval</div>
						</div>
						<NavigationMenu className="flex-1">
							<NavigationMenuList className="gap-0">
								<NavigationMenuItem className="flex-1">
									<NavigationMenuTrigger className="bg-transparent">
										<div className="flex items-center gap-2">
											<div className="text-xl font-bold">5</div>
											<div className="text-muted-foreground text-xs">Disbursements</div>
										</div>
									</NavigationMenuTrigger>
									<NavigationMenuContent className="min-w-sm">
										<div className="text-muted-foreground px-2 py-2 text-xs font-medium">
											Disbursements Pending Approval
										</div>
										<PendingDaLean hasApprove />
									</NavigationMenuContent>
								</NavigationMenuItem>
								<NavigationMenuItem className="flex-1">
									<NavigationMenuTrigger className="bg-transparent">
										<div className="flex items-center gap-2">
											<div className="text-xl font-bold">2</div>
											<div className="text-muted-foreground text-xs">SOFs</div>
										</div>
									</NavigationMenuTrigger>
									<NavigationMenuContent className="min-w-sm">
										<div className="text-muted-foreground px-2 py-2 text-xs font-medium">
											SOFs Pending Approval
										</div>
										<PendingSOFLean />
									</NavigationMenuContent>
								</NavigationMenuItem>
								<NavigationMenuItem className="flex-1">
									<NavigationMenuTrigger className="bg-transparent">
										<div className="flex items-center gap-2">
											<div className="text-xl font-bold">2</div>
											<div className="text-muted-foreground text-xs">Laytimes</div>
										</div>
									</NavigationMenuTrigger>
									<NavigationMenuContent className="min-w-sm">
										<div className="text-muted-foreground px-2 py-2 text-xs font-medium">
											Laytimes Pending Approval
										</div>
										<PendingLaytimeLean hasApprove />
									</NavigationMenuContent>
								</NavigationMenuItem>
							</NavigationMenuList>
						</NavigationMenu>
						<div className="flex flex-1 items-center justify-end gap-2">
							<Button
								variant="secondary"
								size="sm"
								className="text-muted-foreground flex-1 justify-start xl:flex-0"
							>
								<Search />
								Search or Аsk AI
							</Button>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button size="sm">
										<Plus />
										New
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-[150px]">
									<DropdownMenuItem>
										<CheckCircle />
										Appointment
									</DropdownMenuItem>
									<DropdownMenuItem>
										<FileSpreadsheet />
										Quick Quote
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
