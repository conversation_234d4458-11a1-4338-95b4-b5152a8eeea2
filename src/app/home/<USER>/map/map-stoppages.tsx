import { Button } from '@/components/ui/button';

export default function MapStoppages() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">27 Mar, Thu</div>
			<div className="grid gap-3 px-4">
				<Button
					variant="outline"
					className="hover:bg-accent/50 h-auto flex-col items-start gap-1 bg-transparent p-0 py-3"
				>
					<div className="px-3 text-left">mv Los Testigos</div>
					<div className="flex w-full items-center gap-2 px-3">
						<div className="size-1.5 rounded-full bg-red-600"></div>
						<div className="text-normal text-muted-foreground flex-1 text-left text-xs">Heavy rain</div>
						<div className="text-muted-foreground text-xs">10:00 - 12:00</div>
					</div>
				</Button>
				<Button
					variant="outline"
					className="hover:bg-accent/50 h-auto flex-col items-start gap-1 bg-transparent p-0 py-3"
				>
					<div className="px-3 text-left">mv Me<PERSON><PERSON>an</div>
					<div className="flex w-full items-center gap-2 px-3">
						<div className="size-1.5 rounded-full bg-red-600"></div>
						<div className="text-normal text-muted-foreground flex-1 text-left text-xs">Thunder storm</div>
						<div className="text-muted-foreground text-xs">16:00 - 18:00</div>
					</div>
				</Button>
			</div>
			<div className="text-muted-foreground p-6 text-sm">26 Mar, Wed</div>
			<div className="grid gap-3 px-4">
				<Button
					variant="outline"
					className="hover:bg-accent/50 h-auto flex-col items-start gap-1 bg-transparent p-0 py-3"
				>
					<div className="px-3 text-left">mv Oceanic Atlantic</div>
					<div className="flex w-full items-center gap-2 px-3">
						<div className="size-1.5 rounded-full bg-red-600"></div>
						<div className="text-normal text-muted-foreground flex-1 text-left text-xs">Light rain</div>
						<div className="text-muted-foreground text-xs">08:00 - 11:00</div>
					</div>
				</Button>
				<Button
					variant="outline"
					className="hover:bg-accent/50 h-auto flex-col items-start gap-1 bg-transparent p-0 py-3"
				>
					<div className="px-3 text-left">mv Horizon</div>
					<div className="flex w-full items-center gap-2 px-3">
						<div className="size-1.5 rounded-full bg-red-600"></div>
						<div className="text-normal text-muted-foreground flex-1 text-left text-xs">Heavy rain</div>
						<div className="text-muted-foreground text-xs">13:00 - 16:00</div>
					</div>
				</Button>
			</div>
			<div className="text-muted-foreground p-6 text-sm">25 Mar, Tue</div>
			<div className="grid gap-3 px-4">
				<Button
					variant="outline"
					className="hover:bg-accent/50 h-auto flex-col items-start gap-1 bg-transparent p-0 py-3"
				>
					<div className="px-3 text-left">mv Pioneer</div>
					<div className="flex w-full items-center gap-2 px-3">
						<div className="size-1.5 rounded-full bg-red-600"></div>
						<div className="text-normal text-muted-foreground flex-1 text-left text-xs">
							Shore breakdown
						</div>
						<div className="text-muted-foreground text-xs">12:00 - 13:30</div>
					</div>
				</Button>
			</div>
		</>
	);
}
