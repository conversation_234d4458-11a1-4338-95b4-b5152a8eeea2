import { ArrowUpRight } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import vesselImage from '@/static/media/788.jpg';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Button } from '@/components/ui/button';

export default function MapVesselDetails1({ setPortOpen }: { setPortOpen: (open: boolean) => void }) {
	return (
		<div className="grid gap-6">
			<div className="p-6 pb-0">
				<AspectRatio ratio={16 / 8}>
					<img src={vesselImage} alt="vessel" className="rounded-lg object-cover" />
				</AspectRatio>
			</div>
			<div className="text-muted-foreground px-6 text-sm">Latest voyage updates.</div>
			<div className="relative grid gap-6 px-6">
				<div className="sep absolute top-8 bottom-12 left-8 -z-10 w-[1px] bg-slate-700"></div>
				<div className="flex gap-2">
					<div className="py-1.5">
						<CircleFlag countryCode="nl" className="h-4" />
					</div>
					<div>
						<Button
							variant="ghost"
							className="h-auto p-1"
							onClick={() => {
								setPortOpen(true);
							}}
						>
							Rotterdam, NL
						</Button>
						<div className="text-muted-foreground px-1 text-xs">ATA: 27 Feb, 06:00</div>
					</div>
				</div>
				<div className="grid gap-4">
					<div className="flex items-center gap-2">
						<div className="bg-panel py-1">
							<span className="m-1.5 block size-1 rounded-full bg-slate-500"></span>
						</div>
						<div className="flex flex-1 items-center gap-2 px-1 text-sm">
							<div className="text-muted-foreground flex-1">NOR Tendered</div>
							<div className="text-muted-foreground text-xs">08:00 - 08:30</div>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<div className="bg-panel py-1">
							<span className="m-1.5 block size-1 rounded-full bg-emerald-500"></span>
						</div>
						<div className="flex flex-1 items-center gap-2 px-1 text-sm">
							<div className="text-muted-foreground flex-1">Loading</div>
							<div className="text-muted-foreground text-xs">08:30 - 14:00</div>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<div className="bg-panel py-1">
							<span className="m-1.5 block size-1 rounded-full bg-red-500"></span>
						</div>
						<div className="flex flex-1 items-center gap-2 px-1 text-sm">
							<div className="text-muted-foreground flex-1">Heavy rain</div>
							<div className="text-muted-foreground text-xs">14:00 - 18:00</div>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<div className="bg-panel py-1">
							<span className="m-1.5 block size-1 rounded-full bg-emerald-500"></span>
						</div>
						<div className="flex flex-1 items-center gap-2 px-1 text-sm">
							<div className="text-muted-foreground flex-1">Loading</div>
							<div className="text-muted-foreground text-xs">18:00 - Now</div>
						</div>
					</div>
				</div>
				<div className="flex gap-2">
					<div className="py-1.5">
						<CircleFlag countryCode="bg" className="h-4" />
					</div>
					<div>
						<Button
							variant="ghost"
							className="h-auto p-1"
							onClick={() => {
								setPortOpen(true);
							}}
						>
							Varna, BG
						</Button>
						<div className="text-muted-foreground px-1 text-xs">ETA: 16 Mar, 08:00</div>
					</div>
				</div>
			</div>
			{/* <Separator /> */}
			<div className="p-6">
				<Button size="sm">
					View port call
					<ArrowUpRight />
				</Button>
			</div>
		</div>
	);
}
