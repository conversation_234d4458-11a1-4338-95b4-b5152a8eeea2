import { ArrowUpRight, BriefcaseBusiness, Navigation } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import vesselImage from '@/static/media/9385881.jpg';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function MapVesselDetails({ setPortOpen }: { setPortOpen: (open: boolean) => void }) {
	return (
		<div className="grid gap-6">
			<div className="p-6 pb-0">
				<AspectRatio ratio={16 / 8}>
					<img src={vesselImage} alt="vessel" className="rounded-lg object-cover" />
				</AspectRatio>
			</div>
			<div className="text-muted-foreground px-6 text-sm">Latest voyage updates.</div>
			<div className="relative grid gap-6 px-6">
				<div className="sep absolute top-8 bottom-12 left-8 -z-10 w-[1px] bg-slate-700"></div>
				<div className="flex gap-2">
					<div className="py-1.5">
						<CircleFlag countryCode="nl" className="h-4" />
					</div>
					<div>
						<Button
							variant="ghost"
							className="h-auto p-1"
							onClick={() => {
								setPortOpen(true);
							}}
						>
							Rotterdam, NL
						</Button>
						<div className="text-muted-foreground px-1 text-xs">ATD: 11 Feb, 06:00</div>
					</div>
				</div>
				<div className="flex items-center gap-2">
					<div className="bg-panel py-1.5">
						<Navigation className="size-4 text-amber-500" />
					</div>
					<div className="text-muted-foreground px-1 text-sm">
						En route&nbsp;&nbsp;(219° / 10.4 kn&nbsp;&nbsp;•&nbsp;&nbsp;7.9m)
					</div>
				</div>
				<div className="flex gap-2">
					<div className="py-1.5">
						<CircleFlag countryCode="in" className="h-4" />
					</div>
					<div>
						<Button
							variant="ghost"
							className="h-auto p-1"
							onClick={() => {
								setPortOpen(true);
							}}
						>
							Alappuzha, IN
						</Button>
						<div className="text-muted-foreground px-1 text-xs">ETA: 28 Feb, 08:00</div>
					</div>
				</div>
			</div>
			<Separator />
			<div className="text-muted-foreground px-6 text-sm">Recent port calls.</div>
			<div className="grid gap-3 px-4 pb-4">
				<Button variant="outline" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<BriefcaseBusiness className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Santos, BR</div>
					<div className="text-muted-foreground text-xs">19 Jan 25</div>
					<ArrowUpRight className="text-muted-foreground" />
				</Button>
				<Button variant="outline" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<BriefcaseBusiness className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Varna, BG</div>
					<div className="text-muted-foreground text-xs">23 Dec 24</div>
					<ArrowUpRight className="text-muted-foreground" />
				</Button>
			</div>
		</div>
	);
}
