import { ArrowUpRight, BriefcaseBusiness, Rss, Triangle<PERSON>lert, ChevronRight } from 'lucide-react';
import alappuzhaImage from '@/static/media/alappuzha.jpg';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

export default function MapPortDetails({ setNewsOpen }: { setNewsOpen: (open: boolean) => void }) {
	return (
		<div className="grid gap-6">
			<div className="p-6 pb-0">
				<AspectRatio ratio={16 / 8}>
					<img src={alappuzhaImage} alt="alappuzha" className="rounded-lg object-cover" />
				</AspectRatio>
			</div>
			<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-4 px-6 text-sm">
				<div className="text-muted-foreground">Coordinates</div>
				<div>17.5253N, 73.1404E</div>
				<div className="text-muted-foreground">Timezone</div>
				<div>GMT+5.5</div>
				<div className="text-muted-foreground">Weather</div>
				<div>36 °C&nbsp;&nbsp;•&nbsp;&nbsp;6.4 kn</div>
				<div className="text-muted-foreground">ISPS</div>
				<div>
					<span className="text-emerald-500">✓</span> Yes
				</div>
			</div>
			<Separator />
			<div className="grid gap-3 px-4">
				<Button variant="ghost" className="hover:bg-accent/50 h-8 justify-start px-2">
					<TriangleAlert className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Port Restrictions</div>
					<Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
						4
					</Badge>
					<ChevronRight className="text-muted-foreground" />
				</Button>
				<Button
					variant="ghost"
					className="hover:bg-accent/50 h-8 justify-start px-2"
					onClick={() => {
						setNewsOpen(true);
					}}
				>
					<Rss className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Port News</div>
					<Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
						2
					</Badge>
					<ChevronRight className="text-muted-foreground" />
				</Button>
			</div>
			<Separator />
			<div className="text-muted-foreground px-6 text-sm">Recent port calls</div>
			<div className="grid gap-3 px-4 pb-4">
				<Button variant="outline" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<BriefcaseBusiness className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">mv Ultra Quality</div>
					<div className="text-muted-foreground text-xs">19 Jan 25</div>
					<ArrowUpRight className="text-muted-foreground" />
				</Button>
			</div>
		</div>
	);
}
