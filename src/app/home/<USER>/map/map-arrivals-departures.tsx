import { MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const data = {
	arrivals: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			port: 'Rotterdam, NL',
			date: 'ETA: 28 Feb - 10:00',
			status: 'Arriving'
		},
		{
			id: '2',
			vessel: 'mv Meridiaan',
			port: 'Hamburg, DE',
			date: 'ETA: 29 Feb - 08:00',
			status: 'Arriving'
		},
		{
			id: '3',
			vessel: 'mv Oceanic Atlantic',
			port: 'Antwerp, BE',
			date: 'ETD: 01 Mar - 12:00',
			status: 'Departing'
		}
	]
};

export default function MapArrivalsDepartures() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">Arriving and departing vessels</div>
			<div className="grid gap-3 px-4">
				{data.arrivals.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 h-auto flex-col items-start gap-3 bg-transparent p-0 py-3"
					>
						<div className="flex w-full items-center gap-2 px-3">
							<div className="flex-1 text-left text-sm font-medium">{item.vessel}</div>
							<Badge
								variant="secondary"
								className={`rounded-md ${
									item.status === 'Departing' ? 'bg-primary' : 'bg-emerald-800'
								} text-white`}
							>
								{item.status}
							</Badge>
						</div>
						<div className="flex w-full items-center gap-2 px-3">
							<div className="text-muted-foreground flex flex-1 items-center gap-1 text-xs">
								<MapPin className="text-muted-foreground" />
								{item.port}
							</div>
							<div className="text-muted-foreground text-xs">{item.date}</div>
						</div>
					</Button>
				))}
			</div>
		</>
	);
}
