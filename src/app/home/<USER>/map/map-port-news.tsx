import { ArrowUpRight, ListFilter, MapPin } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

const data = {
	news: [
		{
			id: '1',
			title: 'Port of Salalah Announces Updated Tariff Effective February 2025',
			port: 'Salalah (Aruba)'
		},
		{
			id: '2',
			title: 'Suez Canal Completes Dredging for 10 km Duplication Project in Small Bitter',
			port: 'Suez Canal (Egypt)'
		},
		{
			id: '3',
			title: 'Update on Protected Industrial Action at the Ports of Darwin',
			port: 'Darwin (Australia)'
		},
		{
			id: '4',
			title: 'Black River in Manaus Exceeds 13 Meters as Water Levels Increase',
			port: 'Manaus (Brazil)'
		}
	]
};

export default function MapPortNews() {
	return (
		<>
			<div className="flex items-center p-6 pr-4">
				<div className="text-muted-foreground flex-1 text-sm">Latest news and updates.</div>
				<Button variant="ghost" size="xs">
					<ListFilter className="text-muted-foreground" />
				</Button>
			</div>
			<div className="grid gap-3 px-4 pb-4">
				{data.news.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 h-auto flex-col items-start gap-3 bg-transparent p-3"
					>
						<div className="flex gap-2">
							<div className="text-left font-medium text-wrap">{item.title}</div>
							<ArrowUpRight className="text-muted-foreground" />
						</div>
						<div className="text-muted-foreground flex flex-1 items-center gap-1 text-xs">
							<MapPin />
							{item.port}
						</div>
					</Button>
				))}
			</div>
		</>
	);
}
