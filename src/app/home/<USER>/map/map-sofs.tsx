import { Timer } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatTotalHours } from '@/common/utils/formatUtils';

const data = {
	sofs: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			hrs: 78
		},
		{
			id: '2',
			vessel: 'mv <PERSON>rid<PERSON><PERSON>',
			hrs: 65
		}
	]
};

export default function MapSOFs() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">SOFs pending your approval.</div>
			<div className="grid gap-3 px-4 pb-4">
				{data.sofs.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 h-auto flex-row items-center gap-3 bg-transparent px-3 py-3"
					>
						<div className="flex-1 text-left text-sm font-medium">{item.vessel}</div>
						<div className="flex items-center justify-end gap-2">
							<Timer className="text-muted-foreground" />
							<span className="text-xs font-medium">{formatTotalHours(item.hrs)}</span>
						</div>
					</Button>
				))}
			</div>
		</>
	);
}
