import { Anchor, Navigation } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const enRouteClass =
	'rounded-full absolute text-amber-400 scale-75 hover:scale-100 hover:text-amber-400 hover:bg-amber-500/10 focus:scale-100 focus:bg-amber-500/10';

const inPortClass =
	'rounded-full absolute text-sky-400 scale-75 hover:scale-100 hover:text-sky-400 hover:bg-sky-500/10 focus:scale-100 focus:bg-sky-500/10';

export default function MapPins({
	setVesselOpen,
	setVessel1Open
}: {
	setVesselOpen: (open: boolean) => void;
	setVessel1Open: (open: boolean) => void;
}) {
	return (
		<TooltipProvider delayDuration={0}>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[48%] left-[15%] ${enRouteClass}`}
						onClick={() => {
							setVesselOpen(true);
						}}
					>
						<Navigation />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Horizon</div>
					<div className="text-muted-foreground text-xs">ETA: Vancouver, CA / 03 Mar - 08:00</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[65%] left-[25%] ${enRouteClass}`}
						onClick={() => {
							setVesselOpen(true);
						}}
					>
						<Navigation />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Meridiaan</div>
					<div className="text-muted-foreground text-xs">ETA: Ghent, BE / 17 Mar - 08:00</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[45%] left-[40%] ${enRouteClass}`}
						onClick={() => {
							setVesselOpen(true);
						}}
					>
						<Navigation />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Emerald Star </div>
					<div className="text-muted-foreground text-xs">ETA: Rotterdam, NL / 02 Mar - 10:00</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[41%] left-[47.8%] ${inPortClass}`}
						onClick={() => {
							setVessel1Open(true);
						}}
					>
						<Anchor />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Discoverer</div>
					<div className="text-muted-foreground text-xs leading-5">ATA: Rotterdam, NL / 27 Mar - 08:00</div>
					<div className="text-muted-foreground text-xs leading-5">Irone Ore • Discharging</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[47%] left-[52.5%] ${inPortClass}`}
						onClick={() => {
							setVessel1Open(true);
						}}
					>
						<Anchor />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Jordania RN</div>
					<div className="text-muted-foreground text-xs leading-5">ATA: Varna, BG / 26 Mar - 10:00</div>
					<div className="text-muted-foreground text-xs leading-5">Wheat • Loading</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[48.5%] left-[29.5%] ${inPortClass}`}
						onClick={() => {
							setVessel1Open(true);
						}}
					>
						<Anchor />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Santos Maya</div>
					<div className="text-muted-foreground text-xs leading-5">ATA: New Jersey, US / 26 Mar - 07:00</div>
					<div className="text-muted-foreground text-xs leading-5">Iron • Loading</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[66%] left-[60%] ${enRouteClass}`}
						onClick={() => {
							setVesselOpen(true);
						}}
					>
						<Navigation />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Los Testigos</div>
					<div className="text-muted-foreground text-xs">ETA: Alappuzha, IN / 28 Feb - 08:00</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[83%] left-[48%] ${enRouteClass}`}
						onClick={() => {
							setVesselOpen(true);
						}}
					>
						<Navigation />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Oceanic Atlantic</div>
					<div className="text-muted-foreground text-xs">ETA: Cape Town, SA / 01 Mar - 08:00</div>
				</TooltipContent>
			</Tooltip>
			<Tooltip>
				<TooltipTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className={`top-[83%] left-[72%] ${enRouteClass}`}
						onClick={() => {
							setVesselOpen(true);
						}}
					>
						<Navigation />
					</Button>
				</TooltipTrigger>
				<TooltipContent>
					<div className="text-sm leading-6 font-medium">mv Explorer</div>
					<div className="text-muted-foreground text-xs">ETA: Auckland, NZ / 07 Mar - 07:00</div>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
}
