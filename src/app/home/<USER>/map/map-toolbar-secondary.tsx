import { LocateIcon, Minus, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export default function MapToolbarSecondary() {
	return (
		<TooltipProvider delayDuration={100}>
			<div className="absolute right-2 bottom-0 z-50">
				<div className="flex flex-col rounded-md border">
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" size="icon" className="hover:bg-accent/50">
								<LocateIcon />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Your location</TooltipContent>
					</Tooltip>
					<Separator />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" size="icon" className="hover:bg-accent/50">
								<Plus />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Zoom in</TooltipContent>
					</Tooltip>
					<Separator />
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="ghost" size="icon" className="hover:bg-accent/50">
								<Minus />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Zoom out</TooltipContent>
					</Tooltip>
				</div>
			</div>
		</TooltipProvider>
	);
}
