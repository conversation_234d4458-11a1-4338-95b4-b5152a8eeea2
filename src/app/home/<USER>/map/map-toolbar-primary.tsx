import { ArrowLeftRight, Clock, ClockAlert, MoreHorizontal, RssIcon, Text, CircleStop } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface MapToolbarPrimaryProps {
	setPortOpen: (open: boolean) => void;
	setApprovalsOpen: (open: boolean) => void;
	setVesselOpen: (open: boolean) => void;
	setStatusOpen: (open: boolean) => void;
	setArrivalsOpen: (open: boolean) => void;
	setStoppagesOpen: (open: boolean) => void;
	setDelaysOpen: (open: boolean) => void;
	setNewsOpen: (open: boolean) => void;
	setCompanyOpen: (open: boolean) => void;
}

export default function MapToolbarPrimary({
	setApprovalsOpen,
	setStatusOpen,
	setArrivalsOpen,
	setStoppagesOpen,
	setDelaysOpen,
	setNewsOpen
}: MapToolbarPrimaryProps) {
	return (
		<TooltipProvider delayDuration={100}>
			<div className="absolute top-0 right-2 bottom-0 z-10 h-full pt-28 pb-0">
				<div className="flex flex-col gap-1.5">
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="hover:bg-accent/50 relative"
								onClick={() => {
									setApprovalsOpen(true);
								}}
							>
								<Clock />
								<span className="border-panel absolute top-0 right-0 size-2 rounded-full border-1 bg-red-500"></span>
							</Button>
						</TooltipTrigger>
						<TooltipContent>Pending approvals</TooltipContent>
					</Tooltip>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="hover:bg-accent/50"
								onClick={() => {
									setStatusOpen(true);
								}}
							>
								<Text />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Status changes</TooltipContent>
					</Tooltip>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="hover:bg-accent/50"
								onClick={() => {
									setArrivalsOpen(true);
								}}
							>
								<ArrowLeftRight />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Arrivals / Departures</TooltipContent>
					</Tooltip>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="hover:bg-accent/50"
								onClick={() => {
									setStoppagesOpen(true);
								}}
							>
								<CircleStop />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Latest stoppages</TooltipContent>
					</Tooltip>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="hover:bg-accent/50"
								onClick={() => {
									setDelaysOpen(true);
								}}
							>
								<ClockAlert />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Delays</TooltipContent>
					</Tooltip>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="icon"
								className="hover:bg-accent/50"
								onClick={() => {
									setNewsOpen(true);
								}}
							>
								<RssIcon />
							</Button>
						</TooltipTrigger>
						<TooltipContent>Port news</TooltipContent>
					</Tooltip>
					{/* <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                onClick={() => {
                                    setVesselOpen(true);
                                }}
                                variant="outline"
                                size="icon"
                                className="hover:bg-accent/50">
                                <Navigation />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Vessel panel</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                onClick={() => {
                                    setPortOpen(true);
                                }}
                                variant="outline"
                                size="icon"
                                className="hover:bg-accent/50">
                                <Anchor />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Port panel</TooltipContent>
                    </Tooltip>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                onClick={() => {
                                    setCompanyOpen(true);
                                }}
                                variant="outline"
                                size="icon"
                                className="hover:bg-accent/50">
                                <Building2 />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent>Company panel</TooltipContent>
                    </Tooltip> */}
					<Tooltip>
						<TooltipTrigger asChild>
							<Button variant="outline" size="icon" className="hover:bg-accent/50">
								<MoreHorizontal />
							</Button>
						</TooltipTrigger>
						<TooltipContent>More actions</TooltipContent>
					</Tooltip>
				</div>
			</div>
		</TooltipProvider>
	);
}
