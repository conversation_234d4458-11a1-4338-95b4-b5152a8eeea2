import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatAmount } from '@/common/utils/formatUtils';

const data = {
	laytimes: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			laytime: 'DEM',
			color: 'border-red-500',
			currency: 'USD',
			amount: 26522.0
		},
		{
			id: '2',
			vessel: 'mv Meridiaan',
			laytime: 'DES',
			color: 'border-emerald-500',
			currency: 'USD',
			amount: 4980
		},
		{
			id: '3',
			vessel: 'mv Oceanic Atlantic',
			laytime: 'DEM',
			color: 'border-red-500',
			currency: 'USD',
			amount: 41500
		}
	]
};

export default function MapLaytimes() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">Laytimes pending your approval.</div>
			<div className="grid gap-3 px-4 pb-4">
				{data.laytimes.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 h-auto flex-row items-center gap-3 bg-transparent px-3 py-3"
					>
						<div className="flex-1 text-left text-sm font-medium">{item.vessel}</div>
						<div className="flex items-center justify-end gap-1">
							<span className="text-muted-foreground text-xs uppercase">{item.currency}</span>
							<span className="text-xs font-medium">{formatAmount(item.amount)}</span>
						</div>
						<Badge variant="outline" className={`w-9 px-1 py-0.5 ${item.color}`}>
							<span className="text-xs">{item.laytime}</span>
						</Badge>
					</Button>
				))}
			</div>
		</>
	);
}
