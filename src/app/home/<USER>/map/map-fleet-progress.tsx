import { ArrowLeftRight, ChevronRight, CircleStop, ClockAlert, MapPin, Navigation, Text } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

const data = {
	progress: [
		{
			id: '1',
			type: 'Status changes',
			icon: 'Text',
			count: 3,
			action: 'setStatusOpen'
		},
		{
			id: '2',
			type: '48 hrs arrivals / departures',
			icon: 'ArrowLeftRight',
			count: 3,
			action: 'setArrivalsOpen'
		},
		{
			id: '3',
			type: 'Latest stoppages',
			icon: 'CircleStop',
			count: 5,
			action: 'setStoppagesOpen'
		},
		{
			id: '4',
			type: 'Delays',
			icon: 'ClockAlert',
			count: 3,
			action: 'setDelaysOpen'
		}
	],
	recent: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			icon: 'Navigation',
			color: 'text-amber-500',
			action: 'setVesselOpen'
		},
		{
			id: '2',
			vessel: 'Port of Alappuzha, IN',
			icon: 'MapPin',
			color: 'text-primary',
			action: 'setPortOpen'
		}
	]
};

const iconMap = {
	Text,
	ArrowLeftRight,
	CircleStop,
	ClockAlert,
	MapPin,
	Navigation
} as const;

interface MapFleetProgressProps {
	setStatusOpen: (open: boolean) => void;
	setArrivalsOpen: (open: boolean) => void;
	setStoppagesOpen: (open: boolean) => void;
	setDelaysOpen: (open: boolean) => void;
	setVesselOpen: (open: boolean) => void;
	setPortOpen: (open: boolean) => void;
}

export default function MapFleetProgress({
	setStatusOpen,
	setArrivalsOpen,
	setStoppagesOpen,
	setDelaysOpen,
	setVesselOpen,
	setPortOpen
}: MapFleetProgressProps) {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">View the latest fleet progress.</div>
			<div className="grid gap-2 px-3 pb-4">
				{data.progress.map(item => {
					const Icon = iconMap[item.icon as keyof typeof iconMap];
					return (
						<Button
							key={item.id}
							variant="ghost"
							className="hover:bg-accent/50 h-8 justify-start px-2"
							onClick={() => {
								if (item.action === 'setStatusOpen') {
									setStatusOpen(true);
								} else if (item.action === 'setArrivalsOpen') {
									setArrivalsOpen(true);
								} else if (item.action === 'setStoppagesOpen') {
									setStoppagesOpen(true);
								} else if (item.action === 'setDelaysOpen') {
									setDelaysOpen(true);
								}
							}}
						>
							<Icon className="text-muted-foreground" />
							<div className="flex-1 text-left font-normal">{item.type}</div>
							<Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
								{item.count}
							</Badge>
							<ChevronRight className="text-muted-foreground" />
						</Button>
					);
				})}
			</div>
			<Separator />
			<div className="text-muted-foreground p-6 text-sm">Recently viewed</div>
			<div className="grid gap-3 px-4 pb-4">
				{data.recent.map(item => {
					const Icon = iconMap[item.icon as keyof typeof iconMap];
					return (
						<Button
							key={item.id}
							variant="outline"
							className="hover:bg-accent/50 justify-start bg-transparent px-2"
							onClick={() => {
								if (item.action === 'setVesselOpen') {
									setVesselOpen(true);
								} else if (item.action === 'setPortOpen') {
									setPortOpen(true);
								}
							}}
						>
							<Icon className={item.color} />
							<div className="flex-1 text-left font-normal">{item.vessel}</div>
						</Button>
					);
				})}
			</div>
		</>
	);
}
