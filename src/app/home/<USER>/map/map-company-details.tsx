import { <PERSON><PERSON>, <PERSON><PERSON>ronR<PERSON>, <PERSON>, ListChecks, Mail, Phone, Star, Text, WalletCards } from 'lucide-react';
import wpsLogo from '@/static/media/img-wps-logo.svg';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

export default function MapCompanyDetails() {
	return (
		<div className="grid gap-6">
			<div className="p-6 pb-0">
				<AspectRatio ratio={16 / 4} className="rounded-xl border">
					<img src={wpsLogo} alt="wps" className="rounded-lg p-4" />
				</AspectRatio>
			</div>
			<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 px-6 text-sm">
				<div className="text-muted-foreground flex items-center gap-2">
					<Phone className="size-4" />
					Phone
				</div>
				<div>(+65) 6395 4545</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Mail className="size-4" />
					Email
				</div>
				<div><EMAIL></div>
				<div className="text-muted-foreground flex items-center gap-2">
					<WalletCards className="size-4" />
					VAT
				</div>
				<div>GST M874920153</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Text className="size-4" />
					Trade N
				</div>
				<div>(UEN) 202412345K</div>
			</div>
			<Separator />
			<div className="text-muted-foreground px-6 text-sm">Bank details</div>
			<div className="grid gap-2 px-4">
				<Button variant="ghost" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<Landmark className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">United Overseas Bank (SGD)</div>
					<ChevronRight className="text-muted-foreground" />
				</Button>
			</div>
			<Separator />
			<div className="text-muted-foreground px-6 text-sm">Other details</div>
			<div className="grid gap-2 px-4">
				<Button variant="ghost" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<Anchor className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Ports served</div>
					<Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
						43
					</Badge>
					<ChevronRight className="text-muted-foreground" />
				</Button>
				<Button variant="ghost" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<ListChecks className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Compliance</div>
					<ChevronRight className="text-muted-foreground" />
				</Button>
				<Button variant="ghost" className="hover:bg-accent/50 justify-start bg-transparent px-2">
					<Star className="text-muted-foreground" />
					<div className="flex-1 text-left font-normal">Scores</div>
					<ChevronRight className="text-muted-foreground" />
				</Button>
			</div>
		</div>
	);
}
