import { Anchor, CircleDotDashed, FoldHorizontal, MapPin, Navigation, Sailboat } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

const data = {
	changes: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			port: 'Alappuzha, IN',
			iconFrom: 'Navigation',
			statusFrom: 'En route',
			colorFrom: 'text-amber-500',
			time: '3h ago',
			iconTo: 'Anchor',
			statusTo: 'Anchored',
			colorTo: 'text-blue-500'
		},
		{
			id: '2',
			vessel: 'mv Meridiaan',
			port: 'Rotterdam, NL',
			iconFrom: 'FoldHorizontal',
			statusFrom: 'Shifting',
			colorFrom: 'text-indigo-500',
			time: '1d ago',
			iconTo: 'CircleDotDashed',
			statusTo: 'Berthed',
			colorTo: 'text-sky-500'
		},
		{
			id: '3',
			vessel: 'mv Oceanic Atlantic',
			port: 'Hamburg, DE',
			iconFrom: 'CircleDotDashed',
			statusFrom: 'Berthed',
			colorFrom: 'text-sky-500',
			time: '2d ago',
			iconTo: 'Sailboat',
			statusTo: 'Sailed',
			colorTo: 'text-emerald-500'
		}
	]
};

const iconMap = {
	Anchor,
	MapPin,
	Navigation,
	FoldHorizontal,
	CircleDotDashed,
	Sailboat
} as const;

export default function MapStatusChanges() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">Latest status changes</div>
			<div className="grid gap-3 px-4">
				{data.changes.map(item => {
					const IconFrom = iconMap[item.iconFrom as keyof typeof iconMap];
					const IconTo = iconMap[item.iconTo as keyof typeof iconMap];
					return (
						<Button
							key={item.id}
							variant="outline"
							className="hover:bg-accent/50 h-auto flex-col items-start gap-3 bg-transparent p-0 py-3"
						>
							<div className="flex w-full items-center gap-2 px-4">
								<div className="flex-1 text-left text-sm font-medium">{item.vessel}</div>
								<div className="text-muted-foreground text-xs">{item.port}</div>
							</div>
							<div className="flex w-full items-center gap-2 px-2">
								<Badge variant="outline" className="rounded-full">
									<IconFrom className={item.colorFrom} />
									<span className="text-muted-foreground">{item.statusFrom}</span>
								</Badge>
								<Separator className="flex-1" />
								<div className="text-muted-foreground text-xs">{item.time}</div>
								<Separator className="flex-1" />
								<Badge variant="outline" className="rounded-full">
									<IconTo className={item.colorTo} />
									<span className="text-muted-foreground">{item.statusTo}</span>
								</Badge>
							</div>
						</Button>
					);
				})}
			</div>
		</>
	);
}
