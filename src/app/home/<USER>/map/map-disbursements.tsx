import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatAmount } from '@/common/utils/formatUtils';

const data = {
	disbursements: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			da: 'PDA',
			color: 'border-amber-500',
			currency: 'USD',
			amount: 41500.5
		},
		{
			id: '2',
			vessel: 'mv Meridiaan',
			da: 'DDA',
			color: 'border-indigo-500',
			currency: 'USD',
			amount: 4980
		},
		{
			id: '3',
			vessel: 'mv Oceanic Atlantic',
			da: 'FDA',
			color: 'border-emerald-500',
			currency: 'USD',
			amount: 28745.8
		},
		{
			id: '4',
			vessel: 'mv Discoverer',
			da: 'DDA',
			color: 'border-indigo-500',
			currency: 'USD',
			amount: 40200.4
		},
		{
			id: '5',
			vessel: 'mv Explorer',
			da: 'PDA',
			color: 'border-amber-500',
			currency: 'USD',
			amount: 41230.1
		}
	]
};

export default function MapDisbursements() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">Disbursements pending your approval.</div>
			<div className="grid gap-3 px-4 pb-4">
				{data.disbursements.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 h-auto flex-row items-center gap-3 bg-transparent px-3 py-3"
					>
						<div className="flex-1 text-left text-sm font-medium">{item.vessel}</div>
						<div className="flex items-center justify-end gap-1">
							<span className="text-muted-foreground text-xs uppercase">{item.currency}</span>
							<span className="text-xs font-medium">{formatAmount(item.amount)}</span>
						</div>
						<Badge variant="outline" className={`rounded-full px-1.5 py-0.5 ${item.color}`}>
							<span>{item.da}</span>
						</Badge>
					</Button>
				))}
			</div>
		</>
	);
}
