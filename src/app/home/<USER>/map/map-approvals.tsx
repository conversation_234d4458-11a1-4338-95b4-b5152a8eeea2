import { DollarSign, Scale, Timer, ChevronRight, ArrowUpRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { formatAmount } from '@/common/utils/formatUtils';

const data = {
	pending: [
		{
			id: '1',
			type: 'Disbursements',
			icon: 'DollarSign',
			action: 'setDisbursementsOpen',
			count: 5
		},
		{
			id: '2',
			type: 'Laytimes',
			icon: 'Scale',
			action: 'setLaytimesOpen',
			count: 2
		},
		{
			id: '3',
			type: 'SOFs',
			icon: 'Timer',
			action: 'setSofsOpen',
			count: 2
		}
	],
	recent: [
		{
			id: '1',
			name: 'mv Los Testigos',
			type: 'Disbursement',
			color: 'text-amber-500',
			amount: 41500.5,
			currency: 'USD'
		},
		{
			id: '2',
			name: 'mv <PERSON>ridia<PERSON>',
			type: 'Disbursement',
			color: 'text-indigo-500',
			amount: 4980,
			currency: 'USD'
		},
		{
			id: '3',
			name: 'mv Oceanic Atlantic',
			type: 'Disbursement',
			color: 'text-emerald-500',
			amount: 28745.8,
			currency: 'USD'
		}
	]
};

const iconMap = {
	DollarSign,
	Scale,
	Timer
} as const;

interface MapApprovalsProps {
	setDisbursementsOpen: (open: boolean) => void;
	setLaytimesOpen: (open: boolean) => void;
	setSofsOpen: (open: boolean) => void;
	setApprovalsOpen: (open: boolean) => void;
}

export default function MapApprovals({ setDisbursementsOpen, setLaytimesOpen, setSofsOpen }: MapApprovalsProps) {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">Documents required your approval.</div>
			<div className="grid gap-2 px-3 pb-4">
				{data.pending.map(item => {
					const Icon = iconMap[item.icon as keyof typeof iconMap];
					return (
						<Button
							key={item.id}
							variant="ghost"
							className="hover:bg-accent/50 h-8 justify-start px-2"
							onClick={() => {
								if (item.action === 'setDisbursementsOpen') {
									setDisbursementsOpen(true);
								} else if (item.action === 'setLaytimesOpen') {
									setLaytimesOpen(true);
								} else if (item.action === 'setSofsOpen') {
									setSofsOpen(true);
								}
								// setApprovalsOpen(false);
							}}
						>
							<Icon className="text-muted-foreground" />
							<div className="flex-1 text-left font-normal">{item.type}</div>
							<Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
								{item.count}
							</Badge>
							<ChevronRight className="text-muted-foreground" />
						</Button>
					);
				})}
			</div>
			<Separator />
			<div className="text-muted-foreground p-6 text-sm">Recently viewed</div>
			<div className="grid gap-3 px-4 pb-4">
				{data.recent.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 justify-start bg-transparent px-2"
					>
						<DollarSign className={item.color} />
						<div className="flex-1 text-left font-normal">{item.name}</div>
						<div className="flex items-center justify-end gap-2 text-xs">
							<span className="text-muted-foreground text-xs uppercase">{item.currency}</span>
							<span className="font-medium">{formatAmount(item.amount)}</span>
						</div>
						<ArrowUpRight className="text-muted-foreground" />
					</Button>
				))}
			</div>
		</>
	);
}
