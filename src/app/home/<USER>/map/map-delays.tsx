import { MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';

const data = {
	delays: [
		{
			id: '1',
			vessel: 'mv Los Testigos',
			port: 'Rotterdam, NL',
			date: 'ETA: 24 Feb - 10:00',
			delay: '+6 hrs'
		},
		{
			id: '2',
			vessel: 'mv Meridiaan',
			port: 'Hamburg, DE',
			date: 'ETA: 25 Feb - 08:00',
			delay: '+12 hrs'
		},
		{
			id: '3',
			vessel: 'mv Oceanic Atlantic',
			port: 'Antwerp, BE',
			date: 'ETA: 26 Feb - 12:00',
			delay: '+18 hrs'
		}
	]
};

export default function MapDelays() {
	return (
		<>
			<div className="text-muted-foreground p-6 text-sm">Latest delays</div>
			<div className="grid gap-3 px-4">
				{data.delays.map(item => (
					<Button
						key={item.id}
						variant="outline"
						className="hover:bg-accent/50 h-auto flex-col items-start gap-3 bg-transparent p-0 py-3"
					>
						<div className="flex w-full items-center gap-2 px-4">
							<div className="flex-1 text-left text-sm font-medium">{item.vessel}</div>
							<div className="text-sm text-red-500">{item.delay}</div>
						</div>
						<div className="flex w-full items-center gap-2 px-3">
							<div className="text-muted-foreground flex flex-1 items-center gap-1 text-xs">
								<MapPin className="text-muted-foreground" />
								{item.port}
							</div>
							<div className="text-muted-foreground text-xs">{item.date}</div>
						</div>
					</Button>
				))}
			</div>
		</>
	);
}
