import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MapSidePanelProps {
	title: React.ReactNode;
	onClose: () => void;
	isOpen: boolean;
	children: React.ReactNode;
	hasClose?: boolean;
}

const MapSidePanel: React.FC<MapSidePanelProps> = ({ title, onClose, isOpen, children, hasClose = true }) => (
	<div
		className={`absolute top-0 right-0 bottom-0 h-full w-100 px-6 pt-28 pb-0 transition-transform duration-200 ${
			isOpen ? 'translate-x-0' : 'translate-x-full'
		}`}
	>
		<div className="bg-panel/60 flex h-full flex-col overflow-hidden rounded-lg border backdrop-blur-lg">
			<div className="flex items-center justify-between p-4 pb-0">
				<div className="px-2 text-base font-semibold">{title}</div>
				{hasClose && (
					<Button variant="ghost" size="xs" onClick={onClose}>
						<X className="text-muted-foreground" />
					</Button>
				)}
			</div>
			<div className="flex-1 overflow-auto">{children}</div>
		</div>
	</div>
);

export default MapSidePanel;
