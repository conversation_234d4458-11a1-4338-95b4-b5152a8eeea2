import * as React from 'react';
import { <PERSON>chor, ArrowLeftRight, Building2, Clock, ClockAlert, Navigation, Rss, StopCircle, Route } from 'lucide-react';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator
} from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';

interface MapSearchProps {
	setVesselOpen: (open: boolean) => void;
	setVessel1Open: (open: boolean) => void;
	setPortOpen: (open: boolean) => void;
	setCompanyOpen: (open: boolean) => void;
	setApprovalsOpen: (open: boolean) => void;
	setNewsOpen: (open: boolean) => void;
	setStatusOpen: (open: boolean) => void;
	setArrivalsOpen: (open: boolean) => void;
	setStoppagesOpen: (open: boolean) => void;
	setDelaysOpen: (open: boolean) => void;
}

export default function MapSearch({
	setVesselOpen,
	setVessel1O<PERSON>,
	setPortOpen,
	setCompanyOpen,
	setApprovalsOpen,
	setNewsOpen,
	setStatusOpen,
	setArrivalsOpen,
	setStoppagesOpen,
	setDelaysOpen
}: MapSearchProps) {
	const [open, setOpen] = React.useState(false);
	const commandRef = React.useRef<HTMLDivElement>(null);

	const handleSelect = (action: (open: boolean) => void) => {
		action(true);
		setOpen(false);
	};

	// Add click outside handler
	React.useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (commandRef.current && !commandRef.current.contains(event.target as Node)) {
				setOpen(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	const recentItems = [
		{ id: 'lostestigos', icon: Navigation, label: 'mv Los Testigos', action: setVesselOpen },
		{ id: 'alappuzha', icon: Anchor, label: 'Alappuzha, IN', action: setPortOpen },
		{ id: 'wilhelmsen', icon: Building2, label: 'Wilhelmsen Port Services', action: setCompanyOpen },
		{ id: 'discoverer', icon: Navigation, label: 'mv Discoverer', action: setVessel1Open }
	];

	const viewAlsoItems = [
		{ id: 'pending_approvals', icon: Clock, label: 'Pending approvals', count: 9, action: setApprovalsOpen },
		{ id: 'status', icon: Route, label: 'Status changes', count: 3, action: setStatusOpen },
		{
			id: 'arrivals',
			icon: ArrowLeftRight,
			label: '48 hrs arrivals / departures',
			count: 3,
			action: setArrivalsOpen
		},
		{ id: 'stoppages', icon: StopCircle, label: 'Latest stoppages', count: 5, action: setStoppagesOpen },
		{ id: 'delays', icon: ClockAlert, label: 'Delays', count: 3, action: setDelaysOpen },
		{ id: 'news', icon: Rss, label: 'News', count: 16, action: setNewsOpen }
	];

	return (
		<div className="relative z-10 col-span-10">
			<Command
				ref={commandRef}
				className="bg-secondary/20 focus-within:ring-primary aria-expanded:ring-primary overflow-visible rounded-lg border shadow-lg ring-1 ring-transparent backdrop-blur-lg transition-colors aria-expanded:ring-1"
			>
				<CommandInput
					placeholder="Search anything..."
					className="h-auto border-none py-2 text-sm leading-none focus-visible:ring-0 focus-visible:outline-none"
					onFocus={() => setOpen(true)}
				/>
				{open && (
					<CommandList className="bg-panel absolute top-full right-0 left-0 mt-1 min-h-[350px] rounded-lg border p-1 shadow-lg">
						<CommandEmpty>No results found.</CommandEmpty>
						<CommandGroup heading="Recent">
							{recentItems.map(item => (
								<CommandItem key={item.id} value={item.id} onSelect={() => handleSelect(item.action)}>
									<item.icon className="text-muted-foreground size-4" />
									<span className="px-1">{item.label}</span>
								</CommandItem>
							))}
						</CommandGroup>
						<CommandSeparator className="my-1" />
						<CommandGroup heading="View also">
							{viewAlsoItems.map(item => (
								<CommandItem key={item.id} value={item.id} onSelect={() => handleSelect(item.action)}>
									<item.icon className="text-muted-foreground size-4" />
									<span className="flex-1 px-1">{item.label}</span>
									<Badge variant="secondary" className="px-1.5 py-0.5 text-xs">
										{item.count}
									</Badge>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				)}
			</Command>
		</div>
	);
}
