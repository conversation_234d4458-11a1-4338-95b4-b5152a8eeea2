import { formatDayDate } from '@/common/utils/formatUtils';

export default function HeadingWidget1({ className = '' }) {
	const now = new Date();
	const todayDate = formatDayDate(now);
	const hours = now.getHours();

	let greetingMessage = 'Good evening';

	if (hours < 12) {
		greetingMessage = 'Good morning';
	} else if (hours < 18) {
		greetingMessage = 'Good afternoon';
	}
	return (
		<div className={className}>
			<div className="text-muted-foreground text-center">{todayDate}</div>
			<h1 className="py-2 text-center text-4xl font-medium">{greetingMessage}, John</h1>
		</div>
	);
}
