import { CheckCircle2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

export default function ApprovalsWidget1({ className = '' }) {
	const approveButton = (
		<Tooltip>
			<TooltipTrigger asChild>
				<Button variant="ghost" size="icon" className="text-muted-foreground size-6 rounded-full">
					<CheckCircle2 />
				</Button>
			</TooltipTrigger>
			<TooltipContent>Approve</TooltipContent>
		</Tooltip>
	);
	return (
		<Card className={className}>
			<CardHeader className="flex-row items-center">
				<CardTitle className="flex-1">Pending approval</CardTitle>
				<div className="flex gap-2">
					<Button variant="outline" size="sm" className="border-primary rounded-full border-2">
						<span className="text-base font-semibold">5</span>
						<span className="text-muted-foreground">DAs</span>
					</Button>
					<Button variant="outline" size="sm" className="rounded-full border bg-inherit">
						<span className="text-base font-semibold">3</span>
						<span className="text-muted-foreground">Laytimes</span>
					</Button>
					<Button variant="outline" size="sm" className="rounded-full border bg-inherit">
						<span className="text-base font-semibold">2</span>
						<span className="text-muted-foreground">SOFs</span>
					</Button>
				</div>
			</CardHeader>
			<CardContent className="px-0">
				<TooltipProvider>
					<Table>
						<TableBody>
							<TableRow>
								<TableCell className="px-5 font-medium">
									<div className="flex flex-row items-center gap-2">
										{approveButton}
										mv Ultra Quality
									</div>
								</TableCell>
								<TableCell className="flex items-center gap-6 justify-self-end px-6 font-medium">
									<div className="flex items-center gap-2">
										<span className="text-muted-foreground text-xs uppercase">usd</span>
										<span>26,522.00</span>
									</div>
									<Badge variant="outline" className="rounded-full border-amber-500">
										<span>PDA</span>
									</Badge>
									<div className="text-muted-foreground w-24 text-right">23 min ago</div>
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="px-5 font-medium">
									<div className="flex items-center gap-2">
										{approveButton}
										mv Meridiaan
									</div>
								</TableCell>
								<TableCell className="flex items-center gap-6 justify-self-end px-6 font-medium">
									<div className="flex items-center gap-2">
										<span className="text-muted-foreground text-xs uppercase">usd</span>
										<span>4,980.00</span>
									</div>
									<Badge variant="outline" className="rounded-full border-blue-500">
										<span>SDA</span>
									</Badge>
									<div className="text-muted-foreground w-24 text-right">1 day ago</div>
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="px-5 font-medium">
									<div className="flex items-center gap-2">
										{approveButton}
										mv Oceanic Atlantic
									</div>
								</TableCell>
								<TableCell className="flex items-center gap-6 justify-self-end px-6 font-medium">
									<div className="flex items-center gap-2">
										<span className="text-muted-foreground text-xs uppercase">usd</span>
										<span>41,500.50</span>
									</div>
									<Badge variant="outline" className="rounded-full border-emerald-500">
										<span>FDA</span>
									</Badge>
									<div className="text-muted-foreground w-24 text-right">2 days ago</div>
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="px-5 font-medium">
									<div className="flex flex-row items-center gap-2">
										{approveButton}
										mv Navigator
									</div>
								</TableCell>
								<TableCell className="flex items-center gap-6 justify-self-end px-6 font-medium">
									<div className="flex items-center gap-2">
										<span className="text-muted-foreground text-xs uppercase">usd</span>
										<span>26,522.00</span>
									</div>
									<Badge variant="outline" className="rounded-full border-amber-500">
										<span>PDA</span>
									</Badge>
									<div className="text-muted-foreground w-24 text-right">3 days ago</div>
								</TableCell>
							</TableRow>
							<TableRow>
								<TableCell className="px-5 font-medium">
									<div className="flex flex-row items-center gap-2">
										{approveButton}
										mv Ultra Quality
									</div>
								</TableCell>
								<TableCell className="flex items-center gap-6 justify-self-end px-6 font-medium">
									<div className="flex items-center gap-2">
										<span className="text-muted-foreground text-xs uppercase">usd</span>
										<span>26,522.00</span>
									</div>
									<Badge variant="outline" className="rounded-full border-indigo-500">
										<span>DDA</span>
									</Badge>
									<div className="text-muted-foreground w-24 text-right">4 days ago</div>
								</TableCell>
							</TableRow>
						</TableBody>
					</Table>
				</TooltipProvider>
			</CardContent>
		</Card>
	);
}
