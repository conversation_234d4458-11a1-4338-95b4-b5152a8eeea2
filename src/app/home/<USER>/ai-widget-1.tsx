import { <PERSON><PERSON><PERSON>, Maximize2 } from 'lucide-react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import Prompts from '@/components/chatbot/prompts';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

export default function AIWidget1({ className = '' }) {
	return (
		<Card className={`flex flex-col ${className}`}>
			<Tooltip>
				<CardHeader className="relative flex-row items-center justify-between">
					<CardTitle>Operator One</CardTitle>
					<TooltipTrigger asChild>
						<Button
							size="icon"
							variant="ghost"
							className="text-muted-foreground absolute top-4 right-4 size-8"
						>
							<Maximize2 />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Expand</TooltipContent>
				</Card<PERSON><PERSON>er>
				<CardContent className="flex-1">
					<div className="text-muted-foreground py-4 text-xs font-medium">Suggested Prompts</div>
					<Prompts className="flex flex-wrap gap-3" />
				</CardContent>
				<CardFooter>
					<div className="flex h-11 w-full rounded-lg border focus-within:border-slate-300 dark:bg-gray-900 dark:focus-within:border-slate-700">
						<Textarea
							placeholder="Ask Operator One"
							className="min-h-auto resize-none border-none ring-0 outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
						></Textarea>
						<Button variant="default" disabled size="sm" className="mx-2 my-2 size-6">
							<ArrowUp />
						</Button>
					</div>
				</CardFooter>
			</Tooltip>
		</Card>
	);
}
