import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
	try {
		console.log('Fetching voyage itinerary data from Google Apps Script...');

		const response = await fetch(
			'https://script.google.com/a/macros/abraxa.com/s/AKfycbzjyET6KTgzMIdF5cbLZ3fxgZvthWE7zWsrw1vSUZmjFhPtWrtLoxWKvaYzpHdkpYU1/exec?path=n8n_voyage_itinerary&action=read',
			{
				method: 'GET',
				headers: {
					Accept: 'application/json',
					'Content-Type': 'application/json'
				}
			}
		);

		console.log('Response status:', response.status);
		console.log('Response headers:', Object.fromEntries(response.headers.entries()));

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Error response:', errorText);
			throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
		}

		const data = await response.json();
		console.log('Successfully fetched data:', data);

		return NextResponse.json(data, {
			headers: {
				'Access-Control-Allow-Origin': '*',
				'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
				'Access-Control-Allow-Headers': 'Content-Type, Authorization'
			}
		});
	} catch (error) {
		console.error('Error fetching voyage itinerary:', error);
		return NextResponse.json(
			{
				error: 'Failed to fetch voyage itinerary data',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{
				status: 500,
				headers: {
					'Access-Control-Allow-Origin': '*',
					'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
					'Access-Control-Allow-Headers': 'Content-Type, Authorization'
				}
			}
		);
	}
}

export async function OPTIONS(request: NextRequest) {
	return new NextResponse(null, {
		status: 200,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization'
		}
	});
}
