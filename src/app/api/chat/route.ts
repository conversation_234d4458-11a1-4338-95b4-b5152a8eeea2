export const getAIResponse = async (message: string): Promise<string> => {
	const response = await fetch('https://api.openai.com/v1/chat/completions', {
		method: 'POST',
		headers: {
			Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			model: 'gpt-4o',
			messages: [{ role: 'user', content: message }]
			// response_format: 'markdown',
		})
	});

	if (!response.ok) {
		const errorData = (await response.json()) as { error: { message: string } };
		throw new Error(`OpenAI API error: ${errorData.error.message}`);
	}

	const data = (await response.json()) as { choices: Array<{ message: { content: string } }> };
	return data.choices[0].message.content;
};
