import { z } from 'zod';

// Schema for cargo data
export const cargoSchema = z.object({
	id: z.string(),
	commodity: z.string(),
	type: z.string(), // CRUDE
	quantity: z.number(),
	unit: z.string(), // mt
	layday: z.string(),
	canceling: z.string(),
	loadPort: z.string(),
	loadPortCountry: z.string(), // Country code for the flag
	dischargePort: z.string(),
	dischargePortCountry: z.string(), // Country code for the flag
	charterer: z.string(),
	freightRate: z.number(),
	status: z.enum(['scheduled', 'in-progress'])
});

export type Cargo = z.infer<typeof cargoSchema>;
