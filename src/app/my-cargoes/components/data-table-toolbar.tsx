import { Table } from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { useNavigate } from 'react-router';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTableViewOptions } from '@/components/data-table/data-table-view-options';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const navigate = useNavigate();

	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center space-x-2">
				<Button size="sm" onClick={() => navigate('/my-cargoes/new')}>
					<Plus /> Cargo
				</Button>
			</div>
			<div className="flex items-center gap-2">
				<Input
					placeholder="Search..."
					value={(table.getState().globalFilter as string) ?? ''}
					className="h-8 w-[150px] lg:w-[250px]"
					onChange={event => table.setGlobalFilter(event.target.value)}
				/>
				<DataTableViewOptions table={table} />
			</div>
		</div>
	);
}
