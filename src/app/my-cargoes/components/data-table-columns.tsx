import { ColumnDef } from '@tanstack/react-table';
import { ArrowRight, CircleDashed, Clock } from 'lucide-react';
import { Cargo } from '../data/schema';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { Badge } from '@/components/ui/badge';
import { formatAmount, formatQty } from '@/common/utils/formatUtils';
import { Button } from '@/components/ui/button';

export const columns: ColumnDef<Cargo>[] = [
	{
		accessorKey: 'id',
		meta: {
			label: 'Booking ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Booking ID" />,
		cell: ({ row }) => <div className="text-muted-foreground text-sm">{row.getValue('id')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'commodity',
		meta: {
			label: 'Commodity'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Commodity" />,
		cell: ({ row }) => <div className="font-medium">{row.getValue('commodity')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'quantity',
		meta: {
			label: 'Quantity'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Quantity" />,
		cell: ({ row }) => {
			const unit = row.original.unit;

			// Format the quantity with commas
			const formattedQuantity = formatQty(row.getValue('quantity'));

			return (
				<div className="font-medium">
					{formattedQuantity} <span className="text-muted-foreground">{unit}</span>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = row.getValue('status');
			return (
				<div>
					{status === 'scheduled' ? (
						<Badge variant="outline" className="text-muted-foreground rounded-full">
							<Clock className="mr-1 h-3 w-3" />
							Scheduled
						</Badge>
					) : (
						<Badge variant="outline" className="rounded-full">
							<CircleDashed className="mr-1 h-3 w-3 text-amber-500" />
							In Progress
						</Badge>
					)}
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true
	},
	// {
	// 	accessorKey: 'type',
	// 	meta: {
	// 		label: 'Type'
	// 	},
	// 	header: ({ column }) => <DataTableColumnHeader column={column} title="Type" />,
	// 	cell: ({ row }) => (
	// 		<Badge variant="secondary" className="text-muted-foreground text-2xs rounded-full">
	// 			<span className="font-medium tracking-wide">{row.getValue('type')}</span>
	// 		</Badge>
	// 	),
	// 	enableSorting: true,
	// 	enableHiding: true
	// },
	{
		accessorKey: 'layday',
		meta: {
			label: 'Layday'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Layday" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('layday')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'canceling',
		meta: {
			label: 'Canceling'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Canceling" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('canceling')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	// {
	// 	accessorKey: 'charterer',
	// 	meta: {
	// 		label: 'Charterer'
	// 	},
	// 	header: ({ column }) => <DataTableColumnHeader column={column} title="Charterer" />,
	// 	cell: ({ row }) => <div>{row.getValue('charterer')}</div>,
	// 	enableSorting: true,
	// 	enableHiding: true
	// },
	{
		accessorKey: 'loadPort',
		meta: {
			label: 'Load Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Load Port" />,
		cell: ({ row }) => {
			const loadPortCountry = row.original.loadPortCountry;
			return (
				<div className="font-normal">
					{row.getValue('loadPort')}, {loadPortCountry.toUpperCase()}
					{/* <div className="text-muted-foreground text-xs">12 Apr - 08:00</div> */}
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true
	},
	{
		id: 'arrow',
		cell: () => <ArrowRight className="text-muted-foreground mx-2 h-4 w-4" />,
		enableHiding: false,
		enableSorting: false
	},
	{
		accessorKey: 'dischargePort',
		meta: {
			label: 'Discharge Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Discharge Port" />,
		cell: ({ row }) => {
			const dischargePortCountry = row.original.dischargePortCountry;
			return (
				<div className="font-normal">
					{row.getValue('dischargePort')}, {dischargePortCountry.toUpperCase()}
					{/* <div className="text-muted-foreground text-xs">30 Apr - 08:00</div> */}
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'freightRate',
		meta: {
			label: 'Freight Rate'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Freight Rate" reverse />,
		cell: ({ row }) => (
			<div className="px-2 text-right font-medium">
				<span className="text-muted-foreground text-xs">USD</span> {formatAmount(row.getValue('freightRate'))}
			</div>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'cta',
		header: ({ column }) => <DataTableColumnHeader column={column} title="" reverse />,
		cell: () => (
			<div className="text-right">
				<Button variant="default" size="xs" className="text-foreground">
					New Voyage
				</Button>
			</div>
		),
		enableSorting: false,
		enableHiding: false
	}
];
