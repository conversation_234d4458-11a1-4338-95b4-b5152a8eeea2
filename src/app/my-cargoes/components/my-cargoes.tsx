import { useEffect, useState } from 'react';
import { Cargo } from '../data/schema';
import { cargoes } from '../data/data';
import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';

export default function MyCargoes() {
	const [data, setData] = useState<Cargo[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		// Simulate loading data
		setLoading(true);

		// In a real app, this would be an API call
		// For now, we'll use the mock data with a timeout
		const timer = setTimeout(() => {
			try {
				setData(cargoes);
			} catch (error) {
				console.error('Error loading cargoes:', error);
			} finally {
				setLoading(false);
			}
		}, 500);

		// Clean up the timer if the component unmounts
		return () => clearTimeout(timer);
	}, []);

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={data} columns={columns} />;
}
