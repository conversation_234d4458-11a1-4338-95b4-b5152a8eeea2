import * as React from 'react';
import { Anchor, Text } from 'lucide-react';
import AppointmentCargoItem from './appointment-cargo-item';
import { ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Input } from '@/components/ui/input';

const ports: ComboboxOption[] = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' },
	{ value: 'hamburg', label: 'Hamburg, DE' },
	{ value: 'antwerp', label: 'Antwerp, BE' },
	{ value: 'southampton', label: 'Southampton, UK' },
	{ value: 'shanghai', label: 'Shanghai, CN' },
	{ value: 'los-angeles', label: 'Los Angeles, US' },
	{ value: 'sydney', label: 'Sydney, AU' }
];

const loadingTerms: ComboboxOption[] = [
	{ value: 'shinc', label: 'SHINC' },
	{ value: 'fhinc', label: 'FHINC' }
];

export default function PortDetails() {
	const [, setSelectedPort] = React.useState<string | null>(null);
	const [selectedFunction] = React.useState<string | null>(null);
	const [, setSelectedTerms] = React.useState<string | null>(null);

	return (
		<div className="grid gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Port details</h3>
			<div className="grid grid-cols-[130px_minmax(0px,1fr)] items-center gap-x-12 gap-y-2 px-2 py-4 text-sm">
				<div className="text-muted-foreground flex items-center gap-2">
					<Anchor className="size-4" />
					Loading port
				</div>
				<div>
					<ComboboxButtonInline
						data={ports}
						placeholder="Choose port"
						onSelect={value => setSelectedPort(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Text className="size-4" />
					Loading rate
				</div>
				<div>
					<Input
						placeholder="00.00"
						className="hover:bg-accent h-auto w-auto border-none px-2 py-1 font-medium shadow-none"
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Text className="size-4" />
					Loading terms
				</div>
				<div>
					<ComboboxButtonInline
						data={loadingTerms}
						placeholder="Choose terms"
						onSelect={value => setSelectedTerms(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Text className="size-4" />
					Demurrage rate
				</div>
				<div>
					<Input
						placeholder="00.00"
						className="hover:bg-accent h-auto w-auto border-none px-2 py-1 font-medium shadow-none"
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Text className="size-4" />
					Despatch rate
				</div>
				<div>
					<Input
						placeholder="00.00"
						className="hover:bg-accent h-auto w-auto border-none px-2 py-1 font-medium shadow-none"
					/>
				</div>
			</div>
			{selectedFunction === 'cargo-ops' && <AppointmentCargoItem />}
		</div>
	);
}
