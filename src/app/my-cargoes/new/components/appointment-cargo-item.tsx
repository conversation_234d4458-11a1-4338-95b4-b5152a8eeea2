import * as React from 'react';
import { Plus, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { ComboboxOption } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const commodity: ComboboxOption[] = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'oil', label: 'Oil' },
	{ value: 'gas', label: 'Gas' },
	{ value: 'petroleum', label: 'Petroleum' }
];

const unit: ComboboxOption[] = [
	{ value: 'mts', label: 'mts' },
	{ value: 'kgs', label: 'kgs' },
	{ value: 'lts', label: 'lts' },
	{ value: 'cbm', label: 'cbm' }
];

const cargoFunction: ComboboxOption[] = [
	{ value: 'loading', label: 'Loading' },
	{ value: 'discharging', label: 'Discharging' },
	{ value: 'transshipment', label: 'Transshipment' },
	{ value: 'lightering', label: 'Lightering' }
];

const companies: ComboboxOption[] = [
	{ value: 'wilhelmsen-port-services', label: 'Wihelmsen Port Services S.A.' },
	{ value: 'varna-port-services', label: 'Varna Port Services Ltd.' },
	{ value: 'agencia-nabsa-maritima-sa', label: 'Agencia Maritima Nabsa SA' },
	{ value: 'blue-horizon-logistics', label: 'Blue Horizon Logistics' },
	{ value: 'oceanic-freight-solutions', label: 'Oceanic Freight Solutions' }
];

export default function AppointmentCargoItem() {
	const [, setSelectedCommodity] = React.useState<string | null>(null);
	const [, setSelectedUnit] = React.useState<string | null>(null);
	const [, setSelectedFunction] = React.useState<string | null>(null);
	const [, setSelectedCharterer] = React.useState<string | null>(null);
	const [date, setDate] = React.useState<Date>();

	return (
		<>
			<div className="grid gap-4 rounded-md border p-4 text-sm">
				<div className="flex items-center justify-between gap-2">
					Cargo #1
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
									<Trash />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Delete cargo</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<div className="grid grid-cols-[160px_minmax(0px,1fr)] items-center gap-2 text-sm md:grid-cols-[120px_minmax(0px,_1fr)_120px_minmax(0px,_1fr)]">
					<div className="text-muted-foreground truncate" title="C/P Date">
						Commodity
					</div>
					<div>
						<ComboboxButtonInline
							data={commodity}
							placeholder="Choose commodity"
							onSelect={value => setSelectedCommodity(value)}
						/>
					</div>
					<div className="text-muted-foreground truncate" title="C/P Date">
						Quantity
					</div>
					<div className="flex items-center gap-2">
						<Input
							placeholder="0.000.00"
							className="hover:bg-accent h-auto w-24 border-none px-2 py-1 font-medium shadow-none"
						/>
						<ComboboxButtonInline
							data={unit}
							placeholder="mts"
							onSelect={value => setSelectedUnit(value)}
						/>
					</div>
					<div className="text-muted-foreground truncate" title="C/P Date">
						Function
					</div>
					<div>
						<ComboboxButtonInline
							data={cargoFunction}
							placeholder="Choose function"
							onSelect={value => setSelectedFunction(value)}
						/>
					</div>
					<div className="text-muted-foreground truncate" title="C/P Date">
						C/P Date
					</div>
					<div>
						<Popover>
							<PopoverTrigger asChild>
								<Button
									size="xs"
									variant="outline"
									className={cn(
										'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
										!date && 'text-muted-foreground'
									)}
								>
									{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
							</PopoverContent>
						</Popover>
					</div>
					<div className="text-muted-foreground truncate" title="Laycan start">
						Laycan start
					</div>
					<div>
						<Popover>
							<PopoverTrigger asChild>
								<Button
									size="xs"
									variant="outline"
									className={cn(
										'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
										!date && 'text-muted-foreground'
									)}
								>
									{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
							</PopoverContent>
						</Popover>
					</div>
					<div className="text-muted-foreground truncate" title="Laycan end">
						Laycan end
					</div>
					<div>
						<Popover>
							<PopoverTrigger asChild>
								<Button
									size="xs"
									variant="outline"
									className={cn(
										'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
										!date && 'text-muted-foreground'
									)}
								>
									{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="start">
								<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
							</PopoverContent>
						</Popover>
					</div>
					<div className="text-muted-foreground truncate" title="Charterer">
						Charterer
					</div>
					<div>
						<ComboboxButtonInline
							data={companies}
							placeholder="Choose charterer"
							onSelect={value => setSelectedCharterer(value)}
						/>
					</div>
					<div className="text-muted-foreground truncate" title="Charterer’s nominated agent">
						Charterer’s nominated agent
					</div>
					<div>
						<ComboboxButtonInline
							data={companies}
							placeholder="Choose nominated agent"
							onSelect={value => setSelectedCharterer(value)}
						/>
					</div>
				</div>
			</div>
			<div className="pt-2">
				<Button variant="secondary" size="sm" className="flex items-center gap-1">
					<Plus className="h-4 w-4" />
					Add cargo
				</Button>
			</div>
		</>
	);
}
