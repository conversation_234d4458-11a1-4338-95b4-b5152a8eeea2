import * as React from 'react';
import { Building2, CalendarIcon, CircleDotDashed, Hash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

const statuses: ComboboxOption[] = [
	{ value: 'scheduled', label: 'Scheduled' },
	{ value: 'in-progress', label: 'In progress' },
	{ value: 'completed', label: 'Completed' }
];

const counterparties: ComboboxOption[] = [
	{ value: '1', label: 'WPS Legal Singapore' },
	{ value: '2', label: 'WPS Legal Europe' },
	{ value: '3', label: 'WPS Legal Australia' }
];

export default function CargoDetails() {
	const [, setSelectedVessel] = React.useState<string | null>(null);
	const [, setSelectedLegalEntity] = React.useState<string | null>(null);
	const [date, setDate] = React.useState<Date>();

	return (
		<div className="grid grid-cols-[auto_minmax(0px,1fr)] items-center gap-x-12 gap-y-2 px-2 py-4 text-sm">
			<div className="text-muted-foreground flex items-center gap-2">
				<CircleDotDashed className="h-4 w-4" />
				Status
			</div>
			<div>
				<ComboboxButtonInline
					data={statuses}
					placeholder="Choose status"
					onSelect={value => setSelectedVessel(value)}
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Hash className="h-4 w-4" />
				Cargo ID
			</div>
			<div>
				<Input
					placeholder="Enter cargo ID"
					className="hover:bg-accent h-auto w-auto border-none px-2 py-1 font-medium shadow-none"
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Building2 className="h-4 w-4" />
				Counterparty
			</div>
			<div>
				<ComboboxButtonInline
					data={counterparties}
					placeholder="Choose counterparty"
					onSelect={value => setSelectedLegalEntity(value)}
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<CalendarIcon className="h-4 w-4" />
				C/P Date
			</div>
			<div>
				<Popover>
					<PopoverTrigger asChild>
						<Button
							size="xs"
							variant="outline"
							className={cn(
								'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
								!date && 'text-muted-foreground'
							)}
						>
							{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
					</PopoverContent>
				</Popover>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<CalendarIcon className="h-4 w-4" />
				Layday
			</div>
			<div>
				<Popover>
					<PopoverTrigger asChild>
						<Button
							size="xs"
							variant="outline"
							className={cn(
								'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
								!date && 'text-muted-foreground'
							)}
						>
							{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
					</PopoverContent>
				</Popover>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<CalendarIcon className="h-4 w-4" />
				Canceling
			</div>
			<div>
				<Popover>
					<PopoverTrigger asChild>
						<Button
							size="xs"
							variant="outline"
							className={cn(
								'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
								!date && 'text-muted-foreground'
							)}
						>
							{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}
