import { Table } from '@tanstack/react-table';

import { Ship, ListFilter, CircleDashed, Download } from 'lucide-react';
import { statuses } from '@/constants';
import { Button } from '@/components/ui/button';
import { DataTableDisplayOptions } from '@/components/data-table/data-table-display-options';
import { DataTableSearch } from '@/components/data-table/data-table-search';
import {
	DropdownMenu,
	DropdownMenuTrigger,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { DataTableFilterCommand } from '@/components/data-table/data-table-filter-command';
import { DataTableCompactFilter } from '@/components/data-table/data-table-compact-filter';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

function getVesselValues<TData>(table: Table<TData>) {
	const vesselColumn = table.getColumn('vessel');
	const columnValues = vesselColumn
		? Array.from(new Set((vesselColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => ({
		label: value,
		value,
		icon: undefined
	}));
}
function getStatusValues<TData>(table: Table<TData>) {
	const statusColumn = table.getColumn('status');
	const columnValues = statusColumn
		? Array.from(new Set((statusColumn.getFacetedUniqueValues() as Map<string, number>).keys()))
		: [];
	return columnValues.map(value => {
		const status = statuses.find(status => status.value === value);
		return {
			label: status?.label ?? value,
			value: status?.value ?? value,
			icon: status?.icon
		};
	});
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;

	const dropdownMenuFilter = (
		<DropdownMenuContent align="start" className="min-w-48">
			<DropdownMenuLabel>Filters</DropdownMenuLabel>
			<DropdownMenuSeparator />
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<Ship className="mr-1 size-3.5" />
					Vessel
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('vessel')}
							title="Vessel"
							options={getVesselValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
			<DropdownMenuSub>
				<DropdownMenuSubTrigger>
					<CircleDashed className="mr-1 size-3.5" />
					Status
				</DropdownMenuSubTrigger>
				<DropdownMenuPortal>
					<DropdownMenuSubContent>
						<DataTableFilterCommand
							column={table.getColumn('status')}
							title="Status"
							options={getStatusValues(table)}
						/>
					</DropdownMenuSubContent>
				</DropdownMenuPortal>
			</DropdownMenuSub>
		</DropdownMenuContent>
	);

	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center">
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						{isFiltered ? (
							<Button variant="ghost" size="xs" className="w-0 px-0"></Button>
						) : (
							<Button variant="ghost" size="xs">
								<ListFilter />
								Filter
							</Button>
						)}
					</DropdownMenuTrigger>
					{dropdownMenuFilter}
				</DropdownMenu>

				{table.getColumn('vessel') && (
					<DataTableCompactFilter
						column={table.getColumn('vessel')}
						title="Vessel"
						options={getVesselValues(table)}
					/>
				)}
				{table.getColumn('status') && (
					<DataTableCompactFilter
						column={table.getColumn('status')}
						title="Status"
						options={getStatusValues(table)}
					/>
				)}
				{isFiltered && (
					<TooltipProvider>
						<DropdownMenu>
							<Tooltip>
								<TooltipTrigger asChild>
									<DropdownMenuTrigger asChild>
										<Button variant="ghost" size="icon" className="h-7 w-7">
											<ListFilter />
										</Button>
									</DropdownMenuTrigger>
								</TooltipTrigger>
								<TooltipContent>Filter</TooltipContent>
							</Tooltip>
							{dropdownMenuFilter}
						</DropdownMenu>
					</TooltipProvider>
				)}
			</div>
			<div className="flex items-center gap-2">
				<DataTableSearch table={table} />
				<Button variant="ghost" size="xs">
					<Download />
					Export
				</Button>
				<DataTableDisplayOptions table={table} />
			</div>
		</div>
	);
}
