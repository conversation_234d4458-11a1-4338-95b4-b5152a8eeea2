import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { useLaytimes } from '@/hooks/laytime/index';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';
import { LaytimeStatusEnum } from '@/graphql';

export default function Laytimes() {
	const { laytime, loading } = useLaytimes({ status: { ne: LaytimeStatusEnum.Draft } });

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={laytime} columns={columns} />;
}
