import { Download } from 'lucide-react';
import { statuses } from '@/constants';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';

export function LaytimeHeader() {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();
	const statusObj = statuses.find(s => s.value === laytime?.general.status);
	return (
		<div className="flex items-center gap-4">
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">Laytime calculation</h2>
				<Badge variant="outline" className="rounded-full">
					{statusObj?.icon && <statusObj.icon className={cn('h-3 w-3', statusObj?.color)} />}
					<span>{statusObj?.label}</span>
				</Badge>
			</div>
			<Button variant="outline" size="sm">
				<Download />
				Download
			</Button>
		</div>
	);
}
