// eslint-disable-next-line react-compiler/react-compiler
/* eslint-disable react-hooks/exhaustive-deps */

import { useEffect } from 'react';
import { useParams } from 'react-router';
import { LaytimeActivity } from './laytime-activity';
import { LaytimeDocuments } from './laytime-documents';
import { LaytimeExclusions } from './laytime-exclusions';
import { LaytimeCalculation } from './laytime-calculation';
import { LaytimeGeneral } from './laytime-general';
import { LaytimeSkeleton } from './laytime-skeleton';
import { LaytimeFooter } from './laytime-footer';
import { Separator } from '@/components/ui/separator';
import { useLaytime } from '@/hooks/laytime';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';

export function Laytime() {
	const params = useParams();
	const slug = params.id as string;
	const { laytime, loading: laytimeLoading } = useLaytime(slug);

	const { dispatch } = useLaytimeContext();

	useEffect(() => {
		if (!laytimeLoading) {
			dispatch({
				type: 'INITIALIZE_STATE',
				payload: {
					laytime: {
						...laytime,
						documents: [
							{
								name: 'Notice of readines',
								type: 'docx'
							},
							{
								name: 'Statement of facts',
								type: 'pdf'
							},
							{
								name: 'C/P-Terms2025',
								type: 'pdf'
							}
						]
					},
					charterers: [],
					ports: []
				}
			});
		}
	}, [laytimeLoading]);

	if (laytimeLoading) {
		return <LaytimeSkeleton />;
	}

	return (
		<div className="flex w-full flex-col items-center">
			<div className="flex h-full w-full flex-col items-center overflow-auto">
				<div className="w-full max-w-4xl px-4">
					<div className="flex flex-col gap-4">
						<LaytimeGeneral />

						{laytime?.calculation.length > 0 && (
							<>
								<Separator />
								<LaytimeCalculation />
							</>
						)}
						{laytime?.exclusions.length > 0 && (
							<>
								<Separator />
								<LaytimeExclusions />
							</>
						)}
						<Separator />
						<LaytimeDocuments />
						{laytime?.activity.length > 0 && (
							<>
								<Separator />
								<LaytimeActivity />
							</>
						)}
					</div>
				</div>
			</div>
			<LaytimeFooter />
		</div>
	);
}
