import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';

export function LaytimeSkeleton() {
	return (
		<div className="flex w-full flex-col items-center overflow-auto">
			<div className="w-full max-w-4xl px-4">
				<div className="flex flex-col gap-4">
					<div className="flex h-8 items-center justify-between gap-4">
						<Skeleton className="h-3 w-[35%]" />
						<Skeleton className="h-3 w-[10%]" />
					</div>
					<div className="flex flex-col items-center gap-6 lg:flex-row">
						<div className="w-full flex-1">
							<div className="grid grid-cols-2 gap-x-12 gap-y-8">
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
								<Skeleton className="h-3 w-[75%]" />
							</div>
						</div>
						<div className="w-full flex-1">
							<div className="bg-card text-card-foreground rounded-xl border shadow-sm">
								<div className="flex h-16 items-center px-6">
									<Skeleton className="h-3 w-[35%]" />
								</div>
								<div className="p-6 pt-0">
									<div className="grid grid-cols-2 gap-x-4 gap-y-8">
										<Skeleton className="h-3 w-[75%]" />
										<Skeleton className="h-3 w-[100%]" />
										<Skeleton className="h-3 w-[75%]" />
										<Skeleton className="h-3 w-[100%]" />
										<Skeleton className="h-3 w-[75%]" />
										<Skeleton className="h-3 w-[100%]" />
										<Skeleton className="h-3 w-[75%]" />
										<Skeleton className="h-3 w-[100%]" />
										<Skeleton className="h-3 w-[75%]" />
										<Skeleton className="h-3 w-[100%]" />
									</div>
								</div>
							</div>
						</div>
					</div>
					<Separator />
					<div className="flex flex-col gap-2 py-4">
						<h3>
							<Skeleton className="h-3 w-[15%]" />
						</h3>
						<div className="py-2">
							<table className="w-full">
								<tbody>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
									<tr className="h-10 px-2 text-left align-middle">
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
										<td>
											<Skeleton className="h-3 w-[50%]" />
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
