import { Anchor, ArrowRightLeft, Box, BriefcaseBusiness, Calendar, GitPullRequestArrow, Hash } from 'lucide-react';
import { LaytimeHeader } from './laytime-header';

import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { operations, laytimeTypes } from '@/constants';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';

export function LaytimeGeneral() {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();
	const laytimeTypeObj = laytimeTypes.find(l => l.value === laytime?.summary.laytimeType);
	const operation = operations.find(o => o.value === laytime?.general.operation);

	return (
		<>
			<LaytimeHeader />
			<div className="flex flex-col items-center gap-6 lg:flex-row">
				<div className="w-full flex-1">
					<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-4 text-sm">
						<div className="text-muted-foreground flex items-center gap-2">
							<Hash className="h-4 w-4" />
							Voyage ID
						</div>
						<div>
							<Badge variant="outline" className="rounded-full">
								<GitPullRequestArrow className="text-primary h-3 w-3" />
								{laytime?.general.id}
							</Badge>
						</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<Anchor className="h-4 w-4" />
							Port
						</div>
						<div>
							<span className="font-medium">
								{laytime?.general.port?.name}, {laytime?.general.port?.countryCode}
							</span>
						</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<ArrowRightLeft className="h-4 w-4" />
							Operation
						</div>
						<div>{operation?.label}</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<Box className="h-4 w-4" />
							Cargo
						</div>
						<div>
							<span className="font-medium">
								{laytime?.general.cargo?.quantity} {laytime?.general.cargo?.unit}
							</span>{' '}
							- {laytime?.general.cargo?.type}
						</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<BriefcaseBusiness className="h-4 w-4" />
							Counterparty
						</div>
						<div>{laytime?.general.party.name}</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<Calendar className="h-4 w-4" />
							C/P Date
						</div>
						<div>{laytime?.general.cpDate}</div>
						<div className="text-muted-foreground flex items-center gap-2">
							<Calendar className="h-4 w-4" />
							Laycan
						</div>
						<div>
							{laytime?.general.laycanFrom} - {laytime?.general.laycanTo}
						</div>
					</div>
				</div>
				{/* TODO: Detach <LaytimeSummary /> API: /laytime/summary */}
				<div className="w-full flex-1">
					<Card>
						<CardHeader>
							<CardTitle>On {laytime?.summary.laytimeType}</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-[160px_minmax(0px,1fr)] gap-y-1.5 text-sm">
								<div>{operation?.label} rate</div>
								<div className="text-right">
									<span className="font-medium">{laytime?.summary.rateQty}</span>&nbsp;
									<span className="text-muted-foreground text-xs">{laytime?.summary.rateSuf}</span>
								</div>
								<div></div>
								<Separator orientation="horizontal" />
								<div>
									<span className="capitalize">{laytime?.summary.laytimeType}</span> rate
								</div>
								<div className="text-right">
									<span className="text-muted-foreground text-xs">
										{laytime?.summary.rateCurrency}
									</span>
									&nbsp;
									<span className="font-medium">{laytime?.summary.rateAmount}</span>
								</div>
								<div></div>
								<Separator orientation="horizontal" />
								<div>Time allowed</div>
								<div className="text-right">{laytime?.summary.timeAllowed}</div>
								<div></div>
								<Separator orientation="horizontal" />
								<div>Time used</div>
								<div className="text-right">{laytime?.summary.timeUsed}</div>
								<div></div>
								<Separator orientation="horizontal" />
								<div>On {laytime?.summary.laytimeType}</div>
								<div className={cn('text-right', laytimeTypeObj?.color)}>
									{laytime?.summary.varianceTime}
								</div>
								<Separator orientation="horizontal" />
								<Separator orientation="horizontal" />
								<div className="text-lg">Amount due</div>
								<div className="text-right">
									<span className="text-muted-foreground text-xs">
										{laytime?.summary.rateCurrency}
									</span>
									&nbsp;
									<span className={cn('text-lg font-medium', laytimeTypeObj?.color)}>
										{laytime?.summary.amountDue}
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</>
	);
}
