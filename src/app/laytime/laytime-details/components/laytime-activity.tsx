import { formatDistanceToNow } from 'date-fns';
import { Text } from 'lucide-react';
import { AvatarFallback } from '@radix-ui/react-avatar';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { statuses } from '@/constants';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';

export function LaytimeActivity() {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();
	if (laytime?.activity.length === 0) return null;
	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Activity</h3>
			<ul className="a-activity-list p-2">
				{laytime?.activity.map(item => {
					const statusActivObj = statuses.find(s => s.value === item.status);
					return (
						<li key={`activity-${item.operator.id}-${item.status}-${item.date}`} className="flex flex-col">
							<div className="flex flex-row items-center gap-4">
								<Avatar className="h-5 w-5 rounded-sm">
									<AvatarImage src={item.operator?.avatar} alt={item.operator?.firstName} />
									<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
										{item.operator?.firstName?.charAt(0)} {item.operator?.lastName?.charAt(0)}
									</AvatarFallback>
								</Avatar>
								<div className="flex flex-row items-center gap-2 text-sm">
									<span className="font-medium">
										{item.operator?.firstName?.charAt(0)}. {item.operator?.lastName}
									</span>
									<span className="text-muted-foreground hidden sm:block">
										{statusActivObj?.activityText}
									</span>
									<Badge variant="outline" className="rounded-full">
										{statusActivObj?.icon && (
											<statusActivObj.icon className={cn('h-3 w-3', statusActivObj?.color)} />
										)}
										<span>{statusActivObj?.label}</span>
									</Badge>
									<span className="text-muted-foreground text-lg">•</span>
									<span className="text-muted-foreground">
										{formatDistanceToNow(new Date(item.date), {
											addSuffix: true
										})}
									</span>
								</div>
							</div>
							<div className="relative flex min-h-4 flex-row">
								<Separator
									orientation="vertical"
									className="a-list-sep absolute left-[9px]"
								></Separator>
								{item.comment.length ? (
									<div className="flex flex-row items-center gap-2 px-9 py-1 pb-4 text-sm">
										<Text className="h-3 w-3" />
										<i>{item.comment}</i>
									</div>
								) : (
									''
								)}
							</div>
						</li>
					);
				})}
			</ul>
		</div>
	);
}
