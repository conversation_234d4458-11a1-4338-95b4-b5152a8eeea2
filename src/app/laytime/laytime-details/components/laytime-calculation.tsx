import * as React from 'react';
import { format } from 'date-fns';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';
import { Calculation } from '@/hooks/laytime/types';

export function LaytimeCalculation() {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();

	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Calculation</h3>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead className="w-[240px]">Event</TableHead>
						<TableHead className="w-[120px]">Date</TableHead>
						<TableHead className="w-[80px]">Time</TableHead>
						<TableHead>Remarks</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{laytime?.calculation.map(calculation => {
						const date = format(new Date(calculation.date), 'd MMM yy');
						const time = format(new Date(calculation.date), 'HH:mm');

						const renderCalculationRow = (calculation: Calculation) => {
							switch (true) {
								case calculation.laytimeStarts:
									return (
										<React.Fragment
											key={`${laytime?.id}-${calculation.event}-${calculation.date}-group`}
										>
											<TableRow
												key={`${laytime?.id}-${calculation.event}-${calculation.date}-event`}
											>
												<TableCell>
													<div className="flex items-center gap-2">
														{/* TODO: Add event type in calculationSchema */}
														{/* TODO: Extend data model */}
														<span className="block h-1.5 w-1.5 rounded-full bg-red-500"></span>
														<span>{calculation.event}</span>
													</div>
												</TableCell>
												<TableCell>
													<span className="text-muted-foreground">{date}</span>
												</TableCell>
												<TableCell>
													<span className="font-medium">{time}</span>
												</TableCell>
												<TableCell>
													<i className="text-muted-foreground">{calculation.remarks}</i>
												</TableCell>
											</TableRow>
											<TableRow
												key={`${laytime?.id}-${calculation.event}-${calculation.date}-laytime`}
											>
												<TableCell colSpan={4} className="text-center">
													<span className="bg-primary rounded-full px-4 py-1.5 text-xs text-white">
														<b>Laytime Starts</b>&nbsp;&nbsp;
														{date} @ {time}
													</span>
												</TableCell>
											</TableRow>
										</React.Fragment>
									);

								case calculation.demurrageStarts:
									return (
										<React.Fragment
											key={`${laytime?.id}-${calculation.event}-${calculation.date}-group`}
										>
											<TableRow
												key={`${laytime?.id}-${calculation.event}-${calculation.date}-event`}
											>
												<TableCell>
													<div className="flex items-center gap-2">
														<span className="block h-1.5 w-1.5 rounded-full bg-red-500"></span>
														<span>{calculation.event}</span>
													</div>
												</TableCell>
												<TableCell>
													<span className="text-muted-foreground">{calculation.date}</span>
												</TableCell>
												<TableCell>
													<span className="font-medium">{time}</span>
												</TableCell>
												<TableCell>
													<i className="text-muted-foreground">{calculation.remarks}</i>
												</TableCell>
											</TableRow>
											<TableRow
												key={`${laytime?.id}-${calculation.event}-${calculation.date}-laytime`}
											>
												<TableCell colSpan={4} className="text-center">
													<span className="rounded-full bg-red-700 px-4 py-1.5 text-xs text-white">
														<b>On demurrage</b>&nbsp;&nbsp;
														{date} @ {time}
													</span>
												</TableCell>
											</TableRow>
										</React.Fragment>
									);

								case !calculation.laytimeStarts || !!calculation.demurrageStarts:
									return (
										<TableRow key={`${laytime?.id}-${calculation.event}-${calculation.date}`}>
											<TableCell>
												<div className="flex items-center gap-2">
													<span className="block h-1.5 w-1.5 rounded-full bg-slate-500"></span>
													<span>{calculation.event}</span>
												</div>
											</TableCell>
											<TableCell>
												<span className="text-muted-foreground">{date}</span>
											</TableCell>
											<TableCell>
												<span className="font-medium">{time}</span>
											</TableCell>
											<TableCell>
												<i className="text-muted-foreground">{calculation.remarks}</i>
											</TableCell>
										</TableRow>
									);

								default:
									return null;
							}
						};

						return renderCalculationRow(calculation);
					})}
				</TableBody>
			</Table>
		</div>
	);
}
