import { File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';

export function LaytimeDocuments() {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();
	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Documents</h3>
			<div className="flex flex-col gap-2 p-2 sm:flex-row">
				{laytime?.documents.map(document => (
					<Button key={`doc-${document.name}-${document.type}`} variant="outline" size="sm">
						<File />
						{document.name}.{document.type}
					</Button>
				))}
			</div>
		</div>
	);
}
