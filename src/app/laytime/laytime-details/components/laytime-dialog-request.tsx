import { useState } from 'react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from '@/components/ui/dialog';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';
import { LaytimeStatusEnum } from '@/graphql';
import { useUpdateLaytimeStatus } from '@/hooks/laytime/use-update-laytime-status';

interface DialogRequestProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

const status = LaytimeStatusEnum.ChangesRequested;
const operatorId = '02a646f2-24cb-48d9-b968-0d20a088b016';
export function LaytimeDialogRequest({ open, onOpenChange }: DialogRequestProps) {
	const { state, dispatch } = useLaytimeContext();
	const { updateLaytimeStatus } = useUpdateLaytimeStatus();
	const [message, setMessage] = useState('');

	const handleRequest = async () => {
		onOpenChange(false);
		if (state.laytimeData?.id) {
			await updateLaytimeStatus(state.laytimeData?.id, status, message, operatorId).then(() => {
				dispatch({
					type: 'UPDATE_LAYTIME_STATUS',
					payload: status
				});
				dispatch({
					type: 'ADD_ACTIVITY_LOG',
					payload: {
						comment: message,
						operator: {
							avatar: 'https://avatars.githubusercontent.com/u/9770578',
							firstName: 'Brett',
							lastName: 'Zulauf',
							id: operatorId
						},
						status,
						date: new Date().toISOString()
					}
				});
				toast('Changes requested', {
					duration: 3000,
					description: format(new Date(), 'd MMM yy HH:mm a'),
					action: {
						label: 'Undo',
						onClick: () => console.log('Undo')
					}
				});
			});
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Request Changes</DialogTitle>
					<DialogDescription>Subtitle/info text</DialogDescription>
				</DialogHeader>
				<Textarea placeholder="Write a message" value={message} onChange={e => setMessage(e.target.value)} />
				<DialogFooter>
					<Button type="submit" variant="secondary" onClick={() => onOpenChange(false)}>
						Cancel
					</Button>
					<Button type="submit" onClick={() => void handleRequest()}>
						Request
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
