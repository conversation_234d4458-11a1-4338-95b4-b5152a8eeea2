import { useState } from 'react';
import { LaytimeDialogApprove } from './laytime-dialog-approve';
import { LaytimeDialogRequest } from './laytime-dialog-request';
import { Button } from '@/components/ui/button';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';
import { LaytimeStatusEnum } from '@/graphql';

const ActionButtons = () => {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();

	const [openApprove, setOpenApprove] = useState(false);
	const [openRequest, setOpenRequest] = useState(false);

	const disabled =
		laytime?.general.status === LaytimeStatusEnum.Approved ||
		laytime?.general.status === LaytimeStatusEnum.ChangesRequested ||
		laytime?.general.status === '' ||
		!laytime?.general.status;

	return (
		<>
			<LaytimeDialogApprove open={openApprove} onOpenChange={setOpenApprove} />
			<LaytimeDialogRequest open={openRequest} onOpenChange={setOpenRequest} />
			<Button
				disabled={disabled}
				size="sm"
				variant="secondary"
				onClick={() => void setOpenRequest(open => !open)}
			>
				Request changes
			</Button>{' '}
			<Button disabled={disabled} size="sm" onClick={() => setOpenApprove(open => !open)}>
				Approve
			</Button>
		</>
	);
};

export function LaytimeFooter() {
	return (
		<div className="bg-panel flex w-full flex-col items-center border-t shadow-2xl">
			<div className="w-full max-w-4xl px-4">
				<div className="flex flex-row items-center justify-end gap-2 py-2">
					<ActionButtons />
				</div>
			</div>
		</div>
	);
}
