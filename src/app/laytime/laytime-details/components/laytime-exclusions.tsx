import { format } from 'date-fns';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useLaytimeContext } from '@/app/admin/laytime/contexts/laytime-context';
import { formatTimeAsDHM } from '@/common/utils/formatUtils';

export function LaytimeExclusions() {
	const {
		state: { laytimeData: laytime }
	} = useLaytimeContext();
	return (
		<div className="flex flex-col gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Exclusions</h3>
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead className="w-[110px]">Date from</TableHead>
						<TableHead className="w-[80px]">From</TableHead>
						<TableHead className="w-[110px]">Date to</TableHead>
						<TableHead className="w-[80px]">To</TableHead>
						<TableHead>Event</TableHead>
						<TableHead className="w-[110px] text-right">Duration</TableHead>
						<TableHead className="w-[60px] text-center">%</TableHead>
						<TableHead className="w-[110px] text-right">Time used</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{laytime?.exclusions.map(exclusion => (
						<TableRow key={`exclusion-${exclusion.dateFrom}-${exclusion.dateTo}-${exclusion.id}`}>
							<TableCell>
								<span className="text-muted-foreground">{format(exclusion.dateFrom, 'dd MMM yy')}</span>
							</TableCell>
							<TableCell>
								<span className="font-medium">{format(exclusion.dateFrom, 'HH:mm')}</span>
							</TableCell>
							<TableCell>
								<span className="text-muted-foreground">{format(exclusion.dateTo, 'dd MMM yy')}</span>
							</TableCell>
							<TableCell>
								<span className="font-medium">{format(exclusion.dateTo, 'HH:mm')}</span>
							</TableCell>
							<TableCell>{exclusion.event}</TableCell>
							<TableCell className="text-right">{formatTimeAsDHM(Number(exclusion.duration))}</TableCell>
							<TableCell className="text-center">{exclusion.percentage}</TableCell>
							<TableCell className="text-right">{formatTimeAsDHM(Number(exclusion.timeUsed))}</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	);
}
