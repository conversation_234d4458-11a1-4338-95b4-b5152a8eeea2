import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const LaytimeDetailsLayout = lazy(() => import('./laytime-details/layout'));
const LaytimePage = lazy(() => import('./page'));
const LaytimeDetailsPage = lazy(() => import('./laytime-details/page'));

export default function Ports() {
	return (
		<Routes>
			<Route index element={<LaytimePage />} />
			<Route path=":id" element={<LaytimeDetailsLayout />}>
				<Route index element={<LaytimeDetailsPage />} />
			</Route>
		</Routes>
	);
}
