import { useEffect, useState } from 'react';
import { ports } from '../data/schema';
import { DataTable } from './data-table';
import { columns } from './data-table-columns';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';

export default function Ports() {
	const [ports, setPorts] = useState<ports[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			try {
				const { default: response } = await import('../../../../api/ports.json'); // Replace with your API route
				const data = response as ports[];
				setPorts(data);
			} catch (error) {
				console.error('Error fetching ports:', error);
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={ports} columns={columns} />;
}
