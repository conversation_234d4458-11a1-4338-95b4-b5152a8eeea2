import { ColumnDef } from '@tanstack/react-table';
import { Anchor } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import { ports } from '../data/schema';

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';

export const columns: ColumnDef<ports>[] = [
	{
		accessorKey: 'port',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" />,
		cell: ({ row }) => (
			<div className="flex items-center gap-2">
				<Anchor className="text-primary h-4 w-4" />
				<div className="font-medium">{row.getValue('port')}</div>
			</div>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'country.name',
		meta: {
			label: 'Country'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Country" />,
		cell: ({ row }) => {
			const country = row.original.country;

			return (
				<div className="flex items-center gap-2">
					<CircleFlag countryCode={country.code} className="h-4" />
					<span>{country.name}</span>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'timezone',
		meta: {
			label: 'Timezone'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Timezone" />,
		cell: ({ row }) => <div>{row.getValue('timezone')}</div>,
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'updated',
		meta: {
			label: 'Last updated'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Last updated" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('updated')}</div>,
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	}
];
