import { z } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.

const countrySchema = z.object({
	name: z.string(), // Country name is a string
	code: z.string() // Country code is a string
});

export const portsSchema = z.object({
	uid: z.string(),
	id: z.string(),
	port: z.string(),
	country: countrySchema,
	timezone: z.string(),
	updated: z.string()
});

export type ports = z.infer<typeof portsSchema>;
