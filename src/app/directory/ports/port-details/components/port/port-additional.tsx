import { Check, X } from 'lucide-react';

export default function PortAdditional() {
	return (
		<div className="grid gap-4">
			<div className="grid gap-2 px-2 text-sm sm:grid-cols-[140px_minmax(140px,_1fr)_140px_minmax(140px,_1fr)]">
				<div className="text-muted-foreground truncate" title="Harbor size">
					Harbor size
				</div>
				<div className="sm:px-2">Large</div>
				<div className="text-muted-foreground truncate" title="Water density">
					Water density
				</div>
				<div className="sm:px-2">1.000 - 1.020</div>
				<div className="text-muted-foreground truncate" title="Harbor type">
					Harbor type
				</div>
				<div className="sm:px-2">River natural</div>
				<div className="text-muted-foreground truncate" title="Notice of arrival deadline">
					Notice of arrival deadline
				</div>
				<div className="flex items-center gap-2 sm:px-2">
					<Check className="size-4 text-emerald-500" /> Yes
				</div>
				<div className="text-muted-foreground truncate" title="Shelter">
					Shelter
				</div>
				<div className="sm:px-2">Good</div>
				<div className="text-muted-foreground truncate" title="Cargo manifest deadline">
					Cargo manifest deadline
				</div>
				<div className="sm:px-2">---</div>
				<div className="text-muted-foreground truncate" title="Water salinity">
					Water salinity
				</div>
				<div className="sm:px-2">Fresh</div>
				<div className="text-muted-foreground truncate" title="US Representative">
					US Representative
				</div>
				<div className="flex items-center gap-2 sm:px-2">
					<X className="size-4 text-red-500" /> No
				</div>
			</div>
		</div>
	);
}
