import PortGeneral from './port-general';
import PortCoordinates from './port-coordinates';
import PortAdditional from './port-additional';
import PortRestrictions from './port-restrictions';
import PortHealth from './port-health';
import PortPilotageTowage from './port-pilotage-towage';
import PortFacilities from './port-facilities';
import PortServices from './port-services';
import PortSupplies from './port-supplies';
import { Separator } from '@/components/ui/separator';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

export default function PortOverview() {
	return (
		<div className="grid gap-1">
			<PortGeneral />
			<Separator />
			<Accordion type="single" defaultValue="coordinates" collapsible className="w-full">
				<AccordionItem value="coordinates">
					<AccordionTrigger className="px-2 text-base font-semibold">Port Coordinates</AccordionTrigger>
					<AccordionContent>
						<PortCoordinates />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="additional">
					<AccordionTrigger className="px-2 text-base font-semibold">
						Additional Port Information
					</AccordionTrigger>
					<AccordionContent>
						<PortAdditional />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="restrictions">
					<AccordionTrigger className="px-2 text-base font-semibold">Restrictions</AccordionTrigger>
					<AccordionContent>
						<PortRestrictions />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="health">
					<AccordionTrigger className="px-2 text-base font-semibold">Health Information</AccordionTrigger>
					<AccordionContent>
						<PortHealth />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="pilotage">
					<AccordionTrigger className="px-2 text-base font-semibold">Pilotage and Towage</AccordionTrigger>
					<AccordionContent>
						<PortPilotageTowage />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="facilities">
					<AccordionTrigger className="px-2 text-base font-semibold">Facilities</AccordionTrigger>
					<AccordionContent>
						<PortFacilities />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="services">
					<AccordionTrigger className="px-2 text-base font-semibold">Services</AccordionTrigger>
					<AccordionContent>
						<PortServices />
					</AccordionContent>
				</AccordionItem>
				<AccordionItem value="supplies">
					<AccordionTrigger className="px-2 text-base font-semibold">Supplies</AccordionTrigger>
					<AccordionContent>
						<PortSupplies />
					</AccordionContent>
				</AccordionItem>
			</Accordion>
			<Separator />
			<div className="text-muted-foreground px-2 py-4 text-xs">Last updated on 17 Nov 2024</div>
		</div>
	);
}
