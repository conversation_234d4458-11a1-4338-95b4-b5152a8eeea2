import portImage from '@/static/media/NLRTM.jpg';
import { AspectRatio } from '@/components/ui/aspect-ratio';

export default function PortGeneral() {
	return (
		<div className="flex flex-col items-start gap-6 py-4 lg:flex-row">
			<div className="grid w-full flex-1 grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-3 px-2 text-sm">
				<div className="text-muted-foreground">Country</div>
				<div>The Netherlands</div>
				<div className="text-muted-foreground">Locode</div>
				<div>NLRTM</div>
				<div className="text-muted-foreground">Alternative names</div>
				<div>---</div>
				<div className="text-muted-foreground">Operational Status</div>
				<div>Operational</div>
				<div className="text-muted-foreground">Region</div>
				<div>North Sea</div>
				<div className="text-muted-foreground">Subregion</div>
				<div>Ara</div>
				<div className="text-muted-foreground">UN Country subdivision</div>
				<div>ZH</div>
				<div className="text-muted-foreground">ISPS</div>
				<div>---</div>
				<div className="text-muted-foreground">Time zone</div>
				<div>GMT+1</div>
				<div className="text-muted-foreground">Load lines</div>
				<div>Winter / 01 Jan - 31 Dec</div>
			</div>
			<div className="w-full flex-1">
				<AspectRatio ratio={16 / 9}>
					<img src={portImage} alt="Port" className="rounded-lg object-cover" />
				</AspectRatio>
			</div>
		</div>
	);
}
