import { PortHeader } from './port-header';
import PortOverview from './port/port-overview';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

export function Port() {
	return (
		<>
			<PortHeader />
			<Tabs defaultValue="port" className="w-full">
				<TabsList className="h-auto w-full justify-start gap-4 rounded-none border-b bg-transparent p-0">
					<TabsTrigger
						value="port"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Port info
					</TabsTrigger>
					<TabsTrigger
						value="terminals"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Terminals
					</TabsTrigger>
					<TabsTrigger
						value="berths"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Berths
					</TabsTrigger>
					<TabsTrigger
						value="holidays"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Holidays
					</TabsTrigger>
					<TabsTrigger
						value="agents"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Agents
					</TabsTrigger>
				</TabsList>
				<TabsContent value="port">
					<PortOverview />
				</TabsContent>
				<TabsContent value="terminals">
					<div className="p-4">Terminals</div>
				</TabsContent>
				<TabsContent value="berths">
					<div className="p-4">Berths</div>
				</TabsContent>
				<TabsContent value="holidays">
					<div className="p-4">Holidays</div>
				</TabsContent>
				<TabsContent value="agents">
					<div className="p-4">Agents</div>
				</TabsContent>
			</Tabs>
		</>
	);
}
