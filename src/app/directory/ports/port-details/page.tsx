import { Link } from 'react-router';
import { Port } from './components/port';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';

export default function PortPage() {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<Link to="../">
									<BreadcrumbLink>Ports</BreadcrumbLink>
								</Link>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>Rotterdam, NL</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex gap-2 px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4">
				<div className="grid w-full max-w-4xl gap-4 px-4">
					<Port />
				</div>
			</div>
		</>
	);
}
