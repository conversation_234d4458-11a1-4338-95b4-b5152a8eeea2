import { Link, Mail, MapPin, Phone, Text, WalletCards } from 'lucide-react';

import { agent } from '../data/schema';

interface AgentProps {
	agent: agent;
}

export default function AgentGeneral({ agent }: AgentProps) {
	return (
		<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-4 px-2 py-4 text-sm">
			<div className="text-muted-foreground flex items-center gap-2">
				<MapPin className="size-4" />
				Address
			</div>
			<div>{agent.general.address}</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Phone className="size-4" />
				Phone
			</div>
			<div>
				<a href={`tel:${agent.general.phone}`} className="text-link hover:underline">
					{agent.general.phone}
				</a>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Mail className="size-4" />
				E-mail
			</div>
			<div>
				<a href={`mailto:${agent.general.email}`} className="text-link hover:underline">
					{agent.general.email}
				</a>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Link className="size-4" />
				Website
			</div>
			<div>
				<a href={agent.general.website} target="_blank" className="text-link hover:underline" rel="noreferrer">
					{agent.general.website}
				</a>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<WalletCards className="size-4" />
				VAT
			</div>
			<div>{agent.general.vat}</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Text className="size-4" />
				Trade register
			</div>
			<div>{agent.general.tradeRegister}</div>
		</div>
	);
}
