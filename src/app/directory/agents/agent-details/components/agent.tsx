import { useEffect, useState } from 'react';
import { agent, agentSchema } from '../data/schema';
import AgentOverview from './agent-overview';
import AgentCompliance from './agent-compliance';
import AgentScores from './agent-scores';
import { AgentHeader } from './agent-header';
import { AgentSkeleton } from './agent-skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function Agent() {
	const [agent, setAgent] = useState<agent | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			try {
				const { default: response } = await import('../../../../../api/agent/agent.json'); // Replace with your API route
				const data = response as agent;

				// Validate with Zod
				const validatedData = agentSchema.parse(data);

				setAgent(validatedData);
			} catch (error) {
				console.error('Error fetching agent:', error);
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading || !agent) {
		return <AgentSkeleton />;
	}

	return (
		<>
			<AgentHeader agent={agent} />
			<Tabs defaultValue="overview" className="w-full">
				<TabsList className="h-auto w-full justify-start gap-4 rounded-none border-b bg-transparent p-0">
					<TabsTrigger
						value="overview"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Overview
					</TabsTrigger>
					<TabsTrigger
						value="compliance"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Compliance
					</TabsTrigger>
					<TabsTrigger
						value="scores"
						className="data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none"
					>
						Scores
					</TabsTrigger>
				</TabsList>
				<TabsContent value="overview">
					<AgentOverview agent={agent} />
				</TabsContent>
				<TabsContent value="compliance">
					<AgentCompliance />
				</TabsContent>
				<TabsContent value="scores">
					<AgentScores />
				</TabsContent>
			</Tabs>
		</>
	);
}
