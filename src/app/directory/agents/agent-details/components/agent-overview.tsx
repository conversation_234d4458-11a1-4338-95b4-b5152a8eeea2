import { agent } from '../data/schema';
import AgentGeneral from './agent-general';
import AgentBankDetails from './agent-bank-details';
import AgentPortsServed from './agent-ports-served';
import AgentAbout from './agent-about';
import { Separator } from '@/components/ui/separator';

interface AgentProps {
	agent: agent;
}

export default function AgentOverview({ agent }: AgentProps) {
	return (
		<div className="grid gap-4">
			<AgentGeneral agent={agent} />
			<Separator />
			<AgentBankDetails agent={agent} />
			<Separator />
			<AgentPortsServed agent={agent} />
			<Separator />
			<AgentAbout agent={agent} />
		</div>
	);
}
