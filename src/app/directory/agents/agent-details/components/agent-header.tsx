import { ShieldCheck } from 'lucide-react';
import { Separator } from '@radix-ui/react-separator';
import { agent } from '../data/schema';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Badge } from '@/components/ui/badge';

interface AgentProps {
	agent: agent;
}

export function AgentHeader({ agent }: AgentProps) {
	return (
		<div className="items-center gap-4 md:flex md:flex-row-reverse">
			<div className="w-[200px]">
				<AspectRatio ratio={16 / 4}>
					<img src={agent.general.logoUrl} alt="Logo" />
				</AspectRatio>
			</div>
			<Separator orientation="horizontal" className="h-auto py-4" />
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">{agent.general.company}</h2>
				<Badge variant="outline" className="rounded-full">
					{agent.general.verified ? (
						<>
							<ShieldCheck className="size-4 text-emerald-500" />
							<span>Verified</span>
						</>
					) : (
						<>
							<ShieldCheck className="text-muted-foreground size-4" />
							<span>Not verified</span>
						</>
					)}
				</Badge>
			</div>
		</div>
	);
}
