import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';

export function AgentSkeleton() {
	return (
		<div className="grid w-full max-w-4xl gap-4 px-4">
			<div className="h-12 items-center justify-between gap-4 md:flex md:flex-row-reverse">
				<Skeleton className="h-3 w-[200px]" />
				<Skeleton className="h-3 w-[35%]" />
			</div>
			<div className="flex h-12 items-center gap-4 border-b">
				<Skeleton className="h-3 w-[10%]" />
				<Skeleton className="h-3 w-[10%]" />
				<Skeleton className="h-3 w-[10%]" />
			</div>
			<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-8 py-4 text-sm">
				<div>
					<Skeleton className="h-3 w-[120px]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[50%]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[120px]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[50%]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[120px]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[50%]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[120px]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[50%]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[120px]" />
				</div>
				<div>
					<Skeleton className="h-3 w-[50%]" />
				</div>
			</div>
			<Separator />
			<div className="grid gap-4 py-4">
				<div className="flex h-8 items-center">
					<Skeleton className="h-3 w-[20%]" />
				</div>
				<div className="grid gap-6 rounded-md border px-4 py-6">
					<Skeleton className="h-3 w-[25%]" />
					<div className="grid grid-cols-2 gap-6">
						<div>
							<Skeleton className="h-3 w-[75%]" />
						</div>
						<div>
							<Skeleton className="h-3 w-[75%]" />
						</div>
						<div>
							<Skeleton className="h-3 w-[75%]" />
						</div>
						<div>
							<Skeleton className="h-3 w-[75%]" />
						</div>
					</div>
				</div>
			</div>
			<Separator />
			<div className="grid gap-4 py-4">
				<div className="flex h-8 items-center">
					<Skeleton className="h-3 w-[20%]" />
				</div>
				<div className="flex items-center gap-8">
					<Skeleton className="h-3 w-[25%]" />
					<Skeleton className="h-3 w-[25%]" />
					<Skeleton className="h-3 w-[25%]" />
					<Skeleton className="h-3 w-[25%]" />
					<Skeleton className="h-3 w-[25%]" />
					<Skeleton className="h-3 w-[25%]" />
					<Skeleton className="h-3 w-[25%]" />
				</div>
			</div>
			<Separator />
			<div className="grid gap-4 py-4">
				<div className="flex h-8 items-center">
					<Skeleton className="h-3 w-[20%]" />
				</div>
				<div className="h-6">
					<Skeleton className="h-3 w-[75%]" />
				</div>
				<div className="h-6">
					<Skeleton className="h-3 w-[100%]" />
				</div>
			</div>
		</div>
	);
}
