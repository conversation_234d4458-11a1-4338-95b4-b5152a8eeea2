import { CircleFlag } from 'react-circle-flags';
import { agent } from '../data/schema';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface AgentProps {
	agent: agent;
}

export default function AgentPortsServed({ agent }: AgentProps) {
	return (
		<div className="grid gap-4 py-4">
			<h3 className="flex items-center gap-2 px-2 text-base font-semibold">
				Ports served <span className="text-muted-foreground font-normal">(24)</span>
			</h3>
			<div className="flex flex-wrap items-center gap-2">
				{agent.ports.map(port => (
					<Badge key={port.name} variant="outline" className="rounded-full">
						<CircleFlag countryCode={port.country.code} className="h-4" />
						{port.name}
					</Badge>
				))}
				<Button variant="ghost" size="xs">
					View all
				</Button>
			</div>
		</div>
	);
}
