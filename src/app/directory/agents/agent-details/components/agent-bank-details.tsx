import { ShieldCheck } from 'lucide-react';
import { agent } from '../data/schema';

interface AgentProps {
	agent: agent;
}

export default function AgentBankDetails({ agent }: AgentProps) {
	return (
		<div className="grid gap-4 py-4">
			<h3 className="px-2 text-base font-semibold">Bank details</h3>
			{agent.banks.map(bank => (
				<div key={bank.name} className="grid gap-4 rounded-md border p-4">
					<div className="flex items-center gap-2 font-medium">
						{bank.name}
						{bank.verified ? (
							<ShieldCheck className="size-4 text-emerald-500" />
						) : (
							<ShieldCheck className="text-muted-foreground size-4" />
						)}
					</div>
					<div className="grid gap-2 text-sm md:grid-cols-[50px_minmax(50px,_1fr)_100px_minmax(100px,_1fr)]">
						<div className="text-muted-foreground">IBAN</div>
						<div>{bank.iban}</div>
						<div className="text-muted-foreground">Account No</div>
						<div>{bank.account}</div>
						<div className="text-muted-foreground">Swift</div>
						<div>{bank.swift}</div>
						<div className="text-muted-foreground">Beneficiary</div>
						<div>{bank.beneficiary}</div>
					</div>
					<div className="text-muted-foreground text-xs">Last updated on {bank.updated}</div>
				</div>
			))}
		</div>
	);
}
