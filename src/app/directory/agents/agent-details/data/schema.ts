import { z } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.

const generalSchema = z.object({
	id: z.string(),
	verified: z.boolean(),
	company: z.string(),
	logoUrl: z.string(),
	address: z.string(),
	country: z.object({
		name: z.string(),
		code: z.string()
	}),
	phone: z.string(),
	email: z.string(),
	website: z.string(),
	vat: z.string(),
	tradeRegister: z.string()
});

const countrySchema = z.object({
	name: z.string(),
	code: z.string()
});

const portSchema = z.object({
	name: z.string(),
	country: countrySchema
});

const bankSchema = z.object({
	name: z.string(),
	verified: z.boolean(),
	iban: z.string(),
	account: z.string(),
	swift: z.string(),
	beneficiary: z.string(),
	updated: z.string()
});

export const agentSchema = z.object({
	uid: z.string(),
	general: generalSchema,
	banks: z.array(bankSchema),
	ports: z.array(portSchema),
	about: z.string()
});

export type agent = z.infer<typeof agentSchema>;
