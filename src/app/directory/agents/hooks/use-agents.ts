import { useQuery } from '@tanstack/react-query';
import { useGraphqlApiProxy } from '@abraxa/graphql';
import { gql, AgentSelectItem, GetAgentsQuery, GetAgentsQueryVariables } from '@/graphql';

export interface Country {
	name: string;
	code: string;
}

export interface Agent {
	id: string;
	verified: boolean;
	company: string;
	country: Country;
	phone: string;
	email: string;
}

export const GET_ALL_AGENTS = gql(`
	query getAgents {
		agent {
			id
			verified
			company
			country
			phone
			email
		}
	}
`);

const decodeAgent = (agent: AgentSelectItem): Agent => {
	const country = JSON.parse(agent.country) as { name: string; code: string };
	country.code = country.code.toLowerCase();

	return {
		id: agent.id,
		verified: agent.verified,
		company: agent.company,
		country,
		phone: agent.phone,
		email: agent.email
	};
};

export const useAgents = () => {
	const proxy = useGraphqlApiProxy();
	const { data, isLoading, ...props } = useQuery({
		queryKey: ['agents'],
		queryFn: () => proxy<GetAgentsQuery, GetAgentsQueryVariables>(GET_ALL_AGENTS)
	});

	const agents = data?.agent?.map(decodeAgent) || [];

	return {
		agents,
		loading: isLoading,
		...props
	};
};
