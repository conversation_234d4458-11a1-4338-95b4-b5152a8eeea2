import { ColumnDef } from '@tanstack/react-table';

import { ShieldCheck } from 'lucide-react';
import { CircleFlag } from 'react-circle-flags';
import { verified, phones, emails } from '../data/data';
import { Agent } from '../hooks/use-agents';

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';

import { cn } from '@/lib/utils';

export const columns: ColumnDef<Agent>[] = [
	{
		accessorKey: 'company',
		meta: {
			label: 'Company'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Company" />,
		cell: ({ row }) => (
			<div className="flex items-center gap-2">
				<ShieldCheck className={cn('h-4 w-4', verified ? 'text-emerald-500' : 'text-muted-foreground')} />
				<div className="font-medium">{row.getValue('company')}</div>
			</div>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'country.name',
		meta: {
			label: 'Country'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Country" />,
		cell: ({ row }) => {
			const country = row.original.country;

			return (
				<div className="flex items-center gap-2">
					<CircleFlag countryCode={country.code} className="h-4" />
					<span>{country.name}</span>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'phone',
		meta: {
			label: 'Phone'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Phone" />,
		cell: ({ row }) => {
			const icon = phones.find(icon => icon);

			return (
				<div className="text-muted-foreground flex items-center gap-2">
					{icon && <icon.icon className="h-4 w-4" />}
					<span>{row.getValue('phone')}</span>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'email',
		meta: {
			label: 'Email'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
		cell: ({ row }) => {
			const icon = emails.find(icon => icon);

			return (
				<div className="text-muted-foreground flex items-center gap-2">
					{icon && <icon.icon className="h-4 w-4" />}
					<span>{row.getValue('email')}</span>
				</div>
			);
		},
		enableSorting: true,
		enableHiding: true,
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	}
];
