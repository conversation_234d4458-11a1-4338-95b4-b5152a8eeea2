import { useEffect, useState } from 'react';
import { disbursement } from '../../data/schema-old';
import { DataTable } from '../../components/data-table';
import { columns } from './data-table-columns';

export default function DisbursementsAll() {
	const [disbursements, setDisbursements] = useState<disbursement[]>([]);
	const [_loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchData = async (): Promise<void> => {
			setLoading(true);
			try {
				const { default: response } = await import('../../../../../api/disbursements-all.json');
				const data = response as disbursement[];
				setDisbursements(data);
			} catch (error) {
				console.error('Error fetching disbursements:', error);
			}
			setLoading(false);
		};

		void fetchData();
	}, []);

	return <DataTable data={disbursements} columns={columns} />;
}
