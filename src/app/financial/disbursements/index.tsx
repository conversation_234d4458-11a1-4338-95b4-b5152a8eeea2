import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const DisbursementsAllPage = lazy(() => import('./all/page'));
const DisbursementsPendingPage = lazy(() => import('./awaiting/page'));
const DisbursementsApprovedPage = lazy(() => import('./approved/page'));
const DisbursementsClosedPage = lazy(() => import('./closed/page'));

export default function Disbursements() {
	return (
		<Routes>
			<Route index element={<DisbursementsAllPage />} />
			<Route path="awaiting-approval" element={<DisbursementsPendingPage />} />
			<Route path="approved" element={<DisbursementsApprovedPage />} />
			<Route path="closed" element={<DisbursementsClosedPage />} />
		</Routes>
	);
}
