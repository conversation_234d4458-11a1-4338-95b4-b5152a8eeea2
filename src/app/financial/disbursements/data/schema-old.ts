import { z } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.

const labelSchema = z.object({
	label: z.string()
});

export const disbursementSchema = z.object({
	groupId: z.string(),
	portCallId: z.string(),
	daId: z.string(),
	vesselImo: z.string(),
	vesselName: z.string(),
	portName: z.string(),
	portCountryCode: z.string(),
	etdAtd: z.string(),
	etaAta: z.string(),
	eta: z.string(),
	etb: z.string(),
	ata: z.string(),
	atd: z.string(),
	agentName: z.string(),
	appointingParty: z.string(),
	nominatingParty: z.string(),
	subAgent: z.string(),
	da: z.string(),
	currency: z.string(),
	pdaAmount: z.number(),
	ddaAmount: z.number(),
	fdaAmount: z.number(),
	paymentsAmount: z.number(),
	refundAmount: z.number(),
	balanceAmount: z.number(),
	daAmount: z.number(),
	issues: z.number(),
	pendingWith: z.string(),
	approvedBy: z.string(),
	approvedOn: z.string(),
	labels: z.array(labelSchema),
	status: z.string(),
	cta: z.string()
});

export type disbursement = z.infer<typeof disbursementSchema>;
