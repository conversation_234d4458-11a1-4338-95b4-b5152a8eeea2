import { z } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const disbursementSchema = z.object({
	groupId: z.string(),
	vesselImo: z.string(),
	vesselName: z.string(),
	da: z.string(),
	portName: z.string(),
	portCountryCode: z.string(),
	agentName: z.string(),
	amount: z.string(),
	status: z.string(),
	date: z.string(),
	cta: z.string()
});

export type disbursement = z.infer<typeof disbursementSchema>;
