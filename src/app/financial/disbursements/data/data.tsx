import { Check<PERSON>heck, CircleStop, Clock, PenLine, SendHorizonal } from 'lucide-react';

export const das = [
	{
		value: 'pda',
		label: 'PDA',
		color: 'border-amber-500'
	},
	{
		value: 'dda',
		label: 'DDA',
		color: 'border-indigo-500'
	},
	{
		value: 'fda',
		label: 'FDA',
		color: 'border-emerald-500'
	},
	{
		value: 'sda',
		label: 'SDA',
		color: 'border-blue-500'
	}
];

export const statuses = [
	{
		value: 'draft',
		label: 'Draft',
		color: 'text-gray-500',
		icon: PenLine
	},
	{
		value: 'pending',
		label: 'Pending approval',
		color: 'text-amber-500',
		icon: Clock
	},
	{
		value: 'submitted',
		label: 'Submitted',
		color: 'text-blue-500',
		icon: SendHorizonal
	},
	{
		value: 'pending approval',
		label: 'Pending approval',
		color: 'text-blue-500',
		icon: SendHorizonal
	},
	{
		value: 'approved',
		label: 'Approved',
		color: 'text-emerald-500',
		icon: CheckCheck
	},
	{
		value: 'completed',
		label: 'Completed',
		color: 'text-emerald-500',
		icon: CheckCheck
	},
	{
		value: 'settled',
		label: 'Settled',
		color: 'text-emerald-500',
		icon: CheckCheck
	},
	{
		value: 'rejected',
		label: 'Rejected',
		color: 'text-red-500',
		icon: CircleStop
	}
];

export const stages = [
	{
		value: 'draft',
		label: 'Draft',
		color: 'text-muted-foreground'
	},
	{
		value: 'pending',
		label: 'Pending',
		color: 'text-amber-500'
	},
	{
		value: 'approved',
		label: 'Approved',
		color: 'text-emerald-500'
	},
	{
		value: 'submitted',
		label: 'Submitted',
		color: 'text-sky-500'
	},
	{
		value: 'completed',
		label: 'Completed',
		color: 'text-primary'
	}
];

export const ports = [
	{
		value: 'Rotterdam, NL',
		label: 'Rotterdam, NL'
	},
	{
		value: 'Hamburg, DE',
		label: 'Hamburg, DE'
	},
	{
		value: 'Antwerp, BE',
		label: 'Antwerp, BE'
	},
	{
		value: 'Amsterdam, NL',
		label: 'Amsterdam, NL'
	}
];
