import { useNavigate } from 'react-router';

import DisbursementsClosed from './components/disbursements-closed';
import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function DisbursementsClosedPage() {
	const navigate = useNavigate();
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbPage>Disbursements</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
					<Separator orientation="vertical" className="mx-2 h-4" />
					<Button
						size="xs"
						variant="outline"
						className="text-muted-foreground"
						onClick={() => void navigate('/financial/disbursements')}
					>
						All
						<Badge variant="secondary" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs text-inherit">
							9
						</Badge>
					</Button>
					<Button
						size="xs"
						variant="outline"
						className="text-muted-foreground"
						onClick={() => void navigate('/financial/disbursements/awaiting-approval')}
					>
						Awaiting approval
						<Badge
							variant="default"
							className="h-4 min-w-4 rounded-sm bg-amber-600 px-1 py-0 text-xs hover:bg-amber-600/80"
						>
							4
						</Badge>
					</Button>
					<Button
						size="xs"
						variant="outline"
						className="text-muted-foreground"
						onClick={() => void navigate('/financial/disbursements/approved')}
					>
						Approved
						<Badge variant="secondary" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs text-inherit">
							3
						</Badge>
					</Button>
					<Button
						size="xs"
						variant="outline"
						className="bg-primary/20 border-primary/50"
						onClick={() => void navigate('/financial/disbursements/closed')}
					>
						Closed
					</Button>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 p-4 pt-0">
				<div className="flex w-full flex-1 flex-col">
					<DisbursementsClosed />
				</div>
			</div>
		</>
	);
}
