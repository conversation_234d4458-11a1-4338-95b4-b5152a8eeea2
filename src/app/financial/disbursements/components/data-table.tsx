/* eslint-disable */
import * as React from 'react';
import {
	ColumnDef,
	ColumnFiltersState,
	SortingState,
	VisibilityState,
	flexRender,
	getCoreRowModel,
	getFacetedRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable
} from '@tanstack/react-table';

import { useNavigate } from 'react-router';
import { DataTableToolbar } from './data-table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// Define a custom type for column meta that includes className
declare module '@tanstack/react-table' {
	interface ColumnMeta<TData, TValue> {
		className?: string;
		label?: string;
		defaultHidden?: boolean;
	}
}

interface TableData {
	groupId: string;
}

interface DataTableProps<TData extends TableData, TValue> {
	columns: ColumnDef<TData, TValue>[];
	data: TData[];
}

export function DataTable<TData extends TableData, TValue>({ columns, data }: DataTableProps<TData, TValue>) {
	const navigate = useNavigate();

	// Initialize column visibility based on defaultHidden metadata
	const initialColumnVisibility = React.useMemo(() => {
		const visibility: VisibilityState = {};
		columns.forEach(column => {
			// Check if meta exists and has defaultHidden property
			const meta = column.meta as { defaultHidden?: boolean; label?: string } | undefined;
			if (meta?.defaultHidden) {
				// Get column ID (either accessorKey or id)
				const columnId = (column as { accessorKey?: string }).accessorKey || column.id;
				if (columnId) {
					visibility[columnId] = false;
				}
			}
		});
		return visibility;
	}, [columns]);

	const [rowSelection, setRowSelection] = React.useState({});
	const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>(initialColumnVisibility);
	const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
	const [sorting, setSorting] = React.useState<SortingState>([]);
	const [globalFilter, setGlobalFilter] = React.useState<string>('');
	const table = useReactTable({
		data,
		columns,
		state: {
			sorting,
			columnVisibility,
			rowSelection,
			columnFilters,
			globalFilter
		},
		enableRowSelection: false,
		onRowSelectionChange: setRowSelection,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		onGlobalFilterChange: setGlobalFilter,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFacetedRowModel: getFacetedRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues()
	});

	return (
		<>
			<DataTableToolbar table={table} />
			<Table>
				<TableHeader>
					{table.getHeaderGroups().map(headerGroup => (
						<TableRow key={headerGroup.id}>
							{headerGroup.headers.map(header => (
								<TableHead
									key={header.id}
									colSpan={header.colSpan}
									className={header.column.columnDef.meta?.className}
								>
									{header.isPlaceholder
										? null
										: flexRender(header.column.columnDef.header, header.getContext())}
								</TableHead>
							))}
						</TableRow>
					))}
				</TableHeader>
				<TableBody className="border-b">
					{table.getRowModel().rows?.length ? (
						table.getRowModel().rows.map(row => (
							<TableRow
								key={row.id}
								className="h-12 flex-nowrap border-b-0 text-nowrap"
								data-state={row.getIsSelected() && 'selected'}
								onClick={() =>
									navigate('/operations/port-calls/' + row.original.groupId + '/disbursement')
								}
							>
								{row.getVisibleCells().map(cell => (
									<TableCell key={cell.id} className={cell.column.columnDef.meta?.className}>
										{flexRender(cell.column.columnDef.cell, cell.getContext())}
									</TableCell>
								))}
							</TableRow>
						))
					) : (
						<TableRow>
							<TableCell colSpan={columns.length} className="h-24 text-center">
								No results.
							</TableCell>
						</TableRow>
					)}
				</TableBody>
			</Table>
		</>
	);
}
