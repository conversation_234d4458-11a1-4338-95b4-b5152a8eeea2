import { z } from 'zod';

import { useEffect, useState } from 'react';
import { columns } from './components/data-table-columns';
import { taskSchema } from './data/schema';
import { DataTable } from '@/components/data-table/data-table';

import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';

type Task = z.infer<typeof taskSchema>;

export default function TaskPage() {
	const [tasks, setTasks] = useState<Task[]>([]);

	useEffect(() => {
		async function fetchData() {
			try {
				const { default: response } = await import('../../api/tasks.json'); // Replace with your API route
				const tasks = response as Task[];

				// Validate with Zod
				const validatedData = z.array(taskSchema).parse(tasks);

				setTasks(validatedData);
			} catch (error) {
				console.error('Error fetching tasks:', error);
			}
		}

		void fetchData();
	}, []);

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbPage>Tasks</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 p-4 pt-0">
				<div className="flex w-full flex-1 flex-col">
					{' '}
					<DataTable data={tasks} columns={columns} />
				</div>
			</div>
		</>
	);
}
