import { ColumnDef } from '@tanstack/react-table';
import { labels, priorities, statuses } from '../data/data';
import { Task } from '../data/schema';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';

import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { DataTableRowActions } from '@/components/data-table/data-table-row-actions';

export const columns: ColumnDef<Task>[] = [
	{
		id: 'select',
		header: ({ table }) => (
			<Checkbox
				checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
				aria-label="Select all"
				className="translate-y-[2px]"
				onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				aria-label="Select row"
				className="translate-y-[2px]"
				onCheckedChange={value => row.toggleSelected(!!value)}
			/>
		),
		enableSorting: false,
		enableHiding: false
	},
	{
		accessorKey: 'id',
		meta: {
			label: 'Task'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Task" />,
		cell: ({ row }) => <div className="w-[80px]">{row.getValue('id')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'title',
		meta: {
			label: 'Title'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Title" />,
		cell: ({ row }) => {
			const label = labels.find(label => label.value === row.original.label);

			return (
				<div className="flex space-x-2">
					{label && <Badge variant="outline">{label.label}</Badge>}
					<span className="max-w-[500px] truncate font-medium">{row.getValue('title')}</span>
				</div>
			);
		}
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: ({ row }) => {
			const status = statuses.find(status => status.value === row.getValue('status'));

			if (!status) {
				return null;
			}

			return (
				<div className="flex w-[100px] items-center">
					{status.icon && <status.icon className="text-muted-foreground mr-2 h-4 w-4" />}
					<span>{status.label}</span>
				</div>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		accessorKey: 'priority',
		meta: {
			label: 'Priority'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Priority" />,
		cell: ({ row }) => {
			const priority = priorities.find(priority => priority.value === row.getValue('priority'));

			if (!priority) {
				return null;
			}

			return (
				<div className="flex items-center">
					{priority.icon && <priority.icon className="text-muted-foreground mr-2 h-4 w-4" />}
					<span>{priority.label}</span>
				</div>
			);
		},
		filterFn: (row, id, value: string[]) => value.includes(row.getValue(id))
	},
	{
		id: 'actions',
		cell: ({ row }) => <DataTableRowActions row={row} />
	}
];
