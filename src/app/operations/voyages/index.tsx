import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const VoyagesPage = lazy(() => import('./page'));
const NewVoyagePage = lazy(() => import('./new/page'));
const NewEstimatePage = lazy(() => import('./new-estimate/page'));
const NewEstimateLeanPage = lazy(() => import('./new-estimate/lean/page'));
const NewEstimateViewPage = lazy(() => import('./new-estimate/[id]/page'));
const NewEstimateLeanViewPage = lazy(() => import('./new-estimate/lean/[id]/page'));
const VoyageDetailsPage = lazy(() => import('./voyage-details/page'));

export default function Voyages() {
	return (
		<Routes>
			<Route index element={<VoyagesPage />} />
			<Route path="/new" element={<NewVoyagePage />} />
			<Route path="/new-estimate" element={<NewEstimatePage />} />
			<Route path="/new-estimate/:id" element={<NewEstimateViewPage />} />
			<Route path="/new-estimate/lean" element={<NewEstimateLeanPage />} />
			<Route path="/new-estimate/lean/:id" element={<NewEstimateLeanViewPage />} />
			<Route path=":id" element={<VoyageDetailsPage />} />
		</Routes>
	);
}
