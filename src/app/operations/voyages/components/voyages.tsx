import { useEffect, useState } from 'react';
import { Voyage } from '../data/schema';
import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';
export default function Voyages() {
	const [voyages, setVoyages] = useState<Voyage[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			try {
				const { default: response } = await import('../../../../api/voyages.json'); // Replace with your API route

				const data = response as Voyage[];
				setVoyages(data);
			} catch (error) {
				console.error('Error fetching Voyages:', error);
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={voyages} setData={setVoyages} columns={columns} />;
}
