import { Ship, Anchor, Calendar, Box } from 'lucide-react';
import { useState } from 'react';
import { VoyageResponse } from '../data/schema';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { DialogClose, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export function DialogCreateVoyage({
	handlePlan,
	setCpTermsFile,
	data
}: {
	handlePlan: () => void;
	setCpTermsFile: (file: { url: string; name: string; uuid: string; gcpName: string } | null) => void;
	data: VoyageResponse;
}) {
	const [, setSelectedItem] = useState('');

	return (
		<>
			<DialogHeader>
				<DialogTitle>Plan your next voyage</DialogTitle>
				<DialogDescription>Create a new voyage plan and upload relevant documents</DialogDescription>
			</DialogHeader>
			<div className="grid gap-4">
				<div className="bg-panel grid grid-cols-[130px_minmax(0px,1fr)] items-center gap-x-4 gap-y-2 rounded-xl border p-4 text-sm">
					<div className="text-muted-foreground text-sm">Vessel</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder={data.vessel}
							icon={Ship}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
					<div className="text-muted-foreground text-sm">Load port</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder={data.load_port}
							icon={Anchor}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
					<div className="text-muted-foreground text-sm">ETA</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder="28 Feb, 12:00"
							icon={Calendar}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
					<div className="text-muted-foreground text-sm">Discharge port</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder={data.discharge_port}
							icon={Anchor}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
					<div className="text-muted-foreground text-sm">ETA</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder="17 Mar, 08:00"
							icon={Calendar}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
					<div className="text-muted-foreground text-sm">Cargo</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder={data.cargo}
							icon={Box}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
					<div className="text-muted-foreground text-sm">Cargo Quantity</div>
					<div className="flex">
						<ComboboxButtonInline
							data={[]}
							placeholder={data.cargo_quantity}
							icon={Box}
							className="flex-1 font-normal"
							onSelect={value => setSelectedItem(value)}
						/>
					</div>
				</div>
				{/* <div className="bg-panel grid grid-cols-[130px_minmax(0px,1fr)] items-center gap-x-4 gap-y-2 rounded-xl border p-4 text-sm">
					{data.timeCharterer.map(item => (
						<>
							<div key={item.label + item.id} className="text-muted-foreground text-sm">
								{item.label}
							</div>
							<div key={item.value + item.id} className="flex">
								<ComboboxButtonInline
									data={[...data.timeCharterer]}
									placeholder={item.label}
									icon={Text}
									className="flex-1 font-normal"
									onSelect={value => setSelectedItem(value)}
								/>
							</div>
						</>
					))}
				</div> */}
			</div>
			<DialogFooter>
				<DialogClose asChild>
					<Button size="sm" variant="secondary" onClick={() => setCpTermsFile(null)}>
						Cancel
					</Button>
				</DialogClose>
				<DialogClose asChild>
					<Button size="sm" onClick={handlePlan}>
						Plan
					</Button>
				</DialogClose>
			</DialogFooter>
		</>
	);
}
