import { DialogTitle, DialogDescription } from '@radix-ui/react-dialog';
import { UploadField } from './field-upload';
import { DialogHeader } from '@/components/ui/dialog';

export function DialogUploadFiles({
	setCpTermsFile,
	handleUploadStateChange
}: {
	cpTermsFile: {
		url: string;
		name: string;
		uuid: string;
		gcpName: string;
	} | null;
	setCpTermsFile: (file: { url: string; name: string; uuid: string; gcpName: string } | null) => void;
	handleUploadStateChange: (uploading: boolean) => void;
	isPlanDisabled: boolean;
	isUploading: boolean;
}) {
	return (
		<>
			<DialogHeader>
				<DialogTitle>Plan your next voyage</DialogTitle>
				<DialogDescription>Create a new voyage plan and upload relevant documents</DialogDescription>
			</DialogHeader>
			<div className="grid gap-4 py-4">
				<p className="px-2 text-sm">Upload documents</p>
				<div className="flex flex-col items-center gap-2 rounded-lg border-2 border-dashed p-8">
					<UploadField
						title="C/P terms"
						onUpload={(_, fileData) =>
							setCpTermsFile({
								url: fileData.url,
								name: fileData.originalName,
								uuid: fileData.uuid,
								gcpName: fileData.gcpName
							})
						}
						onUploadStateChange={handleUploadStateChange}
					/>
				</div>
			</div>
		</>
	);
}
