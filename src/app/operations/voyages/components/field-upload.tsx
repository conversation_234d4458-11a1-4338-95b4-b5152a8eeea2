import { Upload } from 'lucide-react';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useFileUpload } from '@/hooks/signedUrl';

export function UploadField({
	title,
	onUpload,
	onUploadStateChange
}: {
	title: string;
	onUpload: (file: File, fileData: { url: string; originalName: string; uuid: string; gcpName: string }) => void;
	onUploadStateChange: (isUploading: boolean) => void;
}) {
	const fileInputRef = useRef<HTMLInputElement | null>(null);
	const [file, setFile] = useState<File | null>(null);
	const [uploading, setUploading] = useState(false);
	const { uploadFile } = useFileUpload();

	const handleButtonClick = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.files && event.target.files.length > 0) {
			const selectedFile = event.target.files[0];

			setFile(selectedFile);
			setUploading(true);
			onUploadStateChange(true);

			uploadFile(selectedFile)
				.then(fileData => {
					onUpload(selectedFile, fileData);
				})
				.catch(error => {
					setFile(null);
					console.error('Upload failed:', error);
				})
				.finally(() => {
					setUploading(false);
					onUploadStateChange(false);
				});
		}
	};

	return (
		<>
			<hr className="w-full" />
			<div className="flex h-full w-full items-center justify-between">
				<p className="text-sm">{file ? file.name : title}</p>
				<Input
					ref={fileInputRef}
					type="file"
					accept=".pdf,.xlsx,.docx"
					className="hidden"
					onChange={handleFileChange}
				/>
				{uploading ? (
					<div className="text-sm">Uploading...</div>
				) : file ? (
					<div className="text-sm">Scanning...</div>
				) : (
					<Button size="sm" variant="secondary" onClick={handleButtonClick}>
						<Upload /> Upload
					</Button>
				)}
			</div>
			<hr className="w-full" />
		</>
	);
}
