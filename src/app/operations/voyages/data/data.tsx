import { Box, Droplets, CircleDashed, Clock } from 'lucide-react';

export const portFunctions = [
	{
		value: 'cargo-ops',
		label: 'Cargo ops',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'bunkering',
		label: 'Bunkering',
		color: 'text-cyan-500',
		icon: Droplets
	}
];

export const ports = [
	{
		value: 'Rotterdam, NL',
		label: 'Rotterdam, NL'
	},
	{
		value: 'Hamburg, DE',
		label: 'Hamburg, DE'
	},
	{
		value: 'Antwerp, BE',
		label: 'Antwerp, BE'
	},
	{
		value: 'Southampton, UK',
		label: 'Southampton, UK'
	}
];

export const cargoes = [
	{
		value: 'iron-ore',
		label: 'Iron Ore'
	},
	{
		value: 'coal',
		label: 'Coal'
	},
	{
		value: 'oil',
		label: 'Oil'
	}
];

export const statuses = [
	{
		value: 'commenced',
		label: 'Commenced',
		color: 'text-emerald-500',
		icon: CircleDashed
	},
	{
		value: 'scheduled',
		label: 'Scheduled',
		color: 'text-muted-foreground',
		icon: Clock
	}
];
export const operators = [
	{
		value: 'john',
		label: '<PERSON><PERSON>',
		short: 'JD',
		avatar: ''
	},
	{
		value: 'robert',
		label: '<PERSON><PERSON>',
		short: '',
		avatar: '/static/media/avatar-md.webp'
	}
];
