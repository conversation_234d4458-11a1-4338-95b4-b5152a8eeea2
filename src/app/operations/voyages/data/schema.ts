export type Voyage = {
	id: string;
	tenantId: string;
	voyageId: string;
	vessel: string;
	route: [number, number][];
	lastKnownPosition: [number, number];
	previousPort: string;
	nextPort: string;
	currentPort: string;
	/* These are TBD
	vessel: Vessel;
	cargo: Cargo[];
	alerts: AlertInstance[];
	actions: Record<Action, boolean>;
	*/
};

export interface VoyageResponse {
	shipbroker: string;
	charter_party_date: string;
	charter_party_form: string;
	laydays: string | null;
	cancelling: string;
	vessel: string;
	cargo: string;
	cargo_quantity: string;
	load_port: string;
	load_terms: string;
	load_nor_tendering_clause: string;
	load_turn_time: string | null;
	discharge_port: string;
	discharge_terms: string | null;
	discharge_nor_tendering_clause: string;
	discharge_turn_time: string | null;
	demurrage: string;
	despatch_amount: string;
	despatch_currency: string;
	freight_amount: string;
	freight_currency: string;
	agents_at_load: string;
	agents_at_discharge: string;
	notices_to_load: string;
}
export interface OCRResponse {
	data: VoyageResponse | undefined;
}
