import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function VoyageFooter() {
	const navigate = useNavigate();
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const handleCreateVoyage = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			void navigate('/operations/voyages/voy-123a');
		}, 500);
	};

	return (
		<div className="bg-panel flex w-full flex-col items-center border-t px-4 shadow-2xl">
			<div className="flex w-full max-w-3xl items-center justify-end gap-2 py-2">
				<Button variant="default" size="sm" disabled={isLoading} onClick={handleCreateVoyage}>
					{isLoading ? (
						<>
							<Loader2 className="h-4 w-4 animate-spin" />
							<span>Creating...</span>
						</>
					) : (
						'Create voyage'
					)}
				</Button>
			</div>
		</div>
	);
}
