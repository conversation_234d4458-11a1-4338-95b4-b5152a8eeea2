import { useState } from 'react';
import { GripHorizontal, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { DatePicker } from '@/components/ui/date-picker';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const ports = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' }
];

const activities: ComboboxOption[] = [
	{ value: 'loading', label: 'Loading' },
	{ value: 'discharging', label: 'Discharging' },
	{ value: 'bunkering', label: 'Bunkering' },
	{ value: 'non-commercial', label: 'Non-commercial' },
	{ value: 'canal-transit', label: 'Canal transit' },
	{ value: 'repair', label: 'Repair' }
];

interface Instruction {
	id: string;
	title: string;
	content: string;
}

export default function VoyagePortForm() {
	const [, setSelectedPort] = useState<string | null>(null);
	const [, setSelectedActivity] = useState<string | null>(null);
	const [date, setDate] = useState<Date | undefined>();

	const [instructions, setInstructions] = useState<Instruction[]>([{ id: '1', title: '', content: '' }]);

	const addInstruction = () => {
		const newInstruction: Instruction = {
			id: Date.now().toString(),
			title: '',
			content: ''
		};
		setInstructions(prev => [...prev, newInstruction]);
	};

	const removeInstruction = (id: string) => {
		if (instructions.length > 1) {
			setInstructions(prev => prev.filter(instruction => instruction.id !== id));
		}
	};

	return (
		<TooltipProvider>
			<div className="flex w-full flex-col gap-4">
				<h3 className="text-base font-semibold">Ports</h3>
				<Card>
					<CardContent className="pt-6 pr-10 pl-10">
						<div className="grid grid-cols-1 gap-4 pb-2 md:grid-cols-3">
							<Label className="text-muted-foreground text-xs">Port</Label>
							<Label className="text-muted-foreground text-xs">Activity</Label>
							<Label className="text-muted-foreground text-xs">ETA</Label>
						</div>
						{instructions.map((instruction, index) => (
							<div
								key={instruction.id}
								className="relative grid cursor-grab grid-cols-1 gap-4 pb-4 md:grid-cols-3"
							>
								<div className="absolute top-[10px] -left-6">
									<Tooltip>
										<TooltipTrigger asChild>
											<GripHorizontal className="text-muted-foreground size-3.5" />
										</TooltipTrigger>
										<TooltipContent>Reorder</TooltipContent>
									</Tooltip>
								</div>
								<Combobox data={ports} placeholder="Choose port" onSelect={setSelectedPort} />
								<Combobox
									data={activities}
									search={false}
									placeholder="Choose activity"
									onSelect={setSelectedActivity}
								/>
								<DatePicker date={date} setDate={setDate} className="h-9" />
								{index > 0 && (
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="sm"
												className="text-muted-foreground absolute -right-9 h-8 w-8 p-0"
												onClick={() => removeInstruction(instruction.id)}
											>
												<Trash2 className="size-3.5" />
											</Button>
										</TooltipTrigger>
										<TooltipContent>Delete</TooltipContent>
									</Tooltip>
								)}
							</div>
						))}
						<div>
							<Button
								variant="secondary"
								size="sm"
								className="flex items-center gap-1"
								onClick={addInstruction}
							>
								<Plus className="size-4" />
								Add port
							</Button>
						</div>
					</CardContent>
				</Card>
			</div>
		</TooltipProvider>
	);
}
