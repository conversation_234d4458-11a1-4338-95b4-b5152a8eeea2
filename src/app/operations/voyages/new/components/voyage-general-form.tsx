import { useState } from 'react';

import { Ship, Text, Hash, Building2, User, ArrowBigLeftDash } from 'lucide-react';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';

const vessels = [
	{ value: 'meridiaan-express', label: 'mv Meridiaan Express' },
	{ value: 'meridiaan-cinco', label: 'mv Meridiaan Cinco' },
	{ value: 'vertom-meridiaan', label: 'mv Vertom Meridiaan' },
	{ value: 'gulf-meridiaan', label: 'mv Gulf Meridiaan' },
	{ value: 'astra-meridiaan', label: 'mv Astra Meridiaan' }
];

const legalEntities = [
	{ value: 'sf-shipping', label: 'SF Shipping Ltd.' },
	{ value: 'la-shipping', label: 'LA Shipping S.A.' }
];

const operators = [
	{ value: 'john', label: '<PERSON>. <PERSON>' },
	{ value: 'robert', label: '<PERSON><PERSON>' },
	{ value: 'vicky', label: '<PERSON>. Encheva' }
];

const ports = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' }
];

export default function VoyageGeneralForm() {
	const [, setSelectedVessel] = useState<string | null>(null);
	const [, setSelectedLegalEntity] = useState<string | null>(null);
	const [, setSelectedOperator] = useState<string | null>(null);
	const [, setSelectedPort] = useState<string | null>(null);

	return (
		<div className="flex flex-col gap-6 lg:flex-row-reverse">
			<div className="grid w-full flex-1 grid-cols-2 gap-12">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-6 gap-y-2 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<Ship className="size-4" />
						<div>
							Vessel <span className="text-destructive">*</span>
						</div>
					</div>
					<div>
						<Combobox
							data={vessels}
							placeholder="Choose vessel"
							className="h-8 font-normal"
							onSelect={value => setSelectedVessel(value)}
						/>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Hash className="size-4" />
						<div>
							Voyage ID <span className="text-destructive">*</span>
						</div>
					</div>
					<div>
						<Input className="h-8" placeholder="Enter ID" />
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Text className="size-4" />
						<div>Reference</div>
					</div>
					<div>
						<Input className="h-8" placeholder="Enter reference number" />
					</div>
				</div>
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-6 gap-y-2 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<Building2 className="size-4" />
						<div>Legal Entity</div>
					</div>
					<div>
						<Combobox
							data={legalEntities}
							placeholder="Choose entity"
							className="h-8 font-normal"
							search={false}
							onSelect={value => setSelectedLegalEntity(value)}
						/>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<User className="size-4" />
						<div>
							Operator <span className="text-destructive">*</span>
						</div>
					</div>
					<div>
						<Combobox
							data={operators}
							placeholder="Choose operator"
							className="h-8 font-normal"
							search={false}
							onSelect={value => setSelectedOperator(value)}
						/>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<ArrowBigLeftDash className="size-4" />
						<div>
							Previous port <span className="text-destructive">*</span>
						</div>
					</div>
					<div>
						<Combobox
							data={ports}
							placeholder="Choose port"
							className="h-8 font-normal"
							search={false}
							onSelect={value => setSelectedPort(value)}
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
