import { useState } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const cargoes = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'oil', label: 'Oil' },
	{ value: 'gas', label: 'Gas' },
	{ value: 'petroleum', label: 'Petroleum' }
];

const units = [
	{ value: 'mts', label: 'mts' },
	{ value: 'kgs', label: 'kgs' },
	{ value: 'lts', label: 'lts' },
	{ value: 'cbm', label: 'cbm' }
];

interface Instruction {
	id: string;
	title: string;
	content: string;
}

export default function VoyageCargoForm() {
	const [, setSelectedCargo] = useState<string | null>(null);
	const [, setSelectedUnit] = useState<string | null>(null);
	const [date, setDate] = useState<Date | undefined>();

	const [instructions, setInstructions] = useState<Instruction[]>([{ id: '1', title: '', content: '' }]);

	const addInstruction = () => {
		const newInstruction: Instruction = {
			id: Date.now().toString(),
			title: '',
			content: ''
		};
		setInstructions(prev => [...prev, newInstruction]);
	};

	const removeInstruction = (id: string) => {
		if (instructions.length > 1) {
			setInstructions(prev => prev.filter(instruction => instruction.id !== id));
		}
	};

	return (
		<TooltipProvider>
			<div className="flex w-full flex-col gap-4">
				<h3 className="text-base font-semibold">Cargoes</h3>
				{instructions.map((instruction, index) => (
					<Card key={instruction.id}>
						<CardHeader className="flex-row items-center">
							<span className="text-muted-foreground m-0 flex-1">Cargo #{index + 1}</span>
							{index > 0 && (
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="ghost"
											size="sm"
											className="text-muted-foreground h-8 w-8 p-0"
											onClick={() => removeInstruction(instruction.id)}
										>
											<Trash2 className="size-3.5" />
										</Button>
									</TooltipTrigger>
									<TooltipContent>Delete</TooltipContent>
								</Tooltip>
							)}
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Cargo</Label>
									<Combobox data={cargoes} placeholder="Choose cargo" onSelect={setSelectedCargo} />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Quantity</Label>
									<Input className="bg-background" placeholder="0.000.000" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Unit of measure</Label>
									<Combobox
										data={units}
										placeholder="Choose unit"
										className="font-normal"
										search={false}
										onSelect={value => setSelectedUnit(value)}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Laycan from</Label>
									<DatePicker date={date} setDate={setDate} className="h-9" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Laycan to</Label>
									<DatePicker date={date} setDate={setDate} className="h-9" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">C/P date</Label>
									<DatePicker date={date} setDate={setDate} className="h-9" />
								</div>
							</div>
						</CardContent>
					</Card>
				))}
				<div>
					<Button variant="secondary" size="sm" className="flex items-center gap-1" onClick={addInstruction}>
						<Plus className="size-4" />
						Add cargo
					</Button>
				</div>
			</div>
		</TooltipProvider>
	);
}
