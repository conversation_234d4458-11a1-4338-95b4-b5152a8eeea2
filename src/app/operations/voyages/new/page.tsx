import VoyageCargoForm from './components/voyage-cargo-form';
import <PERSON>Footer from './components/voyage-footer';
import VoyageGeneralForm from './components/voyage-general-form';
import VoyagePortForm from './components/voyage-port-form';

import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';

export default function NewVoyagePage() {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/voyages">My Voyages</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>New Voyage</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-6 overflow-auto p-4">
				<div className="w-full max-w-3xl">
					<div className="grid gap-6">
						<h2 className="text-xl font-semibold">Create a new voyage</h2>
						<VoyageGeneralForm />
						<Separator />
						<VoyagePortForm />
						<Separator />
						<VoyageCargoForm />
					</div>
				</div>
			</div>
			<VoyageFooter />
		</>
	);
}
