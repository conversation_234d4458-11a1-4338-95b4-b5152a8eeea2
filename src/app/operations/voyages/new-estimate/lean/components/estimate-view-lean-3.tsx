import { <PERSON><PERSON><PERSON>, Box, Circle, Loader2, Maximize2 } from 'lucide-react';
import EstimateMapLean from './estimate-map-lean';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Separator } from '@/components/ui/separator';
import ChatbotSimple from '@/components/chatbot/chatbot-simple';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface EstimateViewLeanProps3 {
	handleExpandVoyage?: (showViewPanel: boolean) => void;
	handleCreateVoyage?: () => void;
	isLoading?: boolean;
	className?: string;
}

const tabClassName =
	'data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border hover:text-foreground';

export default function EstimateViewLean3({
	handleExpandVoyage,
	handleCreateVoyage,
	isLoading,
	className
}: EstimateViewLeanProps3) {
	return (
		<div className={`flex h-full flex-col ${className}`}>
			<div className="flex-1 overflow-auto p-6">
				<div className="relative flex flex-col gap-6">
					<div className="bg-panel/60 flex flex-col gap-4 rounded-xl p-4 shadow-2xl backdrop-blur">
						<div className="flex w-full items-center gap-2">
							<div className="text-foreground flex-1 px-2 text-base font-semibold">mv Suez Navigator</div>
							<Badge variant="secondary" className="rounded-full text-xs">
								<Box className="text-primary size-3" />
								<span className="text-muted-foreground">35,000mts - Iron Ore</span>
							</Badge>
						</div>
						<div className="relative">
							<AspectRatio ratio={16 / 7} className="overflow-hidden rounded-xl">
								<EstimateMapLean />
							</AspectRatio>
							<TooltipProvider delayDuration={100}>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="ghost"
											size="icon"
											className="text-muted-foreground absolute top-4 right-4 h-7 w-7"
											onClick={handleExpandVoyage && (() => handleExpandVoyage(true))}
										>
											<Maximize2 />
										</Button>
									</TooltipTrigger>
									<TooltipContent>Expand Map</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						</div>
						<div className="flex flex-col gap-2">
							<div className="flex w-full items-center gap-2 font-normal">
								<Button
									variant="outline"
									className="h-auto rounded-full bg-transparent px-2 py-1 text-xs"
								>
									<Circle className="text-muted-foreground size-3" />
									<span>Rotterdam, NL</span>
								</Button>
								<Separator className="flex-1" />
								<ArrowRight className="text-muted-foreground size-3" />
								<Separator className="flex-1" />
								<Button
									variant="outline"
									className="h-auto rounded-full bg-transparent px-2 py-1 text-xs"
								>
									<Circle className="text-muted-foreground size-3" />
									<span>Port Said, EG</span>
								</Button>
							</div>
							<div className="text-muted-foreground flex w-full items-center gap-2 text-xs">
								<div className="flex-1 px-2">28 Feb, 12:00</div>
								<div className="flex-1 px-2 text-right">17 Mar, 08:00</div>
							</div>
						</div>
					</div>
				</div>

				<Tabs defaultValue="overview" className="pt-6">
					<TabsList className="border-accent/50 flex border bg-transparent px-0.5">
						<TabsTrigger value="overview" className={tabClassName}>
							Overview
						</TabsTrigger>
						<TabsTrigger value="itinerary" className={tabClassName}>
							Itinerary
						</TabsTrigger>
						<TabsTrigger value="bunkers" className={tabClassName}>
							Bunkers
						</TabsTrigger>
					</TabsList>
					<TabsContent value="overview">
						<div className="grid gap-6 pt-2">
							<Table className="text-sm">
								<TableBody>
									<TableRow className="border-b-border/50">
										<TableCell className="text-muted-foreground w-1/2">Total Sailing</TableCell>
										<TableCell className="text-right">13d : 3h</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="text-muted-foreground w-1/2">Port Stay</TableCell>
										<TableCell className="text-right">2d : 8h</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="w-1/2">
											<div className="font-bold">Total Voyage Time</div>
										</TableCell>
										<TableCell className="text-right">
											<span className="text-base font-bold">17d : 3h</span>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="text-muted-foreground w-1/2">Gross Freight</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground">$</span>
												<span>324,000</span>
											</div>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="w-1/2">
											<div className="font-bold">TCE</div>
										</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground text-base">$</span>
												<span className="text-base font-bold">26,314</span>
											</div>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="w-1/2">
											<div className="font-bold">Net Daily</div>
										</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground text-base">$</span>
												<span className="text-base font-bold">134</span>
											</div>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="w-1/2">
											<div className="font-bold">Total Expenses</div>
										</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground text-base">$</span>
												<span className="text-base font-bold">476,280</span>
											</div>
										</TableCell>
									</TableRow>
								</TableBody>
							</Table>
							<Button
								variant="default"
								size="xs"
								className="hidden w-full"
								disabled={isLoading}
								onClick={handleCreateVoyage}
							>
								{isLoading ? (
									<>
										<Loader2 className="h-4 w-4 animate-spin" />
										<span>Creating...</span>
									</>
								) : (
									'Create Voyage'
								)}
							</Button>
						</div>
					</TabsContent>
				</Tabs>
			</div>
			<ChatbotSimple />
		</div>
	);
}
