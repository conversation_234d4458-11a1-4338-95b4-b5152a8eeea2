import { AnimatePresence, motion } from 'framer-motion';
import estimateImg from '@/static/media/img-txt-estimate.svg';

interface EstimateViewLeanEmptyProps {
	className?: string;
}

export default function EstimateEmptyLean({ className }: EstimateViewLeanEmptyProps) {
	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, x: 100 }}
				animate={{ opacity: 1, x: 0 }}
				exit={{ opacity: 0, x: 100 }}
				transition={{ duration: 0.24 }}
				className="h-full"
			>
				<div className={`flex h-full flex-col ${className}`}>
					<div className="flex-1" />
					<div className="flex flex-1 items-center justify-center p-6">
						<div className="m-h-8">
							<img src={estimateImg} alt="Estimate" height={30} />
						</div>
					</div>
					<div className="flex-1" />
				</div>
			</motion.div>
		</AnimatePresence>
	);
}
