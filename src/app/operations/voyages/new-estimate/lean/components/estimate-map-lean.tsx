import 'mapbox-gl/dist/mapbox-gl.css';

import { useState, useRef, useEffect } from 'react';
import { useTheme } from 'next-themes';

import MapGL, { MapRef, Source, Layer } from 'react-map-gl/mapbox';
import {
	lightStyle,
	darkStyle,
	routeDataVoyage,
	routeLayer,
	portLayer,
	portPointsVoyage
} from '@/app/mapbox/data/data';

const mapboxAccessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;

interface ViewState {
	longitude: number;
	latitude: number;
	zoom: number;
}

interface EstimateMapLeanProps {
	showViewPanel?: boolean;
}

const defaultViewState = {
	longitude: 32.5,
	latitude: 24.0,
	zoom: 1.25
};

export default function EstimateMapLean({ showViewPanel = false }: EstimateMapLeanProps) {
	const { theme } = useTheme();
	const mapRef = useRef<MapRef>(null);

	const [viewState, setViewState] = useState<ViewState>(defaultViewState);

	// Effect to zoom the globe when showViewPanel changes
	useEffect(() => {
		if (!showViewPanel) {
			setViewState(defaultViewState);
		}
	}, [showViewPanel]);

	// Function to load custom icon for port markers
	const onMapLoad = () => {
		if (!mapRef.current) return;

		// Load a custom port icon image
		mapRef.current.loadImage('/static/media/pin.png', (error, image) => {
			if (error) throw error;

			// Add the image to the map style with the id referenced in portLayer
			if (image && mapRef.current && !mapRef.current.hasImage('port-icon')) {
				mapRef.current.addImage('port-icon', image);
			}
		});
	};

	return (
		<MapGL
			ref={mapRef}
			mapboxAccessToken={mapboxAccessToken}
			mapStyle={theme === 'light' ? lightStyle : darkStyle}
			onMove={evt => setViewState(evt.viewState)}
			{...viewState}
			projection="mercator"
			onLoad={onMapLoad}
		>
			{!showViewPanel && (
				<>
					<Source id="route-source" type="geojson" data={routeDataVoyage as GeoJSON.FeatureCollection}>
						<Layer {...routeLayer} />
					</Source>
					<Source id="port-points-source" type="geojson" data={portPointsVoyage as GeoJSON.FeatureCollection}>
						<Layer {...portLayer} />
					</Source>
				</>
			)}
		</MapGL>
	);
}
