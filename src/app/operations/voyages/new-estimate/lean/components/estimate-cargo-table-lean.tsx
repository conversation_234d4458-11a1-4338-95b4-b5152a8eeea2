import { Plus } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Combobox } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';

const ports = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, ES' },
	{ value: 'singapore', label: 'Singapore, SG' },
	{ value: 'las-palmas', label: 'Las Palmas, ES' },
	{ value: 'alappuzha', label: 'Alappuzha, IN' }
];

const cargoTerms = [
	{ value: 'sshex', label: 'SSHEX' },
	{ value: 'sshinc', label: 'SSHINC' }
];

const cargoes = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'oil', label: 'Oil' },
	{ value: 'gas', label: 'Gas' },
	{ value: 'petroleum', label: 'Petroleum' }
];

const units = [
	{ value: 'mts', label: 'mts' },
	{ value: 'kgs', label: 'kgs' },
	{ value: 'lts', label: 'lts' },
	{ value: 'cbm', label: 'cbm' }
];

const freightTypes = [
	{ value: 'rate', label: 'Rate' },
	{ value: 'lumpsum', label: 'Lumpsum' },
	{ value: 'world-scale', label: 'World scale' }
];

const inputClassName = 'bg-transparent border-none rounded-none';
const comboboxClassName = 'bg-transparent w-full font-normal border-none rounded-none';
const datePickerClassName = 'bg-transparent h-9 w-full font-normal border-none rounded-none';
const tableHeadClassName = 'font-normal px-3';
const tableCellClassName = 'px-0 py-0.5 font-normal';

export default function EstimateCargoTableLean() {
	const [, setSelectedLoadPort] = useState<string | null>(null);
	const [, setSelectedDischargePort] = useState<string | null>(null);
	const [, setSelectedCargoTerm] = useState<string | null>(null);
	const [, setSelectedCargo] = useState<string | null>(null);
	const [, setSelectedUnit] = useState<string | null>(null);
	const [, setSelectedFreightType] = useState<string | null>(null);
	const [laycanFromDate, setLaycanFromDate] = useState<Date | undefined>();
	const [laycanToDate, setLaycanToDate] = useState<Date | undefined>();
	const [arrivalDate, setArrivalDate] = useState<Date | undefined>();
	const [departureDate, setDepartureDate] = useState<Date | undefined>();

	return (
		<div className="grid gap-4">
			<Table className="border-b">
				<TableHeader className="text-xs">
					<TableRow className="border-t hover:bg-transparent">
						<TableHead className={tableHeadClassName}>Cargo</TableHead>
						<TableHead className={`w-[100px] ${tableHeadClassName}`}>Quantity</TableHead>
						<TableHead className={tableHeadClassName}>Unit</TableHead>
						<TableHead className={tableHeadClassName}>Laycan From</TableHead>
						<TableHead className={tableHeadClassName}>Laycan To</TableHead>
						<TableHead className={tableHeadClassName}>Freight</TableHead>
						<TableHead className={`w-[100px] ${tableHeadClassName}`}>Rate</TableHead>
						<TableHead className={tableHeadClassName}>Charterer</TableHead>
						<TableHead className={`w-[100px] ${tableHeadClassName}`}>Commission</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow className="hover:bg-transparent">
						<TableCell className={tableCellClassName}>
							<Combobox
								data={cargoes}
								placeholder="Select cargo"
								className={comboboxClassName}
								onSelect={value => setSelectedCargo(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Combobox
								data={units}
								placeholder="mts"
								className={comboboxClassName}
								onSelect={value => setSelectedUnit(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<DatePicker
								date={laycanFromDate}
								setDate={setLaycanFromDate}
								className={datePickerClassName}
								placeholder="dd/mm/yy"
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<DatePicker
								date={laycanToDate}
								setDate={setLaycanToDate}
								className={datePickerClassName}
								placeholder="dd/mm/yy"
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Combobox
								data={freightTypes}
								placeholder="Select type"
								className={comboboxClassName}
								onSelect={value => setSelectedFreightType(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="text" placeholder="Enter charterer" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="text" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<TableCell className={tableCellClassName}>
								<TooltipProvider delayDuration={100}>
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="secondary"
												size="icon"
												className="text-muted-foreground h-7 w-7"
											>
												<Plus />
											</Button>
										</TooltipTrigger>
										<TooltipContent>Add cargo</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</TableCell>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>

			<Separator className="bg-transparent" />

			<Table className="border-b">
				<TableHeader className="text-xs">
					<TableRow className="hover:bg-transparent">
						<TableHead className={`w-[100px] ${tableHeadClassName}`}></TableHead>
						<TableHead className={tableHeadClassName}>Port</TableHead>
						<TableHead className={`w-[120px] ${tableHeadClassName}`}>Port Cost</TableHead>
						<TableHead className={`w-[100px] ${tableHeadClassName}`}>Distance</TableHead>
						<TableHead className={`w-[100px] ${tableHeadClassName}`}>Days</TableHead>
						<TableHead className={`w-[100px] ${tableHeadClassName}`}>Quantity</TableHead>
						<TableHead className={`w-[120px] ${tableHeadClassName}`}>Terms</TableHead>
						<TableHead className={tableHeadClassName}>Arrival</TableHead>
						<TableHead className={tableHeadClassName}>Departure</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow className="hover:bg-transparent">
						<TableCell className={`text-nowrap ${tableCellClassName}`}>Load port(s)</TableCell>
						<TableCell className={tableCellClassName}>
							<Combobox
								data={ports}
								placeholder="Select port(s)"
								className={comboboxClassName}
								onSelect={value => setSelectedLoadPort(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Combobox
								data={cargoTerms}
								placeholder="Select"
								className={comboboxClassName}
								onSelect={value => setSelectedCargoTerm(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<DatePicker
								date={arrivalDate}
								setDate={setArrivalDate}
								className={datePickerClassName}
								placeholder="dd/mm/yy"
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<DatePicker
								date={departureDate}
								setDate={setDepartureDate}
								className={datePickerClassName}
								placeholder="dd/mm/yy"
							/>
						</TableCell>
					</TableRow>
					<TableRow className="hover:bg-transparent">
						<TableCell className={`text-nowrap ${tableCellClassName}`}>Discharge port(s)</TableCell>
						<TableCell className={tableCellClassName}>
							<Combobox
								data={ports}
								placeholder="Select port(s)"
								className={comboboxClassName}
								onSelect={value => setSelectedDischargePort(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Input className={inputClassName} type="number" placeholder="0" />
						</TableCell>
						<TableCell className={tableCellClassName}>
							<Combobox
								data={cargoTerms}
								placeholder="Select"
								className={comboboxClassName}
								onSelect={value => setSelectedCargoTerm(value)}
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<DatePicker
								date={arrivalDate}
								setDate={setArrivalDate}
								className={datePickerClassName}
								placeholder="dd/mm/yy"
							/>
						</TableCell>
						<TableCell className={tableCellClassName}>
							<DatePicker
								date={departureDate}
								setDate={setDepartureDate}
								className={datePickerClassName}
								placeholder="dd/mm/yy"
							/>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
