import { useState, useRef, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import EstimateFormLean from './estimate-form-lean';
import { Button } from '@/components/ui/button';

interface EstimateCalculatorLeanProps {
	onCalculateVoyage?: (showViewPanel: boolean) => void;
	onResetForm?: (showViewPanel: boolean) => void;
}

export default function EstimateCalculatorLean({ onCalculateVoyage, onResetForm }: EstimateCalculatorLeanProps) {
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [isResetting, setIsResetting] = useState<boolean>(false);

	// Reference to the form container for scrolling
	const formContainerRef = useRef<HTMLDivElement | null>(null);

	// Get a reference to the form container after component mounts
	useEffect(() => {
		formContainerRef.current = document.querySelector('.mt-24.flex-1.overflow-auto');
	}, []);

	const handleCalculateVoyage = () => {
		setIsLoading(true);

		setTimeout(() => {
			setIsLoading(false);

			// Scroll to the top of the form container
			if (formContainerRef.current) {
				formContainerRef.current.scrollTo({
					top: 0,
					behavior: 'smooth'
				});
			}

			if (onCalculateVoyage) {
				onCalculateVoyage(true);
			}
		}, 500);
	};

	const handleResetForm = () => {
		setIsResetting(true);

		setTimeout(() => {
			setIsResetting(false);

			// Scroll to the top of the form container
			if (formContainerRef.current) {
				formContainerRef.current.scrollTo({
					top: 0,
					behavior: 'smooth'
				});
			}

			if (onResetForm) {
				onResetForm(false);
			}
		}, 500);
	};

	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				exit={{ opacity: 0 }}
				transition={{ duration: 0.24 }}
				className="bg-background flex h-full w-full flex-col"
			>
				<div className="flex-1 overflow-auto">
					<EstimateFormLean />
					<div className="m-auto hidden p-4 text-center">
						<Button variant="secondary" size="sm" onClick={handleResetForm}>
							{isResetting ? (
								<>
									<Loader2 className="h-4 w-4 animate-spin" />
									<span>Resetting...</span>
								</>
							) : (
								'Reset Form'
							)}
						</Button>
					</div>
				</div>
				<div className="m-auto w-full max-w-2xl py-4">
					<Button size="sm" className="w-full" disabled={isLoading} onClick={handleCalculateVoyage}>
						{isLoading ? (
							<>
								<Loader2 className="h-4 w-4 animate-spin" />
								<span>Calculating...</span>
							</>
						) : (
							'Calculate'
						)}
					</Button>
				</div>
			</motion.div>
		</AnimatePresence>
	);
}
