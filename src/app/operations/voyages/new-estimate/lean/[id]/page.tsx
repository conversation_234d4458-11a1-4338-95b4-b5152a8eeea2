import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import EstimateMap from '../../components/estimate-map';
import EstimateViewPanel from '../../components/estimate-view-panel';
import { ResizablePanel, ResizableHandle, ResizablePanelGroup } from '@/components/ui/resizable';

export default function NewEstimateViewPage() {
	const [showViewPanel, setShowViewPanel] = useState(false);
	const navigate = useNavigate();

	// Set showViewPanel to true when the component mounts
	useEffect(() => {
		setShowViewPanel(true);
	}, []);

	return (
		<ResizablePanelGroup direction="horizontal">
			<ResizablePanel className="bg-sidebar relative">
				<EstimateMap showViewPanel={showViewPanel} />
			</ResizablePanel>
			<ResizableHandle />
			<ResizablePanel defaultSize={35} minSize={35} maxSize={50}>
				<EstimateViewPanel onClose={() => void navigate('/operations/voyages/new-estimate/lean')} />
			</ResizablePanel>
		</ResizablePanelGroup>
	);
}
