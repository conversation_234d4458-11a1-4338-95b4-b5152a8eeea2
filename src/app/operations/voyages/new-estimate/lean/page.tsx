import { useState } from 'react';
import { PanelRight } from 'lucide-react';
import EstimateCalculatorLean from './components/estimate-calculator-lean';
import EstimateViewLean from './components/estimate-view-lean';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger, SidebarProvider, Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';

export default function Page() {
	const [open, setOpen] = useState(false);

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/voyages">Voyages</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>New Estimate</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex gap-2 px-4">
					<ModeToggle />
					<Button variant="outline" size="icon" onClick={() => setOpen(open => !open)}>
						<PanelRight />
					</Button>
				</div>
			</header>

			<SidebarProvider
				style={{ '--sidebar-width': '33rem' } as React.CSSProperties}
				open={open}
				className="relative flex min-h-0 flex-1 flex-row overflow-hidden border-none pt-4"
				onOpenChange={setOpen}
			>
				<EstimateCalculatorLean onCalculateVoyage={setOpen} onResetForm={setOpen} />
				<Sidebar variant="floating" side="right" className="sidebar-b-l-0">
					<SidebarContent className="bg-background">
						<EstimateViewLean onResetForm={setOpen} />
					</SidebarContent>
				</Sidebar>
			</SidebarProvider>
		</>
	);
}
