import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { Loader2, Eraser } from 'lucide-react';
import EstimateFormX1 from './estimate-form-x1';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface EstimateCalculatorPanelProps {
	onCalculateVoyage?: () => void;
}

export default function EstimateCalculatorPanel({ onCalculateVoyage }: EstimateCalculatorPanelProps) {
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const handleCalculateVoyage = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			if (onCalculateVoyage) {
				onCalculateVoyage();
			}
		}, 500);
	};
	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, x: 20 }}
				animate={{ opacity: 1, x: 0 }}
				exit={{ opacity: 0, x: -20 }}
				transition={{ duration: 0.24 }}
				className="h-full"
			>
				<div className="flex h-full w-full flex-col">
					<div className="flex items-center justify-between p-6 pt-4">
						<div className="flex h-8 items-center leading-none font-semibold">Voyage Estimate</div>
						<TooltipProvider delayDuration={100}>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
										<Eraser />
									</Button>
								</TooltipTrigger>
								<TooltipContent>Clear form</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					</div>
					<div className="flex-1 overflow-auto">
						<EstimateFormX1 />
					</div>
					<div className="flex items-center gap-2 p-4">
						<Button
							variant="default"
							size="sm"
							className="w-full flex-1"
							disabled={isLoading}
							onClick={handleCalculateVoyage}
						>
							{isLoading ? (
								<>
									<Loader2 className="h-4 w-4 animate-spin" />
									<span>Calculating...</span>
								</>
							) : (
								'Calculate'
							)}
						</Button>
					</div>
				</div>
			</motion.div>
		</AnimatePresence>
	);
}
