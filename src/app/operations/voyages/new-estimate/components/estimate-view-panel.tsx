import { useState } from 'react';
import { useNavigate } from 'react-router';
import { AnimatePresence, motion } from 'framer-motion';
import { Circle, CircleCheck, MoveLeft, Loader2, History, Box, CircleDot } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import ChatbotSimple from '@/components/chatbot/chatbot-simple';
import { cn } from '@/lib/utils';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

const tasks = [
	{
		id: '1',
		status: 'todo',
		text: 'Create Voyage'
	},
	{
		id: '2',
		status: 'todo',
		text: 'Check Restrictions'
	},
	{
		id: '3',
		status: 'todo',
		text: 'Check Prices'
	},
	{
		id: '4',
		status: 'todo',
		text: 'Check Lineups'
	},
	{
		id: '5',
		status: 'todo',
		text: 'Check Weather'
	},
	{
		id: '6',
		status: 'todo',
		text: 'Send Notice of Fixing'
	},
	{
		id: '7',
		status: 'todo',
		text: 'Appoint Agent'
	},
	{
		id: '8',
		status: 'todo',
		text: 'Instruct Master'
	}
];

interface EstimateViewPanelProps {
	onClose?: () => void;
}

export default function EstimateViewPanel({ onClose }: EstimateViewPanelProps) {
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const navigate = useNavigate();

	const handleCreateVoyage = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			void navigate('/operations/voyages/7f8a9b0c-1d2e-4f3a-5b6c-7d8e9f0a1b2c');
		}, 500);
	};

	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0, x: 20 }}
				animate={{ opacity: 1, x: 0 }}
				exit={{ opacity: 0, x: -20 }}
				transition={{ duration: 0.24 }}
				className="h-full"
			>
				<div className="bg-background flex h-full flex-col">
					<div className="flex items-center gap-2 px-4 py-6 pt-4">
						<Button
							variant="ghost"
							size="icon"
							className="text-muted-foreground size-8 self-start"
							onClick={onClose}
						>
							<MoveLeft className="h-4 w-4" />
						</Button>
						<div className="flex-1 leading-none font-semibold">#VOY-LA-01</div>
						<TooltipProvider delayDuration={100}>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
										<History />
									</Button>
								</TooltipTrigger>
								<TooltipContent>Versions</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					</div>

					<div className="flex-1 overflow-auto">
						<div className="px-6">
							<div className="bg-panel flex flex-col gap-4 rounded-xl border p-4 shadow-2xl">
								<div className="flex items-center justify-between gap-2">
									<div className="flex items-center gap-2">
										<div className="font-semibold">mv Suez Navigator</div>
									</div>
									<Badge variant="secondary" className="rounded-full text-xs">
										<Box className="text-primary size-3" />
										<span className="text-muted-foreground">35,000 mts - Iron Ore</span>
									</Badge>
								</div>
								<div className="flex items-center gap-4">
									<div className="flex flex-1 flex-col items-start">
										<div className="text-base leading-5">
											<span className="font-semibold">17</span>d :{' '}
											<span className="font-semibold">3</span>h
										</div>
										<div className="text-muted-foreground text-xs font-normal">Duration</div>
									</div>
									<Separator orientation="vertical" className="h-4" />
									<div className="flex flex-1 flex-col items-center">
										<div className="text-base leading-5 font-semibold">5,023 nm</div>
										<div className="text-muted-foreground text-xs font-normal">Distance</div>
									</div>
									<Separator orientation="vertical" className="h-4" />
									<div className="flex flex-1 flex-col items-center">
										<div className="text-base leading-5 font-semibold">$26,314</div>
										<div className="text-muted-foreground text-xs font-normal">Net TCE</div>
									</div>
									<Separator orientation="vertical" className="h-4" />
									<div className="flex flex-1 flex-col items-end">
										<div className="text-base leading-5 font-semibold">$476,280</div>
										<div className="text-muted-foreground text-xs font-normal">Estimated P&L</div>
									</div>
								</div>
							</div>
						</div>

						<div className="relative z-10 grid gap-6 p-8">
							<div className="sep absolute top-8 bottom-20 left-10 -z-10 w-[1px] bg-slate-700"></div>
							<div className="flex gap-2">
								<div>
									<div className="bg-background py-1.5">
										<CircleDot className="text-muted-foreground size-4" />
									</div>
								</div>
								<div className="grid w-full gap-2">
									<div className="flex items-center justify-between gap-2">
										<div className="flex items-center gap-1">
											<Button variant="ghost" className="h-auto p-1 font-medium">
												Las Palmas, ES
											</Button>
											<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
												Comm.
											</Badge>
										</div>
										<div className="text-muted-foreground pr-2 text-sm">---</div>
									</div>
									<div className="grid grid-cols-[60px_minmax(0px,_1fr)_60px_minmax(0px,_1fr)] gap-1.5 px-1 text-xs">
										<div className="text-muted-foreground">Depart</div>
										<div>19 Apr, 08:00</div>
										<div className="text-muted-foreground col-span-2 pr-1 text-right">
											380 mts / 200 mts
										</div>
									</div>
								</div>
							</div>
							<div className="flex gap-2">
								<div>
									<div className="bg-background py-1.5">
										<Circle className="text-muted-foreground size-4" />
									</div>
								</div>
								<div className="grid w-full gap-2">
									<div className="flex items-center justify-between gap-2">
										<div className="flex items-center gap-1">
											<span className="text-muted-foreground text-sm">via</span>
											<Button variant="ghost" className="h-auto p-1 font-medium">
												Port Said, EG
											</Button>
											<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
												L
											</Badge>
										</div>
										<div className="pr-2 text-sm font-semibold">$112,000</div>
									</div>
									<div className="grid grid-cols-[60px_minmax(0px,_1fr)_60px_minmax(0px,_1fr)] gap-1.5 px-1 text-xs">
										<div className="text-muted-foreground">Arrive</div>
										<div>17 Apr, 08:00</div>
										<div className="text-muted-foreground col-span-2 pr-1 text-right">
											318 mts / 195 mts
										</div>
										<div className="text-muted-foreground">Depart</div>
										<div>19 Apr, 08:00</div>
										<div className="text-muted-foreground col-span-2 pr-1 text-right">
											220 mts / 163 mts
										</div>
									</div>
								</div>
							</div>
							<div className="flex gap-2">
								<div>
									<div className="bg-background py-1.5">
										<Circle className="text-muted-foreground size-4" />
									</div>
								</div>
								<div className="grid w-full gap-2">
									<div className="flex items-center justify-between gap-2">
										<div className="flex items-center gap-1">
											<Button variant="ghost" className="h-auto p-1 font-medium">
												Alappuzha, IN
											</Button>
											<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
												D
											</Badge>
										</div>
										<div className="pr-2 text-sm font-semibold">$193,500</div>
									</div>
									<div className="grid grid-cols-[60px_minmax(0px,_1fr)_60px_minmax(0px,_1fr)] gap-1.5 px-1 text-xs">
										<div className="text-muted-foreground">Arrive</div>
										<div>28 Apr, 08:00</div>
										<div className="text-muted-foreground col-span-2 pr-1 text-right">
											195 mts / 124 mts
										</div>
										<div className="text-muted-foreground">Depart</div>
										<div>02 May, 10:00</div>
										<div className="text-muted-foreground col-span-2 pr-1 text-right">
											183 mts / 111 mts
										</div>
									</div>
								</div>
							</div>
						</div>

						<div className="grid gap-6 p-6 pt-0 pb-0">
							<Table className="text-sm">
								<TableBody>
									<TableRow className="border-border/50 border-t border-b">
										<TableCell className="text-muted-foreground w-1/2">Total Sailing</TableCell>
										<TableCell className="text-right">17d : 3h</TableCell>
									</TableRow>
									<TableRow className="border-border/50 border-t border-b">
										<TableCell className="text-muted-foreground w-1/2">Total Port Stay</TableCell>
										<TableCell className="text-right">4d : 2h</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="w-1/2">
											<div className="font-semibold">Total Voyage Time</div>
										</TableCell>
										<TableCell className="text-right">
											<div className="text-base">
												<span className="font-semibold">17</span>d :{' '}
												<span className="font-semibold">3</span>h
											</div>
										</TableCell>
									</TableRow>

									<TableRow className="border-none hover:bg-transparent">
										<TableCell colSpan={2}></TableCell>
									</TableRow>

									<TableRow className="border-b-border/50">
										<TableCell className="text-muted-foreground w-1/2">Gross Freight</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground">$</span>
												<span>324,000</span>
											</div>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="text-muted-foreground w-1/2">TCE</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground">$</span>
												<span>26,314</span>
											</div>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="text-muted-foreground w-1/2">Net Daily</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground">$</span>
												<span>134</span>
											</div>
										</TableCell>
									</TableRow>
									<TableRow className="border-b-border/50">
										<TableCell className="w-1/2">
											<div className="font-semibold">Total Expenses</div>
										</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end gap-1">
												<span className="text-muted-foreground text-base">$</span>
												<span className="text-base font-semibold">476,280</span>
											</div>
										</TableCell>
									</TableRow>
								</TableBody>
							</Table>
							<Button
								variant="default"
								size="xs"
								className="w-full"
								disabled={isLoading}
								onClick={handleCreateVoyage}
							>
								{isLoading ? (
									<>
										<Loader2 className="h-4 w-4 animate-spin" />
										<span>Creating...</span>
									</>
								) : (
									'Create Voyage'
								)}
							</Button>
						</div>

						<div className="grid gap-1 px-4 py-6">
							<div className="p-2 text-xs font-semibold">Next Tasks</div>
							<div className="grid">
								{tasks.map(item => (
									<Button
										key={item.id}
										variant="ghost"
										className="group text-muted-foreground h-auto justify-start bg-transparent px-2"
									>
										{item.status === 'done' ? (
											<CircleCheck className="text-background fill-primary" />
										) : (
											<Circle className="text-muted-foreground" />
										)}
										<div
											className={cn(
												`flex-1 text-left font-normal text-wrap`,
												item.status === 'done' ? 'line-through' : ''
											)}
										>
											{item.text}
										</div>
									</Button>
								))}
							</div>
						</div>
					</div>
					<ChatbotSimple />
				</div>
			</motion.div>
		</AnimatePresence>
	);
}
