import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import { Combobox } from '@/components/ui/combobox';
import { Separator } from '@/components/ui/separator';

// Sample data for dropdowns
const vessels = [
	{ value: 'meridiaan-express', label: 'mv Meridiaan Express' },
	{ value: 'meridiaan-cinco', label: 'mv Meridiaan Cinco' },
	{ value: 'vertom-meridiaan', label: 'mv Vertom Meridiaan' },
	{ value: 'gulf-meridiaan', label: 'mv Gulf Meridiaan' },
	{ value: 'astra-meridiaan', label: 'mv Astra Meridiaan' }
];

const ports = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, ES' },
	{ value: 'singapore', label: 'Singapore, SG' },
	{ value: 'las-palmas', label: 'Las Palmas, ES' },
	{ value: 'alappuzha', label: 'Alappuzha, IN' }
];

const cargoes = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'oil', label: 'Oil' },
	{ value: 'gas', label: 'Gas' },
	{ value: 'petroleum', label: 'Petroleum' }
];

const units = [
	{ value: 'mts', label: 'mts' },
	{ value: 'kgs', label: 'kgs' },
	{ value: 'lts', label: 'lts' },
	{ value: 'cbm', label: 'cbm' }
];

const labelClassName = 'text-muted-foreground w-full font-normal text-xs';
const inputClassName = 'bg-accent/50 ';
const comboboxClassName = 'bg-accent/50  w-full font-normal';
const datePickerClassName = 'bg-accent/50  h-9 w-full font-normal';

export default function EstimateFormX2() {
	const [, setSelectedVessel] = useState<string | null>(null);
	const [, setSelectedLoadPort] = useState<string | null>(null);
	const [, setSelectedUnit] = useState<string | null>(null);
	const [, setSelectedDischargePort] = useState<string | null>(null);
	const [, setSelectedCargo] = useState<string | null>(null);
	const [commenceDate, setCommenceDate] = useState<Date | undefined>(new Date());

	return (
		<div className="m-auto grid max-w-2xl gap-2">
			{/* VESSEL Section */}
			<div className="grid gap-x-6 gap-y-4 p-4 md:grid-cols-2">
				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Vessel</Label>
					<div className="flex-1">
						<Combobox
							data={vessels}
							placeholder="Select vessel"
							className={comboboxClassName}
							onSelect={value => setSelectedVessel(value)}
						/>
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Start Date</Label>
					<div className="flex-1">
						<DatePicker date={commenceDate} setDate={setCommenceDate} className={datePickerClassName} />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Daily Cost/Addr</Label>
					<div className="grid flex-1 gap-2 md:grid-cols-2">
						<Input className={inputClassName} type="number" placeholder="0" suffix="USD" />
						<Input className={inputClassName} type="number" placeholder="0" suffix="USD" />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Speed Ballast/Laden</Label>
					<div className="grid flex-1 gap-2 md:grid-cols-2">
						<Input className={inputClassName} type="number" placeholder="0" suffix="kn" />
						<Input className={inputClassName} type="number" placeholder="0" suffix="kn" />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>MGO At Sea/At Port</Label>
					<div className="grid flex-1 gap-2 md:grid-cols-2">
						<Input className={inputClassName} type="number" placeholder="0" suffix="mt" />
						<Input className={inputClassName} type="number" placeholder="0" suffix="mt" />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>FO/MGO Price</Label>
					<div className="grid flex-1 gap-2 md:grid-cols-2">
						<Input className={inputClassName} type="number" placeholder="0" suffix="USD" />
						<Input className={inputClassName} type="number" placeholder="0" suffix="USD" />
					</div>
				</div>
			</div>

			<div className="px-4">
				<Separator />
			</div>

			{/* CARGO Section */}
			<div className="grid gap-x-6 gap-y-4 p-4 md:grid-cols-2">
				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Cargo</Label>
					<div className="flex-1">
						<Combobox
							data={cargoes}
							placeholder="Select cargo"
							className={comboboxClassName}
							onSelect={value => setSelectedCargo(value)}
						/>
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Quantity/Unit</Label>
					<div className="grid flex-1 gap-2 md:grid-cols-2">
						<Input className={inputClassName} type="number" placeholder="0" />
						<Combobox
							data={units}
							placeholder="mts"
							className={comboboxClassName}
							onSelect={value => setSelectedUnit(value)}
						/>
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Load Port(s)</Label>
					<div className="flex-1">
						<Combobox
							data={ports}
							placeholder="Select load port"
							className={comboboxClassName}
							onSelect={value => setSelectedLoadPort(value)}
						/>
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Load Time</Label>
					<div className="flex-1">
						<Input className={inputClassName} type="number" placeholder="0" suffix="days" />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Discharge Port(s)</Label>
					<div className="flex-1">
						<Combobox
							data={ports}
							placeholder="Select discharge port"
							className={comboboxClassName}
							onSelect={value => setSelectedDischargePort(value)}
						/>
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Discharge Time</Label>
					<div className="flex-1">
						<Input className={inputClassName} type="number" placeholder="0" suffix="days" />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Freight Rate</Label>
					<div className="flex-1">
						<Input className={inputClassName} type="number" placeholder="0" suffix="USD/mt" />
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-2">
					<Label className={labelClassName}>Commission %</Label>
					<div className="flex-1">
						<Input className={inputClassName} type="number" placeholder="0" suffix="%" />
					</div>
				</div>
			</div>
		</div>
	);
}
