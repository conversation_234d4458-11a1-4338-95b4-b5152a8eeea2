import 'mapbox-gl/dist/mapbox-gl.css';

import { useState, useRef, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { AnimatePresence, motion } from 'framer-motion';

import MapGL, { MapRef, Source, Layer } from 'react-map-gl/mapbox';
import { ModeToggle } from '@/components/theme/mode-toggle';
import {
	lightStyle,
	darkStyle,
	routeDataVoyage,
	portVoyageLayer,
	portPointsVoyage,
	routeVoyageLayer
} from '@/app/mapbox/data/data';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';

const mapboxAccessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;

interface ViewState {
	longitude: number;
	latitude: number;
	zoom: number;
}

interface EstimateMapProps {
	showViewPanel?: boolean;
}

const defaultViewState = {
	longitude: -70,
	latitude: 22,
	zoom: 1.5
};

// Route-specific view state that focuses on the Las Palmas to Alappuzha route
const routeViewState = {
	longitude: 30.0, // Centered on the Mediterranean/Red Sea area
	latitude: 20.0, // Centered between Europe, Africa, and Asia
	zoom: 2.5 // Zoomed out enough to see the entire route
};

export default function EstimateMap({ showViewPanel = false }: EstimateMapProps) {
	const { theme } = useTheme();
	const mapRef = useRef<MapRef>(null);

	const [viewState, setViewState] = useState<ViewState>(defaultViewState);

	// Effect to zoom the globe when showViewPanel changes
	useEffect(() => {
		if (showViewPanel) {
			// When view panel is shown, animate to the route view
			if (mapRef.current) {
				mapRef.current.flyTo({
					...routeViewState,
					center: [routeViewState.longitude, routeViewState.latitude],
					essential: true,
					curve: 1.42,
					duration: 1500, // Animation duration in milliseconds
					easing: t => t // Linear easing
				});
				// Update the view state after animation
				setViewState(routeViewState);
			}
		} else {
			// When view panel is hidden, return to default view
			if (mapRef.current) {
				mapRef.current.flyTo({
					...defaultViewState,
					center: [defaultViewState.longitude, defaultViewState.latitude],
					essential: true,
					curve: 1.42,
					duration: 1000
				});
				// Update the view state after animation
				setViewState(defaultViewState);
			}
		}
	}, [showViewPanel]);
	return (
		<>
			<header className="absolute top-0 z-50 flex h-16 w-full shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/voyages">Voyages</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>New Estimate</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="hidden px-4">
					<ModeToggle />
				</div>
			</header>
			<AnimatePresence>
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					transition={{ duration: 1.5 }}
					className="relative h-full"
				>
					<MapGL
						ref={mapRef}
						mapboxAccessToken={mapboxAccessToken}
						mapStyle={theme === 'light' ? lightStyle : darkStyle}
						onMove={evt => setViewState(evt.viewState)}
						{...viewState}
						onLoad={() => {
							if (!mapRef.current) return;

							// Load a custom port icon image
							mapRef.current.loadImage('/static/media/pin.png', (error, image) => {
								if (error) throw error;

								// Add the image to the map style with the id referenced in portLayer
								if (image && mapRef.current && !mapRef.current.hasImage('port-icon')) {
									mapRef.current.addImage('port-icon', image);
								}
							});
						}}
					>
						{showViewPanel && (
							<>
								<Source
									id="port-points-source"
									type="geojson"
									data={portPointsVoyage as GeoJSON.FeatureCollection}
								>
									<Layer {...portVoyageLayer} />
								</Source>
								<Source
									id="route-source"
									type="geojson"
									data={routeDataVoyage as GeoJSON.FeatureCollection}
								>
									<Layer {...routeVoyageLayer} />
								</Source>
							</>
						)}
					</MapGL>
				</motion.div>
			</AnimatePresence>
		</>
	);
}
