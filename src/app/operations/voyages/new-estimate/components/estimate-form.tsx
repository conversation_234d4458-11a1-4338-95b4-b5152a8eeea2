import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import { Combobox } from '@/components/ui/combobox';
import { Separator } from '@/components/ui/separator';

// Sample data for dropdowns
const vessels = [
	{ value: 'meridiaan-express', label: 'mv Meridiaan Express' },
	{ value: 'meridiaan-cinco', label: 'mv Meridiaan Cinco' },
	{ value: 'vertom-meridiaan', label: 'mv Vertom Meridiaan' },
	{ value: 'gulf-meridiaan', label: 'mv Gulf Meridiaan' },
	{ value: 'astra-meridiaan', label: 'mv Astra Meridiaan' }
];

const ports = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, ES' },
	{ value: 'singapore', label: 'Singapore, SG' },
	{ value: 'las-palmas', label: 'Las Palmas, ES' },
	{ value: 'alappuzha', label: 'Alappuzha, IN' }
];

const freightTypes = [
	{ value: 'lumpsum', label: 'Lumpsum' },
	{ value: 'per-ton', label: 'Per Ton' },
	{ value: 'per-day', label: 'Per Day' }
];

const cargoes = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'oil', label: 'Oil' },
	{ value: 'gas', label: 'Gas' },
	{ value: 'petroleum', label: 'Petroleum' }
];

const charterers = [
	{ value: 'global-chartering', label: 'Global Chartering S.A.' },
	{ value: 'american-shipping', label: 'American Shipping Co.' },
	{ value: 'singapore-trading', label: 'Singapore Trading Ltd.' },
	{ value: 'panama-logistics', label: 'Panama Logistics S.A.' },
	{ value: 'mediterranean-shipping', label: 'Mediterranean Shipping Corp.' }
];

const units = [
	{ value: 'mts', label: 'mts' },
	{ value: 'kgs', label: 'kgs' },
	{ value: 'lts', label: 'lts' },
	{ value: 'cbm', label: 'cbm' }
];

const labelClassName = 'text-muted-foreground w-40 font-normal';
const inputClassName = 'bg-accent/50';
const comboboxClassName = 'bg-accent/50 w-full font-normal';
const datePickerClassName = 'bg-accent/50 h-9 w-full font-normal';

export default function EstimateForm() {
	const [, setSelectedVessel] = useState<string | null>(null);
	const [, setSelectedCharterer] = useState<string | null>(null);
	const [, setSelectedLoadPort] = useState<string | null>(null);
	const [, setSelectedUnit] = useState<string | null>(null);
	const [, setSelectedDischargePort] = useState<string | null>(null);
	const [, setSelectedFreightType] = useState<string | null>(null);
	const [, setSelectedCargo] = useState<string | null>(null);
	const [commenceDate, setCommenceDate] = useState<Date | undefined>(new Date());
	const [completeDate, setCompleteDate] = useState<Date | undefined>();
	const [laycanFrom, setLaycanFrom] = useState<Date | undefined>();
	const [laycanTo, setLaycanTo] = useState<Date | undefined>();

	return (
		<div className="grid py-1">
			{/* VESSEL Section */}
			<div className="p-6 pt-0">
				<div className="grid gap-2">
					<div className="flex items-center">
						<Label className={labelClassName}>Vessel</Label>
						<div className="flex-1">
							<Combobox
								data={vessels}
								placeholder="Select vessel"
								className={comboboxClassName}
								onSelect={value => setSelectedVessel(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Start Date</Label>
						<div className="flex-1">
							<DatePicker date={commenceDate} setDate={setCommenceDate} className={datePickerClassName} />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Daily Cost/Addr</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="USD" />
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="USD" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>DWT/DWF %</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0" suffix="mts" />
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="%" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Speed Ballast/Laden</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="kn" />
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="kn" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Ballast Bonus</Label>
						<div className="flex-1">
							<Input className={inputClassName} type="number" placeholder="0" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Ballast Port</Label>
						<div className="flex-1">
							<Combobox
								data={ports}
								placeholder="Select port"
								className={comboboxClassName}
								onSelect={() => {}}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>FO At Sea/At Port</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.00" />
							<Input className={inputClassName} type="number" placeholder="0.00" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>MGO At Sea/At Port</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.00" />
							<Input className={inputClassName} type="number" placeholder="0.00" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>FO/MGO Price</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="USD" />
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="USD" />
						</div>
					</div>
				</div>
			</div>

			<Separator />

			{/* CARGO Section */}
			<div className="p-6">
				<h4 className="text-md mb-2 font-medium">Cargo</h4>
				<div className="grid gap-2">
					<div className="flex items-center">
						<Label className={labelClassName}>Cargo</Label>
						<div className="flex-1">
							<Combobox
								data={cargoes}
								placeholder="Select cargo"
								className={comboboxClassName}
								onSelect={value => setSelectedCargo(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Charterer</Label>
						<div className="flex-1">
							<Combobox
								data={charterers}
								placeholder="Select charterer"
								className={comboboxClassName}
								onSelect={value => setSelectedCharterer(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Quantity/Unit</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.000" />
							<Combobox
								data={units}
								placeholder="mts"
								className={comboboxClassName}
								onSelect={value => setSelectedUnit(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Min/Max Qty</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0" />
							<Input className={inputClassName} type="number" placeholder="0" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Laycan From/To</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<DatePicker date={laycanFrom} setDate={setLaycanFrom} className={datePickerClassName} />
							<DatePicker date={laycanTo} setDate={setLaycanTo} className={datePickerClassName} />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Load Port(s)</Label>
						<div className="flex-1">
							<Combobox
								data={ports}
								placeholder="Select load port"
								className={comboboxClassName}
								onSelect={value => setSelectedLoadPort(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Discharge Port(s)</Label>
						<div className="flex-1">
							<Combobox
								data={ports}
								placeholder="Select discharge port"
								className={comboboxClassName}
								onSelect={value => setSelectedDischargePort(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Load/Discharge Time</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0" suffix="days" />
							<Input className={inputClassName} type="number" placeholder="0" suffix="days" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Freight Type</Label>
						<div className="flex-1">
							<Combobox
								data={freightTypes}
								placeholder="Select freight type"
								className={comboboxClassName}
								onSelect={value => setSelectedFreightType(value)}
							/>
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Freight Rate</Label>
						<div className="flex-1">
							<Input className={inputClassName} type="number" placeholder="00.00" suffix="USD/mt" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Commission %</Label>
						<div className="flex-1">
							<Input className={inputClassName} type="number" placeholder="0" suffix="%" />
						</div>
					</div>
				</div>
			</div>

			<Separator />

			{/* REPOSITION PORT Section */}
			<div className="p-6">
				<h4 className="text-md mb-2 font-medium">Reposition Port</h4>
				<div className="grid gap-2">
					<div className="flex items-center">
						<Label className={labelClassName}>Routing</Label>
						<div className="flex-1">
							<Input className={inputClassName} placeholder="Enter routing" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Port Days/Sea Days</Label>
						<div className="grid flex-1 grid-cols-2 gap-2">
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="days" />
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="days" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Total Days</Label>
						<div className="flex-1">
							<Input className={inputClassName} type="number" placeholder="0.00" suffix="days" />
						</div>
					</div>

					<div className="flex items-center">
						<Label className={labelClassName}>Complete Date</Label>
						<div className="flex-1">
							<DatePicker date={completeDate} setDate={setCompleteDate} className={datePickerClassName} />
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
