import { useState } from 'react';
import EstimateCalculatorPanel from './components/estimate-calculator-panel';
import EstimateMap from './components/estimate-map';
import EstimateViewPanel from './components/estimate-view-panel';
import { ResizablePanel, ResizableHandle, ResizablePanelGroup } from '@/components/ui/resizable';

export default function NewEstimatePage() {
	const [showViewPanel, setShowViewPanel] = useState(false);

	return (
		<ResizablePanelGroup direction="horizontal" className="">
			<ResizablePanel className="bg-sidebar relative h-full">
				<EstimateMap showViewPanel={showViewPanel} />
			</ResizablePanel>
			<ResizableHandle />
			<ResizablePanel defaultSize={35} minSize={35} maxSize={50}>
				{showViewPanel && <EstimateViewPanel onClose={() => setShowViewPanel(false)} />}
				{!showViewPanel && <EstimateCalculatorPanel onCalculateVoyage={() => setShowViewPanel(true)} />}
			</ResizablePanel>
		</ResizablePanelGroup>
	);
}
