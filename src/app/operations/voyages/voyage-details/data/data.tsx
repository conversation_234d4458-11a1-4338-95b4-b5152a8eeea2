import { CloudSun, Fuel, Text } from 'lucide-react';

export const data = {
	responses: [
		{
			id: '1',
			text: 'What can I help you ship?',
			type: 'greeting',
			suggestions: [
				{
					id: '1',
					icon: Text,
					color: 'text-sky-500',
					refResponse: 'voyage-overview',
					text: 'Voyage Overview'
				},
				{
					id: '2',
					icon: CloudSun,
					color: 'text-amber-500',
					refResponse: 'check-weather-forecast',
					text: 'Check Weather'
				},
				{
					id: '4',
					icon: Fuel,
					color: 'text-primary',
					refResponse: 'check-fuel-consumption',
					text: 'Check Fuel'
				}
			]
		},
		{
			id: 'voyage-overview',
			text: `Here is an overview of your voyage

> * Delayed by 3 hours due to adverse weather conditions.
> * Additionally, a PDA document is pending your approval.
> * Higher fuel consumption of 3.1 mts.`,
			tasks: [],
			suggestions: [
				{
					id: '2',
					refResponse: 'check-weather-forecast',
					text: 'Check Weather'
				},
				{
					id: '4',
					refResponse: 'check-fuel-consumption',
					text: 'Check Fuel'
				}
			]
		},
		{
			id: 'check-weather-forecast',
			text: 'Weather forecast for Port Said, EG',
			type: 'weather',
			suggestions: [
				{
					id: '4',
					refResponse: 'check-fuel-consumption',
					text: 'Check Fuel'
				}
			]
		},
		{
			id: 'check-fuel-consumption',
			text: 'The fuel consumption for the last 24 hours is 2,056 mt.',
			type: 'bunker',
			suggestions: [
				{
					id: '5',
					refResponse: 'plan-a-voyage',
					text: 'Plan my next voyage'
				}
			]
		},
		{
			id: 'plan-a-voyage',
			text: 'Let&apos;s get started with a new voyage.',
			type: 'form',
			suggestions: [
				{
					id: '1',
					refResponse: 'create-voyage',
					text: 'Create Voyage'
				}
			]
		},
		{
			id: 'create-voyage',
			text: 'Great! Your voyage has been created.',
			type: 'voyage',
			tasks: [
				{
					id: '1',
					refResponse: 'plan-a-voyage',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'todo',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'todo',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'todo',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'todo',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'todo',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'todo',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'check-restrictions',
			text: `Here are the Ports Restrictions for your ports \n\n **Rotterdam, NL**

			✅ General Restrictions
			✅ Operational Restrictions
			✅ Safety & Security
			✅ Cargo Restrictions
			✅ Other Restrictions \n\n **Port Said, EG**

			✅ General Restrictions
			✅ Operational Restrictions
			✅ Safety & Security
			✅ Cargo Restrictions
			✅ Other Restrictions`,
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'todo',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'todo',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'todo',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'todo',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'todo',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'check-prices',
			text: `Here are the prices for your ports 
			\n\n **Rotterdam, NL** - 900.00 USD/mt

			Hire: 100.00 USD/mt
			Port Fees: 100.00 USD/mt
			Other: 100.00 USD/mt
			Total: 700.00 USD/mt \n\n **Port Said, EG** - 700.00 USD/mt

			Hire: 100.00 USD/mt
			Port Fees: 100.00 USD/mt
			Other: 100.00 USD/mt
			Total: 700.00 USD/mt`,
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'done',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'todo',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'todo',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'todo',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'todo',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'check-lineups',
			text: `Here are the lineups for your ports 
			\n\n **Port Said, EG Lineups**

			[Some Lineups Here]`,
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'done',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'done',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'todo',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'todo',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'todo',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'check-weather',
			text: `Here is the weather forecast for Port Said, EG`,
			type: 'weather',
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'done',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'done',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'done',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'todo',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'todo',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'send-notice-of-fixing',
			text: `Here is the notice of fixing for your voyage`,
			type: 'email',
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'done',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'done',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'done',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'done',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'todo',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'appoint-agent',
			text: `Let's appoint an agent for Port Said, EG`,
			type: 'email',
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'done',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'done',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'done',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'done',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'done',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'todo',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		},
		{
			id: 'instruct-master',
			text: `Here are the instructions to the Master for your voyage`,
			type: 'email',
			tasks: [
				{
					id: '1',
					refResponse: '5',
					status: 'done',
					text: 'Create Voyage'
				},
				{
					id: '2',
					refResponse: 'check-restrictions',
					status: 'done',
					text: 'Check Restrictions'
				},
				{
					id: '3',
					refResponse: 'check-prices',
					status: 'done',
					text: 'Check Prices'
				},
				{
					id: '4',
					refResponse: 'check-lineups',
					status: 'done',
					text: 'Check Lineups'
				},
				{
					id: '5',
					refResponse: 'check-weather',
					status: 'done',
					text: 'Check Weather'
				},
				{
					id: '6',
					refResponse: 'send-notice-of-fixing',
					status: 'done',
					text: 'Send Notice of Fixing'
				},
				{
					id: '7',
					refResponse: 'appoint-agent',
					status: 'done',
					text: 'Appoint Agent'
				},
				{
					id: '8',
					refResponse: 'instruct-master',
					status: 'done',
					text: 'Instruct Master'
				}
			],
			suggestions: []
		}
	],
	weather: [
		{ date: 'Fri, Mar 28', icon: '🌦️', forecast: 'Possible showers', high: '14°C', low: '8°C' },
		{ date: 'Sat, Mar 29', icon: '🌤️', forecast: 'Partly cloudy', high: '14°C', low: '7°C' },
		{ date: 'Sun, Mar 30', icon: '☁️', forecast: 'Mostly cloudy', high: '13°C', low: '8°C' },
		{
			date: 'Mon, Mar 31',
			icon: '🌧️',
			forecast: 'Occasional rain showers',
			high: '14°C',
			low: '8°C'
		},
		{ date: 'Tue, Apr 01', icon: '☁️', forecast: 'Cloudy', high: '13°C', low: '10°C' },
		{ date: 'Wed, Apr 02', icon: '🌧️', forecast: 'Light rain', high: '15°C', low: '10°C' },
		{ date: 'Thu, Apr 03', icon: '☁️', forecast: 'Increasing cloudiness', high: '15°C', low: '9°C' }
	],
	email: {
		templates: [
			{
				id: 'delay-notification',
				name: 'Delay Notification',
				subject: 'Delay Notification – MV *Suez Navigator*',
				content: `Dear [Port Agent's Name],

Please be advised that MV *Suez Navigator* is delayed by **3 hours** en route to Port Said due to adverse weather conditions. The updated ETA is now **[New ETA Time]**.

Kindly acknowledge receipt of this update and confirm any necessary adjustments on your end. Please let us know if any further actions are required.

`
			},
			{
				id: 'port-arrival',
				name: 'Port Arrival Notice',
				subject: 'Port Arrival Notice – MV *Suez Navigator*',
				content: `Dear [Port Agent's Name],

This is to inform you that MV *Suez Navigator* is scheduled to arrive at Port Said on **[ETA Date and Time]**.

**Voyage details**:
- Cargo: **[Cargo Details]**
- Draft: **[Draft]**
- LOA: **[Length Overall]**

Please arrange for the necessary port services and confirm receipt of this notice.

`
			}
		],
		snippets: [
			{
				id: 'vessel-details',
				name: 'Vessel Details',
				content: `**Vessel details**:
- IMO: 9876543
- Flag: Panama
- Year built: 2015
- DWT: 82,000 MT`
			},
			{
				id: 'cargo-details',
				name: 'Cargo Details',
				content: `**Cargo details**:
- Type: Iron Ore
- Quantity: 75,000 MT
- Stowage factor: 0.45 m3/MT`
			},
			{
				id: 'contact-info',
				name: 'Contact Information',
				content: `**For urgent matters, please contact**:
- Captain: +1 555-123-4567
- Chief Officer: +1 555-234-5678
- Company 24/7: +1 555-987-6543`
			}
		],
		signature: {
			content: `Best regards,  
[Your Name]  
[Vessel Operator's Name]  
[Company Name]`
		}
	}
};
