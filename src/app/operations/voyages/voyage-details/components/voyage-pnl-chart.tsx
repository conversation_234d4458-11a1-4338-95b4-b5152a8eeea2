import { Area, AreaChart } from 'recharts';
import { MoveUp } from 'lucide-react';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Separator } from '@/components/ui/separator';

const chartData = [
	{ day: 'Mon 17 Mar', actual: 43000, estimated: 32000 },
	{ day: 'Tue 18 Mar', actual: 54000, estimated: 32000 },
	{ day: 'Wed 19 Mar', actual: 22800, estimated: 32000 },
	{ day: 'Thu 20 Mar', actual: 47000, estimated: 32000 },
	{ day: 'Fri 21 Mar', actual: 36000, estimated: 32000 },
	{ day: 'Sat 22 Mar', actual: 53000, estimated: 32000 },
	{ day: 'Sun 23 Mar', actual: 59000, estimated: 32000 }
];

const chartConfig = {
	actual: {
		label: 'Actual',
		color: 'hsl(var(--chart-1))'
	},
	estimated: {
		label: 'Est.',
		color: 'hsl(var(--chart-3))'
	}
} satisfies ChartConfig;

interface VoyagePnlChartProps {
	className?: string;
}

export default function VoyagePnlChart({ className }: VoyagePnlChartProps) {
	return (
		<div className={`bg-panel rounded-xl border ${className}`}>
			<div className="flex items-center justify-between p-4">
				<div className="flex flex-col items-start gap-1">
					<div className="text-base leading-5 font-semibold">$324,700</div>
					<div className="text-muted-foreground flex w-full justify-start text-xs font-normal">
						Estimated P&L
					</div>
				</div>
				<Separator orientation="vertical" className="h-4" />
				<div className="flex flex-col items-center gap-1">
					<div className="text-base leading-5 font-semibold">$26,314</div>
					<div className="text-muted-foreground justify-start text-xs font-normal">Net TCE</div>
				</div>
				<Separator orientation="vertical" className="h-4" />
				<div className="flex flex-col items-end gap-1">
					<div className="flex items-center gap-1">
						<MoveUp className="size-3 text-red-500" />
						<div className="text-base leading-5 font-semibold">$476,280</div>
					</div>
					<div className="text-muted-foreground flex w-full justify-end text-xs font-normal">Actual P&L</div>
				</div>
			</div>
			<ChartContainer config={chartConfig} className="h-[80px] w-full">
				<AreaChart
					accessibilityLayer
					data={chartData}
					margin={{
						top: 8,
						left: 16,
						bottom: 8,
						right: 16
					}}
				>
					<ChartTooltip cursor={false} content={<ChartTooltipContent />} />
					<defs>
						<linearGradient id="fillPnl" x1="0" y1="0" x2="0" y2="1">
							<stop offset="5%" stopColor="var(--color-actual)" stopOpacity={0.6} />
							<stop offset="95%" stopColor="var(--color-actual)" stopOpacity={0} />
						</linearGradient>
					</defs>
					<Area
						dataKey="actual"
						type="step"
						fill="url(#fillPnl)"
						fillOpacity={0.4}
						stroke="var(--color-actual)"
						stackId="a"
						dot={{
							r: 1,
							fill: 'var(--color-actual)'
						}}
					/>
					<Area
						dataKey="estimated"
						type="linear"
						strokeWidth={0.5}
						strokeDasharray="1 3"
						fill="url(#fillPnl)"
						fillOpacity={0}
						stroke="var(--color-estimated)"
						stackId="b"
					/>
				</AreaChart>
			</ChartContainer>
		</div>
	);
}
