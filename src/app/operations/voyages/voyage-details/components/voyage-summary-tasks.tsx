import { Sparkles } from 'lucide-react';
import VoyageSummary from './voyage-summary';
import VoyageTasks from './voyage-tasks';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';

export default function VoyageSummaryTasks({ onOpenChatPanel }: { onOpenChatPanel?: () => void }) {
	const handleOpenChat = () => {
		if (onOpenChatPanel) {
			onOpenChatPanel();
		}
	};

	return (
		<div className="grid gap-6">
			<div className="bg-panel rounded-xl border">
				<VoyageSummary className="p-4" />
			</div>
			<div className="bg-panel rounded-xl border">
				<div className="flex h-full flex-col">
					<VoyageTasks className="flex-1 p-4" />
					<Separator className="bg-border/50" />
					<div className="flex items-center p-4">
						<Button size="xs" variant="ghost" onClick={handleOpenChat}>
							<Sparkles className="text-muted-foreground size-3" />
							<span className="text-muted-foreground">Ask Operator One</span>
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
