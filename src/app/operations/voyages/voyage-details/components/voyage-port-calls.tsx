import { MoveDown, MoveUp } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function VoyagePortCalls() {
	return (
		<div className="grid gap-2">
			<h3 className="px-2 text-base font-semibold">Port Calls</h3>
			<Table className="text-sm">
				<TableHeader>
					<TableRow>
						<TableHead className="text-xs">Port</TableHead>
						<TableHead className="text-xs">Arrived</TableHead>
						<TableHead className="text-xs">Depart</TableHead>
						<TableHead className="text-right text-xs">Consumption</TableHead>
						<TableHead className="text-right text-xs">Port Stay</TableHead>
						<TableHead className="text-right text-xs">Port Cost</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow>
						<TableCell>
							<div className="flex items-center gap-1">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Las Palmas, ES
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									L
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground text-xs">11 Apr - 09:00</TableCell>
						<TableCell className="text-muted-foreground text-xs">13 Apr - 12:00</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">75.00 mt</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">84.35 mt</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">2 days</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">2.4 days</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">$100,000</span>
								<span className="text-muted-foreground">/</span>
								<MoveUp className="size-3 text-red-500" />
								<span className="font-semibold">$112,000</span>
							</div>
						</TableCell>
					</TableRow>
					<TableRow>
						<TableCell>
							<div className="flex items-center gap-1">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Allapuzha, IN
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									D
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground text-xs">28 Apr - 08:00</TableCell>
						<TableCell className="text-muted-foreground text-xs">02 May - 08:00</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">75.00 mt</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">84.35 mt</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">4 days</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">4 days</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">$193,000</span>
								<span className="text-muted-foreground">/</span>
								<MoveDown className="size-3 text-emerald-500" />
								<span className="font-semibold">$180,000</span>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
