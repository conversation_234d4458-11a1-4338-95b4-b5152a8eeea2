import 'mapbox-gl/dist/mapbox-gl.css';

import { useState, useRef } from 'react';
import { useTheme } from 'next-themes';

import MapGL, { MapRef, Source, Layer } from 'react-map-gl/mapbox';
import {
	lightStyle,
	routeDataVoyage,
	portPointsVoyage,
	darkStyle,
	portVoyageLayer,
	routeVoyageLayer
} from '@/app/mapbox/data/data';

const mapboxAccessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;

interface ViewState {
	longitude: number;
	latitude: number;
	zoom: number;
}

interface VoyageMapProps {
	className?: string;
	zoom?: number;
}

export default function VoyageMap({ className, zoom }: VoyageMapProps) {
	const { theme } = useTheme();
	const mapRef = useRef<MapRef>(null);
	const defaultViewState = {
		longitude: 32.5,
		latitude: 25.0,
		zoom: zoom || 3
	};

	const [viewState, setViewState] = useState<ViewState>(defaultViewState);

	// Function to load custom icon for port markers
	const onMapLoad = () => {
		if (!mapRef.current) return;

		// Load a custom port icon image
		mapRef.current.loadImage('/static/media/pin.png', (error, image) => {
			if (error) throw error;

			// Add the image to the map style with the id referenced in portLayer
			if (image && mapRef.current && !mapRef.current.hasImage('port-icon')) {
				mapRef.current.addImage('port-icon', image);
			}
		});
	};

	return (
		<div className={`bg-card grid gap-4 overflow-hidden ${className}`}>
			<MapGL
				ref={mapRef}
				mapboxAccessToken={mapboxAccessToken}
				mapStyle={theme === 'light' ? lightStyle : darkStyle}
				onMove={evt => setViewState(evt.viewState)}
				{...viewState}
				projection="mercator"
				onLoad={onMapLoad}
			>
				<Source id="route-source" type="geojson" data={routeDataVoyage as GeoJSON.FeatureCollection}>
					<Layer {...routeVoyageLayer} />
				</Source>
				<Source id="port-points-source" type="geojson" data={portPointsVoyage as GeoJSON.FeatureCollection}>
					<Layer {...portVoyageLayer} />
				</Source>
			</MapGL>
		</div>
	);
}
