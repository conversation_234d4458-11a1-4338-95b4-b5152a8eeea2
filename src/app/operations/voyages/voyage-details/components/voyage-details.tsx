import { An<PERSON>, Box, Calendar, CircleDotDashed, DollarSign, Gauge, Hash, Ship, Text } from 'lucide-react';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';

export default function VoyageDetails() {
	return (
		<div className="grid gap-4">
			<div className="grid grid-cols-[auto_minmax(0px,1fr)_auto_minmax(0px,1fr)] items-center gap-x-8 gap-y-2 py-4 text-sm">
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Hash className="size-3.5" />
					Voyage ID
				</div>
				<div className="font-medium">#VOY-LA-01</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Text className="size-3.5" />
					Status
				</div>
				<div className="-mx-1">
					<Badge variant="outline" className="text-muted-foreground rounded-full text-xs">
						<CircleDotDashed className="size-3 text-emerald-500" />
						Commenced
					</Badge>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Ship className="size-3.5" />
					Vessel
				</div>
				<div className="font-semibold">mv Suez Navigator</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<DollarSign className="size-3.5" />
					Daily Cost / Addr
				</div>
				<div className="flex items-center gap-2">
					<div className="flex items-end gap-2">
						<div className="text-muted-foreground text-xs">USD</div>
						<div className="font-semibold">38,700.00</div>
					</div>
					<div className="text-muted-foreground">/</div>
					<div className="flex items-end gap-2">
						<div className="text-muted-foreground text-xs">USD</div>
						<div className="font-semibold">129,400.00</div>
					</div>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Gauge className="size-3.5" />
					Speed Ballast / Laden
				</div>
				<div className="flex items-center gap-2">
					<div className="flex items-end gap-2">
						<div className="font-semibold">12</div>
						<div className="text-muted-foreground text-xs">kn</div>
					</div>
					<div className="text-muted-foreground">/</div>
					<div className="flex items-end gap-2">
						<div className="font-semibold">14</div>
						<div className="text-muted-foreground text-xs">kn</div>
					</div>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<DollarSign className="size-3.5" />
					FO / MGO Price
				</div>
				<div className="flex items-center gap-2">
					<div className="flex items-end gap-2">
						<div className="text-muted-foreground text-xs">USD</div>
						<div className="font-semibold">65,800.00</div>
					</div>
					<div className="text-muted-foreground">/</div>
					<div className="flex items-end gap-2">
						<div className="text-muted-foreground text-xs">USD</div>
						<div className="font-semibold">62,300.00</div>
					</div>
				</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Anchor className="size-3.5" />
					Opening port
				</div>
				<div>Las Palmas, ES</div>
				<div className="text-muted-foreground flex h-7 items-center gap-2 text-sm">
					<Calendar className="size-3.5" />
					Start Date
				</div>
				<div>11 Apr 2025</div>
			</div>

			<Separator />

			<h2 className="px-1 text-base font-semibold">Cargo</h2>

			<Table>
				<TableHeader className="text-xs hover:bg-transparent">
					<TableRow>
						<TableHead className="font-normal">Cargo</TableHead>
						<TableHead className="font-normal">Quantity</TableHead>
						<TableHead className="font-normal">Laycan From</TableHead>
						<TableHead className="font-normal">Laycan To</TableHead>
						<TableHead className="font-normal">Freight</TableHead>
						<TableHead className="font-normal">Type</TableHead>
						<TableHead className="font-normal">Charterer</TableHead>
						<TableHead className="text-right font-normal">Commission</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow className="hover:bg-transparent">
						<TableCell className="px-1">
							<div className="flex items-center gap-1">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									<Box className="text-primary size-3.5" />
									Iron Ore
								</Button>
							</div>
						</TableCell>
						<TableCell>
							<div className="flex items-end gap-1">
								<div className="leading-none font-semibold">2,500.00</div>
								<div className="text-muted-foreground text-xs leading-none">mts</div>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground">11 Apr 25</TableCell>
						<TableCell className="text-muted-foreground">13 Apr 25</TableCell>
						<TableCell>
							<div className="flex items-end gap-1">
								<div className="leading-none">84</div>
								<div className="text-muted-foreground text-xs leading-none">pmt</div>
							</div>
						</TableCell>
						<TableCell>Lumpsum</TableCell>
						<TableCell>Global Chartering S.A.</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none">2.5</div>
								<div className="text-muted-foreground text-xs leading-none">%</div>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>

			<Separator className="bg-transparent" />

			<h2 className="px-1 text-base font-semibold">Ports</h2>
			<Table>
				<TableHeader className="text-xs hover:bg-transparent">
					<TableRow>
						<TableHead className="font-normal">Port</TableHead>
						<TableHead className="text-right font-normal">Port Cost</TableHead>
						<TableHead className="font-norma text-right">Distance</TableHead>
						<TableHead className="text-right font-normal">Days</TableHead>
						<TableHead className="text-right font-normal">Quantity</TableHead>
						<TableHead className="font-normal">Terms</TableHead>
						<TableHead className="text-right font-normal">Arrival</TableHead>
						<TableHead className="text-right font-normal">Departure</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow className="hover:bg-transparent">
						<TableCell>
							<div className="flex items-center gap-1">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Las Palmas, ES
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									L
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="text-muted-foreground text-xs leading-none">USD</div>
								<div className="leading-none font-semibold">82,300</div>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none font-semibold">3,600</div>
								<div className="text-muted-foreground text-xs leading-none">nm</div>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none font-semibold">11</div>
								<div className="text-muted-foreground text-xs leading-none">days</div>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none font-semibold">5,000</div>
								<div className="text-muted-foreground text-xs leading-none">mts</div>
							</div>
						</TableCell>
						<TableCell className="">SSHEX</TableCell>
						<TableCell className="text-muted-foreground text-right">11 Apr 25</TableCell>
						<TableCell className="text-muted-foreground text-right">27 Apr 25</TableCell>
					</TableRow>
					<TableRow className="hover:bg-transparent">
						<TableCell>
							<div className="flex items-center gap-1">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Alappuzha, IN
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									D
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="text-muted-foreground text-xs leading-none">USD</div>
								<div className="leading-none font-semibold">82,300</div>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none font-semibold">3,600</div>
								<div className="text-muted-foreground text-xs leading-none">nm</div>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none font-semibold">11</div>
								<div className="text-muted-foreground text-xs leading-none">days</div>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-end justify-end gap-1">
								<div className="leading-none font-semibold">5,000</div>
								<div className="text-muted-foreground text-xs leading-none">mts</div>
							</div>
						</TableCell>
						<TableCell className="">SSHEX</TableCell>
						<TableCell className="text-muted-foreground text-right">11 Apr 25</TableCell>
						<TableCell className="text-muted-foreground text-right">27 Apr 25</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
