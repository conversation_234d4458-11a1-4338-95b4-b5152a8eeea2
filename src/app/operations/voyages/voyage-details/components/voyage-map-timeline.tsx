import { Box, MoveRight } from 'lucide-react';
import VoyageMap from './voyage-map';
import VoyageItineraryTimeline from './voyage-itinerary-timeline';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface VoyageMapTimelineProps {
	onOpenItineraryTab?: () => void;
}

export default function VoyageMapTimeline({ onOpenItineraryTab }: VoyageMapTimelineProps) {
	return (
		<div className="bg-panel rounded-xl border">
			<VoyageMap className="-m-[1px] h-[180px] rounded-t-2xl" zoom={1} />
			<div className="flex items-center justify-between p-4">
				<div className="text-base font-semibold">mv Suez Navigator</div>
				<Badge variant="secondary" className="rounded-full text-xs">
					<Box className="text-primary size-3" />
					<span className="text-muted-foreground">35,000mts - Iron Ore</span>
				</Badge>
			</div>
			<Separator className="bg-border/50" />
			<VoyageItineraryTimeline />
			<Separator className="bg-border/50" />
			<div className="flex items-center p-4">
				<Button size="xs" variant="ghost" onClick={onOpenItineraryTab}>
					<span className="text-muted-foreground">View Full Itinerary</span>
					<MoveRight className="text-muted-foreground size-3" />
				</Button>
			</div>
		</div>
	);
}
