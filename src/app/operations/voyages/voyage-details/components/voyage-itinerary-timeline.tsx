import { Navigation2, Radio, CircleCheck, Circle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function VoyageItineraryTimeline() {
	return (
		<div className="relative z-10 grid gap-6 p-4">
			<div className="sep absolute top-8 bottom-16 left-6 -z-10 w-[1px] bg-slate-700"></div>
			<div className="flex gap-2">
				<div>
					<div className="bg-card py-1.5">
						<CircleCheck className="text-primary size-4" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-1">
						<Button variant="ghost" className="h-auto p-1">
							Las Palmas, ES
						</Button>
						<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
							L
						</Badge>
					</div>
					<div className="grid grid-cols-[40px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-xs">
						<div className="text-muted-foreground">Arrived:</div>
						<div>11 Apr, 08:00</div>
						<div className="text-muted-foreground col-span-2 text-right">318 mts / 195 mts</div>

						<div className="text-muted-foreground">Departed:</div>
						<div>13 Apr, 06:00</div>
						<div className="text-muted-foreground col-span-2 text-right">318 mts / 195 mts</div>
					</div>
				</div>
			</div>
			<div className="flex gap-2">
				<div>
					<div className="bg-card py-1.5">
						<Navigation2 className="size-4 rotate-180 text-amber-500" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-2">
						<div className="px-1 text-sm">En route</div>
					</div>
					<div className="text-muted-foreground grid grid-cols-[40px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-xs">
						<div className="col-span-2 flex items-center gap-1">
							<Radio className="size-3" />
							<span>219°</span>
							<span>&nbsp;•&nbsp;</span>
							<span>10.4 kn</span>
							<span>&nbsp;•&nbsp;</span>
							<span>7.9m</span>
						</div>
						<div className="col-span-2 text-right">220 mts / 163 mts</div>
					</div>
				</div>
			</div>
			<div className="flex gap-2">
				<div>
					<div className="bg-card py-1.5">
						<Circle className="text-muted-foreground size-4" />
					</div>
				</div>
				<div className="grid w-full gap-2">
					<div className="flex items-center gap-1">
						<Button variant="ghost" className="h-auto p-1">
							Alappuzha, IN
						</Button>
						<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
							D
						</Badge>
					</div>
					<div className="grid grid-cols-[40px_minmax(0px,_1fr)_auto_minmax(0px,_1fr)] gap-x-12 gap-y-2 px-1 py-1 text-xs">
						<div className="text-muted-foreground">ETA:</div>
						<div>28 Apr, 12:00</div>
						<div className="text-muted-foreground col-span-2 text-right">--- mts / --- mts</div>
					</div>
				</div>
			</div>
		</div>
	);
}
