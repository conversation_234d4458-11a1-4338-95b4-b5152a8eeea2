import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { MoreHorizontal, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface Props {
	onOpenAIpanel?: () => void;
	onOpenChatPanel?: (useDefault?: boolean) => void;
}

export default function VoyageSummary2({ onOpenAIpanel }: Props) {
	const [expanded, setExpanded] = useState(false);
	return (
		<div className="grid gap-2 px-4">
			<div className="hidden">
				<Button
					variant="ghost"
					className="text-muted-foreground h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Voyage Summary
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<div className="grid gap-4 p-2 leading-6">
				<div className={`text-sm leading-6 ${expanded ? '' : 'a-mask-image'}`}>
					<div>
						<p>
							mv Suez Navigator departed Las Palmas, ES, on 11 April at 08:00, loaded 32,000 mts of{' '}
							<strong>Iron Ore</strong> at Port Said, EG, on 17 April at 10:00, and is currently en route
							to Alappuzha, IN, with an <strong>ETA of 28 April at 08:00</strong>.
						</p>
						<p className="py-2">
							Covering <strong>5,023 nm over 17 days</strong>, the voyage is on schedule with a current
							profit of <strong>USD 476,280</strong>.
						</p>
					</div>
					<div className="hidden">
						<ul>
							<li>Commencement Port: Las Palmas, ES</li>
							<li>Via Port (Loading): Port Said, EG</li>
							<li>Destination Port (Discharge): Alappuzha, IN</li>
							<li>Cargo: 32,000 metric tons of Iron Ore</li>
							<li>Total Distance: 5,023 nm</li>
							<li>Total Duration: 17 days and 3 hours</li>
							<li>Current PnL: USD 476,280</li>
						</ul>
					</div>
					<TooltipProvider>
						<div className="hidden">
							<p>
								Delayed by 3 hours due to adverse{' '}
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="text-link cursor-help underline decoration-dotted">
											weather conditions.
										</span>
									</TooltipTrigger>
									<TooltipContent>
										<div className="grid gap-2 p-1 leading-5">
											<div className="grid">
												<p>Fm: The Master of the vessel </p>
												<p>To: Agents B&M Agencia Maritima S.A.</p>
												<p>Subject : MV Abtenauer - calling Tocopilla for loading</p>
											</div>
											<p>[Email Body]</p>
										</div>
									</TooltipContent>
								</Tooltip>
							</p>
							<p>
								Additionally, a PDA document is{' '}
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="text-link cursor-help underline decoration-dotted">
											pending your approval.
										</span>
									</TooltipTrigger>
									<TooltipContent>[Email Body]</TooltipContent>
								</Tooltip>
							</p>
							<p>
								Higher fuel{' '}
								<Tooltip>
									<TooltipTrigger asChild>
										<span className="text-link cursor-help underline decoration-dotted">
											consumption of 3.1 mts.
										</span>
									</TooltipTrigger>
									<TooltipContent>[Email Body]</TooltipContent>
								</Tooltip>
							</p>
						</div>
					</TooltipProvider>
					<AnimatePresence>
						{expanded && (
							<motion.div
								initial={{ height: 0, overflow: 'hidden' }}
								animate={{ height: 'auto', overflow: 'visible' }}
								exit={{ height: 0, overflow: 'hidden' }}
								transition={{ duration: 0.15 }}
							>
								<div className="mt-4 grid gap-4">
									<div className="text-xs font-semibold">Suggestions</div>
									<div className="flex flex-wrap gap-2">
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Check weather forecast
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Approve PDA
											<span className="text-muted-foreground text-xs">(#DA-12345)</span>
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Inform the port agent about the delay
										</Button>
										<Button
											variant="outline"
											className="hover:bg-accent/50 width-auto h-auto justify-start rounded-full border py-1.5 font-normal"
											onClick={onOpenAIpanel}
										>
											Check fuel consumption
										</Button>
									</div>
								</div>
							</motion.div>
						)}
					</AnimatePresence>
				</div>
				<div className="flex justify-center">
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									size="xs"
									variant="ghost"
									className="h-6 w-6 p-1"
									onClick={() => {
										setExpanded(prev => !prev);
									}}
								>
									<MoreHorizontal className="text-muted-foreground" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>View {expanded ? 'Less' : 'More'}</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
			</div>
		</div>
	);
}
