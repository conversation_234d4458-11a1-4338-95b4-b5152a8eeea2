import { Navigation2 } from 'lucide-react';
import VoyageMap from './voyage-map';
import VoyageSummary from './voyage-summary';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';

interface VoyageRouteMapProps {
	className?: string;
}

export default function VoyageRouteMap({ className }: VoyageRouteMapProps) {
	return (
		<div className={`bg-card flex flex-col rounded-xl border ${className}`}>
			<div className="flex w-full items-center p-2 font-normal">
				<Button variant="ghost" className="h-auto flex-col items-start gap-1 rounded-xl bg-transparent p-2">
					<div className="text-sm leading-5 font-semibold">Las Palmas, ES</div>
					<div className="text-muted-foreground text-xs font-normal">11 Apr, 08:00</div>
				</Button>
				<Separator orientation="horizontal" className="flex-1" />
				<Button variant="ghost" className="h-auto rounded-xl p-1.5">
					<Navigation2 className="size-3 rotate-90 text-amber-500" />
				</Button>
				<Separator orientation="horizontal" className="flex-1" />
				<Button variant="ghost" className="h-auto flex-col items-end gap-1 rounded-xl bg-transparent p-2">
					<div className="text-sm leading-5 font-semibold">Alappuzha, IN</div>
					<div className="text-muted-foreground text-xs font-normal">28 Apr, 12:00</div>
				</Button>
			</div>

			<VoyageMap className="border/50 -mx-[1px] min-h-[200px] flex-1" zoom={2} />

			<div className="flex items-center gap-4 p-4">
				<div className="flex flex-1 flex-col items-start">
					<div className="text-base leading-5 font-semibold">17d : 3h</div>
					<div className="text-muted-foreground text-xs font-normal">Duration</div>
				</div>
				<Separator orientation="vertical" className="h-4" />
				<div className="flex flex-1 flex-col items-center">
					<div className="text-base leading-5 font-semibold">5,023 nm</div>
					<div className="text-muted-foreground text-xs font-normal">Distance</div>
				</div>
				<Separator orientation="vertical" className="h-4" />
				<div className="flex flex-1 flex-col items-center">
					<div className="text-base leading-5 font-semibold">482 nm</div>
					<div className="text-muted-foreground text-xs font-normal">(S)ECA</div>
				</div>
				<Separator orientation="vertical" className="h-4" />
				<div className="flex flex-1 flex-col items-center">
					<div className="text-base leading-5 font-semibold">76.35 mt</div>
					<div className="text-muted-foreground text-xs font-normal">Consumption</div>
				</div>
			</div>
			<Separator className="bg-border/50" />
			<VoyageSummary hasTitle={false} className="p-4" />
		</div>
	);
}
