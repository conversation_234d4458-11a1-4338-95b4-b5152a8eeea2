import { useState } from 'react';
import { File, Mail, MoreHorizontal, MoveLeft, PanelLeft, Trash2, Upload } from 'lucide-react';
import { AnimatePresence, motion } from 'framer-motion';
import { data } from '../data/data';
import VoyageAlerts from './voyage-alerts';
import VoyagePending from './voyage-pending';
import { Button } from '@/components/ui/button';
import { DrawerContent, DrawerHeader, DrawerTitle } from '@/components/ui/drawer';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Chat from '@/app/mapbox/components/chat/chat';
import { Separator } from '@/components/ui/separator';

const docs = [
	{
		id: '1',
		name: 'Charter party.pdf',
		type: 'pdf'
	},
	{
		id: '2',
		name: 'Loadport Docs.pdf',
		type: 'pdf'
	},
	{
		id: '3',
		name: 'PDA Rotterdam.pdf',
		type: 'pdf'
	},
	{
		id: '4',
		name: 'PDA Port Said.pdf',
		type: 'pdf'
	},
	{
		id: '5',
		name: 'Recap.pdf',
		type: 'pdf'
	},
	{
		id: '6',
		name: 'SOF & NOR.pdf',
		type: 'pdf'
	},
	{
		id: '7',
		name: 'Vertom Meridiaan NOR.pdf',
		type: 'pdf'
	},
	{
		id: '8',
		name: 'Vertom Meridiaan SOF.pdf',
		type: 'pdf'
	}
];

const emails = [
	{
		id: '1',
		name: 'Delay Notification',
		date: '3 hrs ago',
		type: 'email'
	},
	{
		id: '2',
		name: 'Port Arrival Notice',
		date: '07 May - 10:53',
		type: 'email'
	},
	{
		id: '3',
		name: 'Higher IFO consumption',
		date: '05 May - 16:24',
		type: 'email'
	}
];

export default function VoyageChatDrawer() {
	const [expanded, setExpanded] = useState(false);
	const [showEmail, setShowEmail] = useState(false);

	return (
		<DrawerContent className="h-[95%] data-[vaul-drawer-direction=bottom]:max-h-[95%]">
			<DrawerHeader className="hidden">
				<DrawerTitle>Chat</DrawerTitle>
			</DrawerHeader>
			<div className="flex h-full gap-6 px-6">
				{showEmail ? (
					<div className="max-w-[580px] flex-1/3 pb-4">
						<div className="bg-panel flex h-full flex-col rounded-xl border">
							<div className="flex items-center gap-2 border-b px-4 py-4 font-medium">
								<Button
									variant="ghost"
									size="icon"
									className="text-muted-foreground h-7 w-7"
									onClick={() => setShowEmail(false)}
								>
									<MoveLeft />
								</Button>
								Port Arrival Notice
							</div>
							<div className="flex-1 overflow-x-hidden overflow-y-auto">
								<AnimatePresence>
									<motion.div
										initial={{ x: 20, overflow: 'hidden', opacity: 0 }}
										animate={{ x: 0, overflow: 'visible', opacity: 1 }}
										exit={{ x: 20, overflow: 'hidden', opacity: 0 }}
										transition={{ duration: 0.15 }}
									>
										<div className="ai-markdown p-6 text-base/6 text-[14px]">
											<div className="grid gap-2">
												<p>
													From: Koletzki [mailto:<EMAIL>]
													<br />
													Sent: Thursday, November 19, 2015 6:14 PM
													<br />
													To: Agents B&M Agencia Maritima S.A.
												</p>
												<p>Doc-No. 4139724 19/NOV/2015 (THU) 18:14 (+0100) FA</p>
												<p>Mark / Frank</p>
												<p>Confirm having fully fixed with all subjects in order as follows:</p>
												<p>C/P dated 19th November 2015</p>
												<p>
													<strong>MV ABTENAUER</strong>
												</p>
												<p>
													Described as attached but delete `&apos;wog`&apos; - `&apos;calm
													seas`&apos; and the last paragraph - `&apos;all details about`&apos;
													to remain.
												</p>
												<p>
													FOR ACCOUNT: OPERATOR
													<br />
													HEAD OWNERS : Stallion Seven Shipping Co.S.A., Majuro, The Marshall
													Islands
													<br />
													DISPONENT OWNERS : Sea Stallion Pool Inc. Majuro, The Marshall
													Islands
													<br />
													BANK DETAILS: Sea Stallion Pool Inc.
													<br /> c/o Aug. Bolten Wm. Miller`&apos;s Nachfolger GmbH & Co.KG
													Mattentwiete 8 20457
													<br /> Hamburg
													<br /> GERMANY
												</p>
											</div>
										</div>
									</motion.div>
								</AnimatePresence>
							</div>
						</div>
					</div>
				) : (
					<div className="max-w-[400px] flex-1/4 pb-4">
						<div className="bg-panel flex h-full flex-col rounded-xl border">
							<div className="flex items-center justify-between gap-2 border-b px-6 py-4 font-medium">
								Sources
								<div className="flex items-center justify-end gap-2">
									<TooltipProvider delayDuration={100}>
										<Tooltip>
											<TooltipTrigger asChild>
												<Button
													variant="ghost"
													size="icon"
													className="text-muted-foreground h-7 w-7"
												>
													<PanelLeft />
												</Button>
											</TooltipTrigger>
											<TooltipContent>Collapse</TooltipContent>
										</Tooltip>
									</TooltipProvider>
								</div>
							</div>
							<div className="flex-1 overflow-auto">
								<div className="flex flex-col gap-2 p-4">
									<div className="text-muted-foreground p-2 text-xs">Documents</div>
									{docs.map(item => (
										<Button
											key={item.id}
											variant="ghost"
											className="group w-full justify-start px-2"
										>
											<File className="text-primary" />
											<span className="flex-1 text-left">{item.name}</span>
											<div className="flex items-center justify-end gap-3 opacity-0 group-hover:opacity-100">
												<MoreHorizontal className="text-muted-foreground" />
												<Trash2 className="text-muted-foreground" />
											</div>
										</Button>
									))}
									<div className="py-4">
										<Button variant="outline" className="rounded-full">
											<Upload />
											Upload document
										</Button>
									</div>
								</div>
								<Separator />
								<div className="flex flex-col gap-2 p-4">
									<div className="text-muted-foreground p-2 text-xs">Emails</div>
									{emails.map(item => (
										<Button
											key={item.id}
											variant="ghost"
											className="group w-full justify-start px-2"
											onClick={() => setShowEmail(true)}
										>
											<Mail className="text-muted-foreground" />
											<span className="flex-1 text-left">{item.name}</span>
											<span className="text-muted-foreground text-xs font-normal">
												{item.date}
											</span>
										</Button>
									))}
								</div>
							</div>
						</div>
					</div>
				)}
				<div className="flex-1/2">
					<Chat data={data} backButton={false} chatTitle="mv Suez Navigator" />
					<div className="absolute top-8 right-8">
						<Button
							variant="ghost"
							size="xs"
							className="text-muted-foreground"
							onClick={() => setExpanded(prev => !prev)}
						>
							Issues & Tasks
						</Button>
					</div>
				</div>
				<AnimatePresence>
					{expanded && (
						<motion.div
							initial={{ x: 100, overflow: 'hidden', opacity: 0 }}
							animate={{ x: 0, overflow: 'visible', opacity: 1 }}
							exit={{ x: 100, overflow: 'hidden', opacity: 0 }}
							transition={{ duration: 0.15 }}
							className="max-w-[400px] flex-1/4 py-4 pl-4"
						>
							<div className="flex h-full flex-col gap-4">
								<VoyageAlerts />
								<Separator className="bg-transparent" />
								<VoyagePending />
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</DrawerContent>
	);
}
