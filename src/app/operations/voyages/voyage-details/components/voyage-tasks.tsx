import { Circle, CircleCheck } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const tasks = [
	{
		id: '1',
		status: 'todo',
		task: 'Check PDA is paid',
		type: 'task'
	},
	{
		id: '2',
		status: 'todo',
		task: 'Check original BsL',
		type: 'task'
	},
	{
		id: '3',
		status: 'todo',
		task: 'Daily update to the Charterers',
		type: 'task'
	},
	{
		id: '4',
		status: 'todo',
		task: 'Arrange off-hire bunker',
		type: 'task'
	},
	{
		id: '5',
		status: 'todo',
		task: 'Inform the port agent about the delay',
		type: 'task'
	},
	{
		id: '6',
		status: 'todo',
		task: 'Approve PDA (#DA-123LA)',
		type: 'task'
	}
];

interface VoyageTasksProps {
	className?: string;
}

export default function VoyageTasks({ className }: VoyageTasksProps) {
	return (
		<div className={cn('flex flex-col gap-2', className)}>
			<h3 className="text-base font-semibold">Tasks</h3>
			<div className="-mx-2 grid">
				{tasks.map(item => (
					<Button key={item.id} variant="ghost" className="group h-auto justify-start bg-transparent px-2">
						{item.status === 'done' ? (
							<CircleCheck className="text-background fill-primary" />
						) : (
							<Circle className="text-muted-foreground" />
						)}
						<div
							className={cn(
								`flex-1 text-left font-normal text-wrap`,
								item.status === 'done' ? 'line-through' : ''
							)}
						>
							{item.task}
						</div>
					</Button>
				))}
			</div>
		</div>
	);
}
