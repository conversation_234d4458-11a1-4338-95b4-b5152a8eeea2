import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface VoyageSummaryProps {
	className?: string;
	hasTitle?: boolean;
}

export default function VoyageSummary({ className, hasTitle = true }: VoyageSummaryProps) {
	return (
		<div className={`grid gap-2 ${className}`}>
			{hasTitle && <h3 className="text-base font-semibold">Summary</h3>}
			<div className="text-sm leading-6">
				<TooltipProvider>
					<div>
						<p>
							Delayed by 3 hours due to adverse{' '}
							<Tooltip>
								<TooltipTrigger asChild>
									<span className="text-link cursor-help underline decoration-dotted">
										weather conditions.
									</span>
								</TooltipTrigger>
								<TooltipContent>
									<div className="grid gap-2 p-1 leading-5">
										<div className="grid">
											<p>Fm: The Master of the vessel </p>
											<p>To: Agents B&M Agencia Maritima S.A.</p>
											<p>Subject : MV Abtenauer - calling <PERSON><PERSON>pi<PERSON> for loading</p>
										</div>
										<p>[Email Body]</p>
									</div>
								</TooltipContent>
							</Tooltip>
						</p>
						<p>
							Additionally, a PDA document is{' '}
							<Tooltip>
								<TooltipTrigger asChild>
									<span className="text-link cursor-help underline decoration-dotted">
										pending your approval.
									</span>
								</TooltipTrigger>
								<TooltipContent>[Email Body]</TooltipContent>
							</Tooltip>
						</p>
						<p>
							Higher fuel{' '}
							<Tooltip>
								<TooltipTrigger asChild>
									<span className="text-link cursor-help underline decoration-dotted">
										consumption of 3.1 mts.
									</span>
								</TooltipTrigger>
								<TooltipContent>[Email Body]</TooltipContent>
							</Tooltip>
						</p>
					</div>
				</TooltipProvider>
			</div>
		</div>
	);
}
