import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Dash<PERSON>, <PERSON>Dot, MoveDown, MoveUp } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function VoyageItineraryFull() {
	return (
		<div className="grid gap-4">
			<div className="m-auto hidden w-full max-w-4xl">
				<h3 className="px-2 text-base font-semibold">Itinerary</h3>
			</div>
			<Table className="text-sm">
				<TableHeader>
					<TableRow className="border-t">
						<TableHead className="text-xs text-nowrap">Status</TableHead>
						<TableHead className="text-xs text-nowrap">Port</TableHead>
						<TableHead className="border-r text-xs text-nowrap">Draft</TableHead>
						<TableHead className="border-r text-right text-xs text-nowrap">Miles</TableHead>
						<TableHead className="border-r text-xs text-nowrap">Arrival</TableHead>
						<TableHead className="border-r text-xs text-nowrap">VLSFO / LSMGO</TableHead>
						<TableHead className="border-r text-xs text-nowrap">Berthing</TableHead>
						<TableHead className="border-r text-xs text-nowrap">VLSFO / LSMGO</TableHead>
						<TableHead className="border-r text-xs text-nowrap">Departure</TableHead>
						<TableHead className="border-r text-xs text-nowrap">VLSFO / LSMGO</TableHead>
						<TableHead className="border-r text-right text-xs text-nowrap">Laytime</TableHead>
						<TableHead className="border-r text-right text-xs text-nowrap">P&L Time</TableHead>
						<TableHead className="text-right text-xs text-nowrap">P&L Cost</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody className="border-b">
					<TableRow>
						<TableCell>
							<Badge variant="outline" className="text-muted-foreground rounded-full">
								<CircleCheck className="text-primary size-3" />
								Sailed
							</Badge>
						</TableCell>
						<TableCell>
							<div className="flex items-center gap-1">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Callao, PE
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									COMM.
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">Ballast</div>
						</TableCell>
						<TableCell className="border-r text-right">0</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">14 Mar - 06:45</div>
						</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">450 / 120</div>
						</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">14 Mar - 06:45</div>
						</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">450 / 120</div>
						</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">14 Mar - 06:45</div>
						</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">450 / 120</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="text-muted-foreground text-xs">---</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="text-muted-foreground text-xs">---</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="text-muted-foreground text-xs">---</div>
						</TableCell>
					</TableRow>

					<TableRow>
						<TableCell>
							<Badge variant="outline" className="text-muted-foreground rounded-full">
								<CircleDot className="size-3 text-emerald-500" />
								Arrived
							</Badge>
						</TableCell>
						<TableCell>
							<div className="flex items-center gap-1 text-nowrap">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Las Palmas, ES
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									L
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">Laden</div>
						</TableCell>
						<TableCell className="border-r text-right">733</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">14 Mar - 06:45</div>
						</TableCell>
						<TableCell className="border-r">
							<div className="text-nowrap">450 / 120</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">24 Mar - 08:00</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">420 / 120</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">27 Mar - 16:00</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">410 / 120</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="flex items-center gap-2 justify-self-end font-medium text-nowrap">
								$132,000
								<Badge variant="outline" className="w-9 border-red-500 px-1 py-0.5">
									DEM
								</Badge>
							</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="flex items-center justify-end gap-1 text-nowrap">
								<span className="text-muted-foreground text-xs">4 d</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">4 d</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1 text-nowrap">
								<span className="text-muted-foreground text-xs">$100,000</span>
								<span className="text-muted-foreground">/</span>
								<MoveUp className="size-3 text-red-500" />
								<span className="font-semibold">$112,000</span>
							</div>
						</TableCell>
					</TableRow>

					<TableRow>
						<TableCell>
							<Badge variant="outline" className="text-muted-foreground rounded-full">
								<CircleDashed className="size-3" />
								Planned
							</Badge>
						</TableCell>
						<TableCell>
							<div className="flex items-center gap-1 text-nowrap">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Port Said, EG
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									B
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">Laden</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r text-right">
							<div className="text-nowrap">1,401</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">30 Mar - 01:00</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">380 / 120</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">30 Mar - 01:00</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">380 / 120</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">30 Mar - 21:30</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">740 / 120</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="text-muted-foreground text-xs">---</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="flex items-center justify-end gap-1 text-nowrap">
								<span className="text-muted-foreground text-xs">0.5 d</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">0.8 d</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1 text-nowrap">
								<span className="text-muted-foreground text-xs">$8,000</span>
								<span className="text-muted-foreground">/</span>
								<MoveDown className="size-3 text-emerald-500" />
								<span className="font-semibold">$5,000</span>
							</div>
						</TableCell>
					</TableRow>

					<TableRow>
						<TableCell>
							<Badge variant="outline" className="text-muted-foreground rounded-full">
								<CircleDashed className="size-3" />
								Planned
							</Badge>
						</TableCell>
						<TableCell>
							<div className="flex items-center gap-1 text-nowrap">
								<Button variant="ghost" className="h-auto p-1 font-semibold">
									Alappuzha, IN
								</Button>
								<Badge variant="secondary" className="text-2xs px-1.5 py-0 font-semibold">
									D
								</Badge>
							</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">Laden</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r text-right">
							<div className="text-nowrap">7,678</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">22 Apr - 09:30</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">375 / 120</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">23 Apr - 09:30</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">365 / 120</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">28 Apr - 22:00</div>
						</TableCell>
						<TableCell className="text-muted-foreground border-r">
							<div className="text-nowrap">355 / 120</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="flex items-center gap-2 justify-self-end font-medium text-nowrap">
								$21,000
								<Badge variant="outline" className="w-9 border-red-500 px-1 py-0.5">
									DEM
								</Badge>
							</div>
						</TableCell>
						<TableCell className="border-r text-right">
							<div className="flex items-center justify-end gap-1 text-nowrap">
								<span className="text-muted-foreground text-xs">3.5 d</span>
								<span className="text-muted-foreground">/</span>
								<span className="font-semibold">7.5 d</span>
							</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="text-muted-foreground text-xs">---</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
