import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import VoyageHeader from './voyage-header';
import VoyageGeneral from './voyage-general';
import VoyagePortCalls from './voyage-port-calls';
import VoyageCargoes from './voyage-cargoes';
import VoyageMap from './voyage-map';
import VoyageDetails from './voyage-details';
import VoyagePnlCost from './voyage-pnl-cost';
import VoyageItineraryMvp from './voyage-itinerary-mvp';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

const tabClassName =
	'data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none';

export default function Voyage() {
	const [activeTab, setActiveTab] = useState('itinerary');
	return (
		<AnimatePresence>
			<motion.div
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				exit={{ opacity: 0 }}
				transition={{ duration: 0.2 }}
				className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4"
			>
				<div className="flex w-full flex-col items-center gap-4">
					<VoyageHeader className="w-full max-w-4xl" />
					<Tabs value={activeTab} className="flex w-full flex-col items-center" onValueChange={setActiveTab}>
						<TabsList className="m-auto h-auto w-full max-w-4xl justify-start gap-4 rounded-none border-b bg-transparent p-0">
							<TabsTrigger value="overview" className={cn(tabClassName, 'hidden')}>
								Overview
							</TabsTrigger>
							<TabsTrigger value="itinerary" className={tabClassName}>
								Itinerary
							</TabsTrigger>
							<TabsTrigger value="voyage" className={tabClassName}>
								Voyage
							</TabsTrigger>
							<TabsTrigger value="docs" className={tabClassName}>
								Documents
							</TabsTrigger>
							<TabsTrigger value="issues" className={cn(tabClassName, 'gap-1')}>
								Issues
								<Badge variant="destructive" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
									3
								</Badge>
							</TabsTrigger>
						</TabsList>
						<TabsContent value="overview" className="m-0 hidden w-full">
							<VoyageGeneral />
						</TabsContent>
						<TabsContent value="itinerary" className="m-0 w-full">
							<div className="grid gap-0">
								<VoyageMap className="h-[350px] w-full border-none" zoom={2.5} />
								<VoyageItineraryMvp />
							</div>
						</TabsContent>
						<TabsContent value="voyage" className="w-full max-w-4xl">
							<div className="grid gap-4">
								<VoyageDetails />
								<Separator className="bg-transparent" />
								<VoyagePortCalls />
								<Separator className="bg-transparent" />
								<VoyageCargoes />
							</div>
						</TabsContent>
						<TabsContent value="docs">Docs</TabsContent>
						<TabsContent value="issues">Issues</TabsContent>
						<TabsContent value="pnl" className="w-full max-w-4xl">
							<div className="grid gap-4">
								<VoyagePnlCost />
							</div>
						</TabsContent>
						<TabsContent value="insights">Insights</TabsContent>
						<TabsContent value="activity">Activity</TabsContent>
					</Tabs>
				</div>
			</motion.div>
		</AnimatePresence>
	);
}
