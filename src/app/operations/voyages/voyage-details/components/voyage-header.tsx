import { CircleDashed } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface VoyageHeaderProps {
	className?: string;
}

export default function VoyageHeader({ className }: VoyageHeaderProps) {
	return (
		<div className={`flex items-center gap-4 ${className}`}>
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">mv Suez Navigator</h2>
				<div className="text-muted-foreground">/</div>
				<div className="text-muted-foreground text-sm font-medium">#VOY-LA-01</div>
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					<CircleDashed className="size-3 text-emerald-500" />
					<span>Commenced</span>
				</Badge>
			</div>
		</div>
	);
}
