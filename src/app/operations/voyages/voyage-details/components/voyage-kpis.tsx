import { MoveUp } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface VoyageKpisProps {
	className?: string;
}

export default function VoyageKpis({ className }: VoyageKpisProps) {
	return (
		<div className={`grid grid-cols-2 gap-6 ${className}`}>
			<div className="bg-panel rounded-xl border">
				<div className="flex items-center justify-between p-4">
					<div className="flex flex-col items-start gap-1">
						<div className="text-base leading-5 font-semibold">17d : 3h</div>
						<div className="text-muted-foreground flex w-full justify-start text-xs font-normal">
							Duration
						</div>
					</div>
					<Separator orientation="vertical" className="h-4" />
					<div className="flex flex-col items-center gap-1">
						<div className="text-base leading-5 font-semibold">5,023 nm</div>
						<div className="text-muted-foreground justify-start text-xs font-normal">Distance</div>
					</div>
					<Separator orientation="vertical" className="h-4" />
					<div className="flex flex-col items-center gap-1">
						<div className="text-base leading-5 font-semibold">482 nm</div>
						<div className="text-muted-foreground justify-start text-xs font-normal">(S)ECA</div>
					</div>
					<Separator orientation="vertical" className="h-4" />
					<div className="flex flex-col items-end gap-1">
						<div className="flex items-center gap-1">
							<div className="text-base leading-5 font-semibold">76.35 mt</div>
						</div>
						<div className="text-muted-foreground flex w-full justify-end text-xs font-normal">
							Consumption
						</div>
					</div>
				</div>
			</div>
			<div className="bg-panel rounded-xl border">
				<div className="flex items-center justify-between p-4">
					<div className="flex flex-col items-start gap-1">
						<div className="text-base leading-5 font-semibold">$26,314</div>
						<div className="text-muted-foreground flex w-full justify-start text-xs font-normal">
							Net TCE
						</div>
					</div>
					<Separator orientation="vertical" className="h-4" />
					<div className="flex flex-col items-center gap-1">
						<div className="text-base leading-5 font-semibold">$324,700</div>
						<div className="text-muted-foreground justify-start text-xs font-normal">Estimated P&L</div>
					</div>
					<Separator orientation="vertical" className="h-4" />
					<div className="flex flex-col items-end gap-1">
						<div className="flex items-center gap-1">
							<MoveUp className="size-3 text-red-500" />
							<div className="text-base leading-5 font-semibold">$476,280</div>
						</div>
						<div className="text-muted-foreground flex w-full justify-end text-xs font-normal">
							Actual P&L
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
