import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, ChevronRight, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

const alerts = [
	{
		id: '1',
		alert: 'Approve PDA (#DA-VA-231)',
		type: 'alert'
	}
	// {
	// 	id: '2',
	// 	alert: 'Higher fuel consumption with 3.1 mts',
	// 	type: 'alert'
	// }
];

interface Props {
	onOpenAIpanel?: () => void;
}

export default function VoyagePending({ onOpenAIpanel }: Props) {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="flex flex-col gap-2 px-4">
			<div className="text-muted-foreground text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Pending Tasks
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-2">
							{alerts.map(item => (
								<Button
									key={item.id}
									variant="outline"
									className="group h-auto justify-start px-1"
									onClick={onOpenAIpanel}
								>
									<Clock className="text-muted-foreground mx-1" />
									<div className="flex-1 text-left font-normal text-wrap">{item.alert}</div>
									<ChevronRight className="text-muted-foreground opacity-0 group-hover:opacity-100" />
								</Button>
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
