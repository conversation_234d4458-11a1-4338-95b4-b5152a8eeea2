import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';

interface VoyageExpensesProps {
	className?: string;
}

export default function VoyageExpenses({ className }: VoyageExpensesProps) {
	return (
		<div className={`bg-panel rounded-xl border p-4 ${className}`}>
			<Table className="text-sm">
				<TableBody>
					<TableRow className="border-border/50 border-b">
						<TableCell className="text-muted-foreground w-1/2">Total Sailing</TableCell>
						<TableCell className="text-right">17d : 3h</TableCell>
					</TableRow>
					<TableRow className="border-border/50 border-t border-b">
						<TableCell className="text-muted-foreground w-1/2">Total Port Stay</TableCell>
						<TableCell className="text-right">4d : 2h</TableCell>
					</TableRow>
					<TableRow className="border-b-border/50">
						<TableCell className="w-1/2">
							<div className="font-bold">Total Voyage</div>
						</TableCell>
						<TableCell className="text-right">
							<span className="text-base font-bold">17d : 3h</span>
						</TableCell>
					</TableRow>

					<TableRow className="border-none hover:bg-transparent">
						<TableCell colSpan={2}></TableCell>
					</TableRow>

					<TableRow className="border-b-border/50">
						<TableCell className="text-muted-foreground w-1/2">Gross Freight</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground">$</span>
								<span>324,000</span>
							</div>
						</TableCell>
					</TableRow>
					<TableRow className="border-b-border/50">
						<TableCell className="text-muted-foreground w-1/2">TCE</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground">$</span>
								<span>26,314</span>
							</div>
						</TableCell>
					</TableRow>
					<TableRow className="border-b-border/50">
						<TableCell className="text-muted-foreground w-1/2">Net Daily</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground">$</span>
								<span>134</span>
							</div>
						</TableCell>
					</TableRow>
					<TableRow className="border-b-border/50">
						<TableCell className="w-1/2">
							<div className="font-bold">Total Expenses</div>
						</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-base">$</span>
								<span className="text-base font-bold">476,280</span>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
