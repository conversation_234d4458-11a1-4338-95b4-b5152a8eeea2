import { useNavigate } from 'react-router';
import { useState, useEffect } from 'react';

export default function VoyageItineraryMvp() {
	const navigate = useNavigate();

	const [portCalls, setPortCalls] = useState<PortCalls[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			try {
				const { default: response } = await import('https://script.googleusercontent.com/a/macros/abraxa.com/echo?user_content_key=AehSKLh9nJbpnrll2_p0i-Bw850L8UhsKr8t-pK61UTvDpK9rGcYkMamZebUGazf9hMzQ0WGrZ0fNnxK0_H2c8YrRne1g-DHSotjXkDhV7JZD-L7do8wq8u73n3-okfT0hS_Jaf3iqjk8F8V3N51umRDhFuDrp1GwSKiMo-iUCsJ0Lywe4X1s1Rvjy9Nb0r4jwESLxtpxFMwVQdvNjNgo7_xiP7agiM1UOSDsU8yV0IZRlg5XeaRvNyuL5KnQVZzQvhkYUr9qDD_QTfpBGAkHz6r3JOoV_8RZlfbzmyWGO89swwiiUBIteOufJYt7TuQURcX43omGU3445p_LOh9EPjAt6sVyBGiAU1lvIrnoOv0&lib=Me8GjD7WOIYHd570kK8hivpinp1njc95i'); // Replace with your API route

				const data = response as Voyage[];
				setVoyages(data);
			} catch (error) {
				console.error('Error fetching Voyages:', error);
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading) {
		return <DataTableSkeleton />;
	}


	return (
		<div></div>
		// <TooltipProvider>
		// 	<div className="grid gap-4">
		// 		<Table className="text-sm">
		// 			<TableHeader className="border-none">
		// 				<TableRow className="border-t">
		// 					<TableHead className="text-xs text-nowrap"></TableHead>
		// 					<TableHead className="text-xs text-nowrap">Status</TableHead>
		// 					<TableHead className="px-4 text-xs text-nowrap">Port</TableHead>
		// 					<TableHead className="text-xs text-nowrap">Activity</TableHead>
		// 					<TableHead className="text-xs text-nowrap">Arrival</TableHead>
		// 					<TableHead className="text-xs text-nowrap">Berthing</TableHead>
		// 					<TableHead className="text-xs text-nowrap">Departure</TableHead>
		// 					<TableHead className="text-xs text-nowrap">Agent</TableHead>
		// 					<TableHead className="text-xs text-nowrap">DA Stage</TableHead>
		// 					<TableHead className="border-r border-l px-4 text-right text-xs text-nowrap">
		// 						Port costs
		// 					</TableHead>
		// 					<TableHead className="border-r px-4 text-right text-xs text-nowrap">My payments</TableHead>
		// 					<TableHead className="border-r px-4 text-right text-xs text-nowrap">Balance</TableHead>
		// 					<TableHead className="text-right text-xs text-nowrap">Action</TableHead>
		// 				</TableRow>
		// 			</TableHeader>
		// 			<TableBody className="border-b">
		// 				<TableRow className="">
		// 					<TableCell>
		// 						<Tooltip>
		// 							<TooltipTrigger>
		// 								<GripHorizontal className="text-muted-foreground size-3 cursor-grab opacity-0" />
		// 							</TooltipTrigger>
		// 							<TooltipContent>Reorder</TooltipContent>
		// 						</Tooltip>
		// 					</TableCell>
		// 					<TableCell>
		// 						<Badge variant="outline" className="text-muted-foreground rounded-full">
		// 							<CircleCheck className="text-primary size-3" />
		// 							Sailed
		// 						</Badge>
		// 					</TableCell>
		// 					<TableCell>
		// 						<div className="flex items-center gap-1">
		// 							<Button
		// 								variant="ghost"
		// 								className="h-auto px-2 py-1 font-semibold"
		// 								onClick={() => navigate('/operations/port-calls/123')}
		// 							>
		// 								<div className="flex flex-col items-start">
		// 									<div className="leading-none">Birkenhead, UK</div>
		// 									<div className="text-muted-foreground text-2xs font-normal">
		// 										Starting port
		// 									</div>
		// 								</div>
		// 							</Button>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="text-muted-foreground">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>

		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>

		// 					<TableCell className="">
		// 						<div className="text-nowrap">23 April - 11:30</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-nowrap">Casper Agency Ltd</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>
		// 					<TableCell className="border-r border-l text-right">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<div className="text-muted-foreground text-nowrap"></div>
		// 					</TableCell>
		// 					<TableCell className="text-right"></TableCell>
		// 				</TableRow>

		// 				<TableRow className="border-none">
		// 					<TableCell>
		// 						<Tooltip>
		// 							<TooltipTrigger>
		// 								<GripHorizontal className="text-muted-foreground size-3 cursor-grab" />
		// 							</TooltipTrigger>
		// 							<TooltipContent>Reorder</TooltipContent>
		// 						</Tooltip>
		// 					</TableCell>
		// 					<TableCell>
		// 						<Badge variant="outline" className="text-muted-foreground rounded-full">
		// 							<CircleDot className="size-3 text-emerald-500" />
		// 							Arrived
		// 						</Badge>
		// 					</TableCell>
		// 					<TableCell>
		// 						<div className="flex items-center gap-1">
		// 							<Button
		// 								variant="ghost"
		// 								className="h-auto px-2 py-1 font-semibold"
		// 								onClick={() => navigate('/operations/port-calls/123')}
		// 							>
		// 								Amsterdam, NL
		// 							</Button>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="text-muted-foreground">
		// 						<div className="text-nowrap">
		// 							<Badge variant="secondary" className="rounded-full">
		// 								Loading
		// 							</Badge>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-nowrap">23 April - 09:45</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-nowrap">23 April - 10:15</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">25 April - 09:45</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-nowrap">Wilhelmsen Port Services</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<Badge variant="outline" className="rounded-full">
		// 							<Diamond className="size-3 text-emerald-500" />
		// 							<span className="font-medium">FDA</span>
		// 							<span className="text-muted-foreground">Completed</span>
		// 						</Badge>
		// 					</TableCell>
		// 					<TableCell className="border-r border-l text-right">
		// 						<Tooltip>
		// 							<TooltipTrigger asChild>
		// 								<Button
		// 									variant="ghost"
		// 									className="h-auto px-2 py-1"
		// 									onClick={() => navigate('/operations/port-calls/123/disbursement')}
		// 								>
		// 									<AlertTriangle className="size-3 text-red-500" />
		// 									<div>
		// 										<span className="text-muted-foreground text-2xs uppercase">usd</span>{' '}
		// 										<span className="font-semibold">80,000</span>
		// 									</div>
		// 								</Button>
		// 							</TooltipTrigger>
		// 							<TooltipContent>
		// 								<div className="grid gap-2 p-1">
		// 									<div className="text-muted-foreground py-1">DA Cost</div>
		// 									<div className="grid gap-2">
		// 										<div className="flex items-center gap-2">
		// 											<AlertTriangle className="size-3 text-red-500" />
		// 											<div className="min-w-[100px] font-medium">Pilotage</div>
		// 											<Badge
		// 												variant="outline"
		// 												className="text-muted-foreground h-5 rounded-full text-xs"
		// 											>
		// 												Tariff Mismatch
		// 											</Badge>
		// 											<div className="min-w-[80px] text-right font-semibold">+ $800</div>
		// 										</div>
		// 										<Separator className="bg-border/50" />
		// 										<div className="flex items-center gap-2">
		// 											<AlertTriangle className="size-3 text-red-500" />
		// 											<div className="min-w-[100px] font-medium">Tug Services</div>
		// 											<Badge
		// 												variant="outline"
		// 												className="text-muted-foreground h-5 rounded-full text-xs"
		// 											>
		// 												Tariff Mismatch
		// 											</Badge>
		// 											<div className="min-w-[80px] flex-1 text-right font-semibold">
		// 												+ $2,000
		// 											</div>
		// 										</div>
		// 										<Separator className="bg-border/50" />
		// 										<div className="-mx-1">
		// 											<Button
		// 												variant="ghost"
		// 												size="xs"
		// 												className="text-muted-foreground text-xs"
		// 												onClick={() =>
		// 													navigate('/operations/port-calls/123/disbursement')
		// 												}
		// 											>
		// 												View DA
		// 											</Button>
		// 										</div>
		// 									</div>
		// 								</div>
		// 							</TooltipContent>
		// 						</Tooltip>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<Button
		// 							variant="ghost"
		// 							className="h-auto px-2 py-1"
		// 							onClick={() => navigate('/operations/port-calls/ABX-123A/disbursement')}
		// 						>
		// 							<div>
		// 								<span className="text-muted-foreground text-2xs uppercase">usd</span>{' '}
		// 								<span className="font-semibold">65,000</span>
		// 							</div>
		// 						</Button>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<Button
		// 							variant="ghost"
		// 							className="h-auto px-2 py-1"
		// 							onClick={() => navigate('/operations/port-calls/ABX-123A/disbursement')}
		// 						>
		// 							<div>
		// 								<span className="text-muted-foreground text-2xs uppercase">usd</span>{' '}
		// 								<span className="font-semibold">15,000</span>
		// 							</div>
		// 						</Button>
		// 					</TableCell>
		// 					<TableCell className="text-right">
		// 						<Button
		// 							size="xs"
		// 							variant="secondary"
		// 							onClick={() => navigate('/operations/port-calls/ABX-123A/')}
		// 						>
		// 							View
		// 						</Button>
		// 					</TableCell>
		// 				</TableRow>

		// 				<TableRow className="border-none">
		// 					<TableCell>
		// 						<Tooltip>
		// 							<TooltipTrigger>
		// 								<GripHorizontal className="text-muted-foreground size-3 cursor-grab" />
		// 							</TooltipTrigger>
		// 							<TooltipContent>Reorder</TooltipContent>
		// 						</Tooltip>
		// 					</TableCell>
		// 					<TableCell>
		// 						<Badge variant="outline" className="text-muted-foreground rounded-full">
		// 							<CircleDashed className="size-3" />
		// 							Planned
		// 						</Badge>
		// 					</TableCell>
		// 					<TableCell>
		// 						<div className="flex items-center gap-1">
		// 							<Button
		// 								variant="ghost"
		// 								className="h-auto px-2 py-1 font-semibold"
		// 								onClick={() => navigate('/operations/port-calls/123')}
		// 							>
		// 								Gibraltar, GB
		// 							</Button>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="text-muted-foreground">
		// 						<div className="text-nowrap">
		// 							<Badge variant="secondary" className="rounded-full">
		// 								Bunkering
		// 							</Badge>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">3 May - 12:00</div>
		// 					</TableCell>

		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>

		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="border-r border-l text-right">
		// 						<div className="text-muted-foreground px-2 text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<div className="text-muted-foreground px-2 text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<div className="text-muted-foreground px-2 text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="text-right">
		// 						<Button size="xs" onClick={() => navigate('/operations/appointments/new-ai')}>
		// 							Appoint
		// 						</Button>
		// 					</TableCell>
		// 				</TableRow>

		// 				<TableRow className="border-none">
		// 					<TableCell>
		// 						<Tooltip>
		// 							<TooltipTrigger>
		// 								<GripHorizontal className="text-muted-foreground size-3 cursor-grab" />
		// 							</TooltipTrigger>
		// 							<TooltipContent>Reorder</TooltipContent>
		// 						</Tooltip>
		// 					</TableCell>
		// 					<TableCell>
		// 						<Badge variant="outline" className="text-muted-foreground rounded-full">
		// 							<CircleDashed className="size-3" />
		// 							Planned
		// 						</Badge>
		// 					</TableCell>
		// 					<TableCell>
		// 						<div className="flex items-center gap-1">
		// 							<Button
		// 								variant="ghost"
		// 								className="h-auto px-2 py-1 font-semibold"
		// 								onClick={() => navigate('/operations/port-calls/123')}
		// 							>
		// 								Odessa, UA
		// 							</Button>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="text-muted-foreground">
		// 						<div className="text-nowrap">
		// 							<Badge variant="secondary" className="rounded-full">
		// 								Discharging
		// 							</Badge>
		// 						</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>

		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>

		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="">
		// 						<div className="text-muted-foreground text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="border-r border-l text-right">
		// 						<div className="text-muted-foreground px-2 text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<div className="text-muted-foreground px-2 text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="border-r text-right">
		// 						<div className="text-muted-foreground px-2 text-nowrap">---</div>
		// 					</TableCell>
		// 					<TableCell className="text-right">
		// 						<Button size="xs" onClick={() => navigate('/operations/appointments/new-ai')}>
		// 							Appoint
		// 						</Button>
		// 					</TableCell>
		// 				</TableRow>
		// 			</TableBody>
		// 		</Table>
		// 		<div>
		// 			<Button variant="secondary" size="xs">
		// 				<Plus className="size-3" />
		// 				Port Call
		// 			</Button>
		// 		</div>
		// 	</div>
		// </TooltipProvider>
	);
}
