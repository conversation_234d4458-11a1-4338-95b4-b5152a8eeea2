import { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// Define the type for the API response data
interface PortCallData {
	[key: string]: unknown; // Using unknown instead of any for better type safety
}

export default function VoyageItineraryMvp() {
	const [portCalls, setPortCalls] = useState<PortCallData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			setError(null);

			try {
				const apiUrl =
					'https://script.google.com/a/macros/abraxa.com/s/AKfycbzjyET6KTgzMIdF5cbLZ3fxgZvthWE7zWsrw1vSUZmjFhPtWrtLoxWKvaYzpHdkpYU1/exec?path=n8n_voyage_itinerary&action=read';

				console.log('Fetching from API:', apiUrl);

				// Try multiple CORS proxy services
				const proxies = [
					'https://corsproxy.io/?',
					'https://api.allorigins.win/raw?url=',
					'https://cors-anywhere.herokuapp.com/'
				];

				let lastError: Error | null = null;

				for (const proxyUrl of proxies) {
					try {
						console.log(`Trying proxy: ${proxyUrl}`);

						let response;
						let data;

						if (proxyUrl.includes('allorigins.win/raw')) {
							// AllOrigins raw endpoint returns the content directly
							response = await fetch(proxyUrl + encodeURIComponent(apiUrl));
							if (!response.ok) {
								throw new Error(`HTTP error! status: ${response.status}`);
							}
							data = (await response.json()) as unknown;
						} else {
							// Other proxies return content directly
							response = await fetch(proxyUrl + apiUrl);
							if (!response.ok) {
								throw new Error(`HTTP error! status: ${response.status}`);
							}
							data = (await response.json()) as unknown;
						}

						console.log('Successfully fetched data:', data);
						const portCallsData = Array.isArray(data) ? data : [data];
						setPortCalls(portCallsData as PortCallData[]);
						return; // Success, exit the function
					} catch (proxyError) {
						console.log(`Proxy ${proxyUrl} failed:`, proxyError);
						lastError = proxyError instanceof Error ? proxyError : new Error('Unknown proxy error');
						continue; // Try next proxy
					}
				}

				// If we get here, all proxies failed
				throw lastError || new Error('All CORS proxies failed');
			} catch (error) {
				console.error('All fetch attempts failed:', error);
				setError(`Failed to fetch data: ${error instanceof Error ? error.message : 'Unknown error'}`);
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
					<p>Loading voyage itinerary...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<p className="mb-4 text-red-500">Error loading data: {error}</p>
					<Button onClick={() => window.location.reload()}>Retry</Button>
				</div>
			</div>
		);
	}

	if (!portCalls.length) {
		return (
			<div className="flex items-center justify-center p-8">
				<p className="text-muted-foreground">No voyage itinerary data available</p>
			</div>
		);
	}

	// Get all unique keys from the data to create table headers
	const allKeys = Array.from(new Set(portCalls.flatMap(item => Object.keys(item))));

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-lg font-semibold">Voyage Itinerary</h2>
				<Badge variant="outline">{portCalls.length} records</Badge>
			</div>

			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							{allKeys.map(key => (
								<TableHead key={key} className="text-xs font-medium">
									{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
								</TableHead>
							))}
						</TableRow>
					</TableHeader>
					<TableBody>
						{portCalls.map((item, index) => {
							// Create a unique key using available data or fallback to index
							const idValue = item.id || item.fileId || item.name;
							const uniqueKey =
								typeof idValue === 'string' || typeof idValue === 'number'
									? String(idValue)
									: `row-${index}`;

							return (
								<TableRow key={uniqueKey} className="hover:bg-muted/50">
									{allKeys.map(key => {
										const value = item[key];
										let displayValue = '-';

										if (value !== null && value !== undefined) {
											if (typeof value === 'object') {
												displayValue = JSON.stringify(value);
											} else if (
												typeof value === 'string' ||
												typeof value === 'number' ||
												typeof value === 'boolean'
											) {
												displayValue = String(value);
											} else {
												// For any other type, try to convert to string safely
												displayValue = '[Complex Value]';
											}
										}

										return (
											<TableCell key={key} className="text-sm">
												{displayValue}
											</TableCell>
										);
									})}
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
