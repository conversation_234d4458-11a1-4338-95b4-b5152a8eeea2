import { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// Define the type for the API response data
interface PortCallData {
	[key: string]: unknown; // Using unknown instead of any for better type safety
}

export default function VoyageItineraryMvp() {
	const [portCalls, setPortCalls] = useState<PortCallData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			setError(null);

			try {
				const apiUrl =
					'https://script.google.com/a/macros/abraxa.com/s/AKfycbzjyET6KTgzMIdF5cbLZ3fxgZvthWE7zWsrw1vSUZmjFhPtWrtLoxWKvaYzpHdkpYU1/exec?path=n8n_voyage_itinerary&action=read';

				console.log('Fetching from API:', apiUrl);

				// Try different fetch configurations
				const fetchConfigs = [
					// Try direct fetch with different modes
					{ mode: 'cors' as RequestMode, credentials: 'omit' as RequestCredentials },
					{ mode: 'cors' as RequestMode, credentials: 'include' as RequestCredentials },
					{ mode: 'no-cors' as RequestMode, credentials: 'omit' as RequestCredentials }
				];

				let lastError: Error | null = null;

				for (const config of fetchConfigs) {
					try {
						console.log(`Trying fetch with config:`, config);

						const response = await fetch(apiUrl, {
							method: 'GET',
							...config,
							headers: {
								Accept: 'application/json',
								'Content-Type': 'application/json'
							}
						});

						console.log('Response:', response);

						// For no-cors mode, we can't read the response
						if (response.type === 'opaque') {
							console.log('Got opaque response (no-cors mode), cannot read data');
							continue;
						}

						if (!response.ok) {
							throw new Error(`HTTP error! status: ${response.status}`);
						}

						const data = (await response.json()) as unknown;
						console.log('Successfully fetched data:', data);

						const portCallsData = Array.isArray(data) ? data : [data];
						setPortCalls(portCallsData as PortCallData[]);
						return; // Success, exit the function
					} catch (fetchError) {
						console.log(`Fetch config failed:`, fetchError);
						lastError = fetchError instanceof Error ? fetchError : new Error('Unknown fetch error');
						continue; // Try next config
					}
				}

				// If direct fetch fails, try one simple proxy as last resort
				try {
					console.log('Trying simple proxy as last resort...');
					const proxyResponse = await fetch(
						`https://api.codetabs.com/v1/proxy?quest=${encodeURIComponent(apiUrl)}`
					);

					if (proxyResponse.ok) {
						const data = (await proxyResponse.json()) as unknown;
						console.log('Proxy succeeded with data:', data);
						const portCallsData = Array.isArray(data) ? data : [data];
						setPortCalls(portCallsData as PortCallData[]);
						return;
					}
				} catch (proxyError) {
					console.log('Last resort proxy also failed:', proxyError);
				}

				// If we get here, everything failed
				throw lastError || new Error('All fetch attempts failed');
			} catch (error) {
				console.error('All fetch attempts failed:', error);
				setError(
					`Failed to fetch data: ${error instanceof Error ? error.message : 'Unknown error'}. Please ensure you are logged into your Google account and the API is accessible.`
				);
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
					<p>Loading voyage itinerary...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<p className="mb-4 text-red-500">Error loading data: {error}</p>
					<Button onClick={() => window.location.reload()}>Retry</Button>
				</div>
			</div>
		);
	}

	if (!portCalls.length) {
		return (
			<div className="flex items-center justify-center p-8">
				<p className="text-muted-foreground">No voyage itinerary data available</p>
			</div>
		);
	}

	// Get all unique keys from the data to create table headers
	const allKeys = Array.from(new Set(portCalls.flatMap(item => Object.keys(item))));

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-lg font-semibold">Voyage Itinerary</h2>
				<Badge variant="outline">{portCalls.length} records</Badge>
			</div>

			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							{allKeys.map(key => (
								<TableHead key={key} className="text-xs font-medium">
									{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
								</TableHead>
							))}
						</TableRow>
					</TableHeader>
					<TableBody>
						{portCalls.map((item, index) => {
							// Create a unique key using available data or fallback to index
							const idValue = item.id || item.fileId || item.name;
							const uniqueKey =
								typeof idValue === 'string' || typeof idValue === 'number'
									? String(idValue)
									: `row-${index}`;

							return (
								<TableRow key={uniqueKey} className="hover:bg-muted/50">
									{allKeys.map(key => {
										const value = item[key];
										let displayValue = '-';

										if (value !== null && value !== undefined) {
											if (typeof value === 'object') {
												displayValue = JSON.stringify(value);
											} else if (
												typeof value === 'string' ||
												typeof value === 'number' ||
												typeof value === 'boolean'
											) {
												displayValue = String(value);
											} else {
												// For any other type, try to convert to string safely
												displayValue = '[Complex Value]';
											}
										}

										return (
											<TableCell key={key} className="text-sm">
												{displayValue}
											</TableCell>
										);
									})}
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
