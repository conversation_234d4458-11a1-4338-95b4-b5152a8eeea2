import { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// Define the type for the API response data
interface PortCallData {
	[key: string]: unknown; // Using unknown instead of any for better type safety
}

export default function VoyageItineraryMvp() {
	const [portCalls, setPortCalls] = useState<PortCallData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			setError(null);

			try {
				// First try the direct approach
				const apiUrl =
					'https://script.google.com/a/macros/abraxa.com/s/AKfycbzjyET6KTgzMIdF5cbLZ3fxgZvthWE7zWsrw1vSUZmjFhPtWrtLoxWKvaYzpHdkpYU1/exec?path=n8n_voyage_itinerary&action=read';

				const response = await fetch(apiUrl, {
					method: 'GET',
					mode: 'cors',
					credentials: 'omit',
					headers: {
						Accept: 'application/json'
					}
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = (await response.json()) as unknown;
				const portCallsData = Array.isArray(data) ? data : [data];
				setPortCalls(portCallsData as PortCallData[]);
			} catch (fetchError) {
				console.error('Fetch failed:', fetchError);

				// Fallback: Use JSONP approach
				try {
					await fetchWithJSONP();
				} catch (jsonpError) {
					console.error('JSONP also failed:', jsonpError);
					setError('Unable to fetch data. Please check if you are logged into your Google account.');
				}
			}
			setLoading(false);
		}

		// JSONP fallback function
		function fetchWithJSONP(): Promise<void> {
			return new Promise((resolve, reject) => {
				const callbackName = 'jsonp_callback_' + Math.round(100000 * Math.random());
				const script = document.createElement('script');

				// Create global callback
				(window as any)[callbackName] = function (data: unknown) {
					try {
						const portCallsData = Array.isArray(data) ? data : [data];
						setPortCalls(portCallsData as PortCallData[]);
						resolve();
					} catch (error) {
						reject(error);
					} finally {
						// Cleanup
						document.head.removeChild(script);
						delete (window as any)[callbackName];
					}
				};

				// Set up error handling
				script.onerror = () => {
					document.head.removeChild(script);
					delete (window as any)[callbackName];
					reject(new Error('JSONP request failed'));
				};

				// Make the request
				script.src = `https://script.google.com/a/macros/abraxa.com/s/AKfycbzjyET6KTgzMIdF5cbLZ3fxgZvthWE7zWsrw1vSUZmjFhPtWrtLoxWKvaYzpHdkpYU1/exec?path=n8n_voyage_itinerary&action=read&callback=${callbackName}`;
				document.head.appendChild(script);
			});
		}

		void fetchData();
	}, []);

	if (loading) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
					<p>Loading voyage itinerary...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<p className="mb-4 text-red-500">Error loading data: {error}</p>
					<Button onClick={() => window.location.reload()}>Retry</Button>
				</div>
			</div>
		);
	}

	if (!portCalls.length) {
		return (
			<div className="flex items-center justify-center p-8">
				<p className="text-muted-foreground">No voyage itinerary data available</p>
			</div>
		);
	}

	// Get all unique keys from the data to create table headers
	const allKeys = Array.from(new Set(portCalls.flatMap(item => Object.keys(item))));

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-lg font-semibold">Voyage Itinerary</h2>
				<Badge variant="outline">{portCalls.length} records</Badge>
			</div>

			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							{allKeys.map(key => (
								<TableHead key={key} className="text-xs font-medium">
									{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
								</TableHead>
							))}
						</TableRow>
					</TableHeader>
					<TableBody>
						{portCalls.map((item, index) => {
							// Create a unique key using available data or fallback to index
							const idValue = item.id || item.fileId || item.name;
							const uniqueKey =
								typeof idValue === 'string' || typeof idValue === 'number'
									? String(idValue)
									: `row-${index}`;

							return (
								<TableRow key={uniqueKey} className="hover:bg-muted/50">
									{allKeys.map(key => {
										const value = item[key];
										let displayValue = '-';

										if (value !== null && value !== undefined) {
											if (typeof value === 'object') {
												displayValue = JSON.stringify(value);
											} else if (
												typeof value === 'string' ||
												typeof value === 'number' ||
												typeof value === 'boolean'
											) {
												displayValue = String(value);
											} else {
												// For any other type, try to convert to string safely
												displayValue = '[Complex Value]';
											}
										}

										return (
											<TableCell key={key} className="text-sm">
												{displayValue}
											</TableCell>
										);
									})}
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
