import { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// Define the type for the API response data
interface PortCallData {
	[key: string]: unknown; // Using unknown instead of any for better type safety
}

// Mock data for development/fallback
const mockData: PortCallData[] = [
	{
		id: '1',
		port_name: 'Rotterdam, NL',
		arrival_date: '2024-01-15',
		departure_date: '2024-01-17',
		status: 'Completed',
		cargo_operation: 'Loading',
		agent: 'Rotterdam Port Services'
	},
	{
		id: '2',
		port_name: 'Hamburg, DE',
		arrival_date: '2024-01-20',
		departure_date: '2024-01-22',
		status: 'In Progress',
		cargo_operation: 'Discharging',
		agent: 'Hamburg Maritime Agency'
	},
	{
		id: '3',
		port_name: 'Antwerp, BE',
		arrival_date: '2024-01-25',
		departure_date: '2024-01-27',
		status: 'Planned',
		cargo_operation: 'Loading',
		agent: 'Antwerp Logistics'
	}
];

export default function VoyageItineraryMvp() {
	const [portCalls, setPortCalls] = useState<PortCallData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		async function fetchData() {
			setLoading(true);
			setError(null);
			try {
				// Try direct API call first, fallback to proxy if CORS fails
				let response;
				try {
					response = await fetch(
						'https://script.google.com/a/macros/abraxa.com/s/AKfycbzjyET6KTgzMIdF5cbLZ3fxgZvthWE7zWsrw1vSUZmjFhPtWrtLoxWKvaYzpHdkpYU1/exec?path=n8n_voyage_itinerary&action=read',
						{
							method: 'GET',
							mode: 'cors',
							credentials: 'include',
							headers: {
								Accept: 'application/json',
								'Content-Type': 'application/json'
							}
						}
					);
				} catch (corsError) {
					console.log('Direct API call failed, trying proxy...', corsError);
					// Fallback to proxy endpoint
					response = await fetch('/api/voyage-itinerary');
				}

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = (await response.json()) as unknown;

				// Handle both array and object responses
				const portCallsData = Array.isArray(data) ? data : [data];
				setPortCalls(portCallsData as PortCallData[]);
			} catch (error) {
				console.error('Error fetching data:', error);
				setError(error instanceof Error ? error.message : 'Failed to fetch data');
			}
			setLoading(false);
		}

		void fetchData();
	}, []);

	if (loading) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
					<p>Loading voyage itinerary...</p>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<p className="mb-4 text-red-500">Error loading data: {error}</p>
					<Button onClick={() => window.location.reload()}>Retry</Button>
				</div>
			</div>
		);
	}

	if (!portCalls.length) {
		return (
			<div className="flex items-center justify-center p-8">
				<p className="text-muted-foreground">No voyage itinerary data available</p>
			</div>
		);
	}

	// Get all unique keys from the data to create table headers
	const allKeys = Array.from(new Set(portCalls.flatMap(item => Object.keys(item))));

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h2 className="text-lg font-semibold">Voyage Itinerary</h2>
				<Badge variant="outline">{portCalls.length} records</Badge>
			</div>

			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							{allKeys.map(key => (
								<TableHead key={key} className="text-xs font-medium">
									{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
								</TableHead>
							))}
						</TableRow>
					</TableHeader>
					<TableBody>
						{portCalls.map((item, index) => {
							// Create a unique key using available data or fallback to index
							const idValue = item.id || item.fileId || item.name;
							const uniqueKey =
								typeof idValue === 'string' || typeof idValue === 'number'
									? String(idValue)
									: `row-${index}`;

							return (
								<TableRow key={uniqueKey} className="hover:bg-muted/50">
									{allKeys.map(key => {
										const value = item[key];
										let displayValue = '-';

										if (value !== null && value !== undefined) {
											if (typeof value === 'object') {
												displayValue = JSON.stringify(value);
											} else if (
												typeof value === 'string' ||
												typeof value === 'number' ||
												typeof value === 'boolean'
											) {
												displayValue = String(value);
											} else {
												// For any other type, try to convert to string safely
												displayValue = '[Complex Value]';
											}
										}

										return (
											<TableCell key={key} className="text-sm">
												{displayValue}
											</TableCell>
										);
									})}
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</div>
		</div>
	);
}
