import VoyagePnlChart from './voyage-pnl-chart';
import VoyageTasks from './voyage-tasks';

interface VoyagePnlSummaryProps {
	className?: string;
}

export default function VoyagePnlSummary({ className }: VoyagePnlSummaryProps) {
	return (
		<div className={`flex flex-col gap-6 ${className}`}>
			<VoyagePnlChart />
			<div className="bg-panel flex-1 rounded-xl border p-4">
				<VoyageTasks />
			</div>
		</div>
	);
}
