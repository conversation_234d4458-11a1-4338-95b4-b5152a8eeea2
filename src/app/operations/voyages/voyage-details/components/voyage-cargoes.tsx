import { MoveUp } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

export default function VoyageCargoes() {
	return (
		<div className="grid gap-2">
			<h3 className="px-2 text-base font-semibold">Cargoes</h3>
			<Table className="text-sm">
				<TableHeader>
					<TableRow>
						<TableHead className="text-xs">Cargo</TableHead>
						<TableHead className="text-xs">Port</TableHead>
						<TableHead className="text-xs">Function</TableHead>
						<TableHead className="text-right text-xs">Quantity</TableHead>
						<TableHead className="text-right text-xs">Freight Rate</TableHead>
						<TableHead className="text-right text-xs">Laytime</TableHead>
						<TableHead className="text-right text-xs">Demurrage</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<TableRow>
						<TableCell>
							<span className="font-semibold">Iron Ore</span>
						</TableCell>
						<TableCell>Las Palmas, ES</TableCell>
						<TableCell>
							<Badge variant="outline" className="text-muted-foreground rounded-full">
								<span>Loading</span>
							</Badge>
						</TableCell>
						<TableCell className="text-right">
							<span className="font-semibold">10,000</span> mts
						</TableCell>
						<TableCell className="text-right">
							<span className="font-semibold">$1000</span> pmt
						</TableCell>
						<TableCell className="text-right">SHINC</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">$10,000</span>
								<span className="text-muted-foreground">/</span>
								<MoveUp className="size-3 text-red-500" />
								<span className="font-semibold">$11,658</span>
							</div>
						</TableCell>
					</TableRow>
					<TableRow>
						<TableCell>
							<span className="font-semibold">Iron Ore</span>
						</TableCell>
						<TableCell>Allapuzha, IN</TableCell>
						<TableCell>
							<Badge variant="outline" className="text-muted-foreground rounded-full">
								<span>Discharging</span>
							</Badge>
						</TableCell>
						<TableCell className="text-right">
							<span className="font-semibold">10,000</span> mts
						</TableCell>
						<TableCell className="text-right">
							<span className="font-semibold">$1000</span> pmt
						</TableCell>
						<TableCell className="text-right">SHINC</TableCell>
						<TableCell className="text-right">
							<div className="flex items-center justify-end gap-1">
								<span className="text-muted-foreground text-xs">$10,000</span>
								<span className="text-muted-foreground">/</span>
								<MoveUp className="size-3 text-red-500" />
								<span className="font-semibold">$11,658</span>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>
	);
}
