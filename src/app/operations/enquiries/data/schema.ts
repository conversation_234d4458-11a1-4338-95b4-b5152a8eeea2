import { z } from 'zod';

// Schema for vessel data
export const enquirySchema = z.object({
	id: z.string(),
	enqId: z.string(),
	name: z.string(),
	flag: z.string(),
	port: z.string(),
	eta: z.string(),
	cargoQty: z.string(),
	cargoUnit: z.string(),
	cargoType: z.string(),
	ownership: z.string(),
	type: z.string(),
	bunkers: z.string(), // MT
	lowestOffer: z.number(),
	status: z.enum(['en-route', 'in-port'])
});

export type Enquiry = z.infer<typeof enquirySchema>;
