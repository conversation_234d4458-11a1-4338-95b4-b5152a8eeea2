import { Table } from '@tanstack/react-table';
import { Plus, ListFilter, Download } from 'lucide-react';
import { useNavigate } from 'react-router';
import { Button } from '@/components/ui/button';
import { DataTableSearch } from '@/components/data-table/data-table-search';
import { DataTableDisplayOptions } from '@/components/data-table/data-table-display-options';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	const navigate = useNavigate();

	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center space-x-2">
				<Button size="xs" onClick={() => navigate('#')}>
					<Plus />
					Enquiry
				</Button>
				<Button variant="ghost" size="xs">
					<ListFilter />
					Filter
				</Button>
			</div>
			<div className="flex items-center gap-2">
				<DataTableSearch table={table} />
				<Button variant="ghost" size="xs">
					<Download />
					Export
				</Button>
				<DataTableDisplayOptions table={table} />
			</div>
		</div>
	);
}
