import { ColumnDef } from '@tanstack/react-table';
import { Box, CheckCheck } from 'lucide-react';
import { Enquiry } from '../data/schema';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { Badge } from '@/components/ui/badge';
import { formatAmount } from '@/common/utils/formatUtils';

export const columns: ColumnDef<Enquiry>[] = [
	{
		accessorKey: 'enqId',
		meta: {
			label: 'Enquiry ID'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Enquiry ID" />,
		cell: ({ row }) => <div className="text-muted-foreground">#{row.getValue('enqId')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'name',
		meta: {
			label: 'Vessel'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Vessel" className="min-w-[160px]" />,
		cell: ({ row }) => <div className="font-semibold">{row.getValue('name')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'port',
		meta: {
			label: 'Port'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Port" />,
		cell: ({ row }) => <div>{row.getValue('port')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'eta',
		meta: {
			label: 'ETA'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="ETA" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('eta')}</div>,
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'function',
		meta: {
			label: 'Function'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Function" />,
		cell: () => (
			<Badge variant="secondary" className="rounded-full">
				<Box className="text-primary size-3" />
				<span>Cargo Ops</span>
			</Badge>
		)
	},
	{
		accessorKey: 'cargo',
		meta: {
			label: 'Cargo'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Cargo" />,
		cell: () => (
			<div className="flex items-center gap-2">
				<div>12,500 mts</div>
				<div className="text-muted-foreground">-</div>
				<div className="text-muted-foreground">Iron Ore</div>
			</div>
		)
	},
	{
		accessorKey: 'requestedAgent',
		meta: {
			label: 'Requested agent'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Requested agent" />,
		cell: () => (
			<Badge variant="outline" className="rounded-full">
				3/3
			</Badge>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'status',
		meta: {
			label: 'Status'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
		cell: () => (
			<Badge variant="outline" className="rounded-full">
				<CheckCheck className="size-3 text-emerald-500" />
				Quoted
			</Badge>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'lowestOffer',
		meta: {
			label: 'Lowest offer'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Lowest offer" reverse />,
		cell: ({ row }) => (
			<div className="px-2 text-right font-medium">
				<span className="text-muted-foreground text-2xs">USD</span> {formatAmount(row.getValue('lowestOffer'))}
			</div>
		),
		enableSorting: true,
		enableHiding: true
	},
	{
		accessorKey: 'eta',
		meta: {
			label: 'Date submitted'
		},
		header: ({ column }) => <DataTableColumnHeader column={column} title="Date submitted" />,
		cell: ({ row }) => <div className="text-muted-foreground">{row.getValue('eta')}</div>,
		enableSorting: true,
		enableHiding: true
	}
];
