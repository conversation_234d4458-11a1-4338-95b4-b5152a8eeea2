import { useEffect, useState } from 'react';
import type { Enquiry } from '../data/schema';
import { enquiries } from '../data/data';
import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';

export default function Enquiry() {
	const [data, setData] = useState<Enquiry[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		// Simulate loading data
		setLoading(true);

		// In a real app, this would be an API call
		// For now, we'll use the mock data with a timeout
		const timer = setTimeout(() => {
			try {
				setData(enquiries);
			} catch (error) {
				console.error('Error loading vessels:', error);
			} finally {
				setLoading(false);
			}
		}, 500);

		// Clean up the timer if the component unmounts
		return () => clearTimeout(timer);
	}, []);

	if (loading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={data} columns={columns} />;
}
