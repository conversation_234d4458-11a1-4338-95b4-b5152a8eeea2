import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';
import { useAppointments } from '@/hooks/appointment/use-appointments';

export default function Appointments() {
	const { appointments, loading: appointmentsLoading } = useAppointments();
	if (appointmentsLoading) {
		return <DataTableSkeleton />;
	}
	return <DataTable data={appointments} columns={columns} />;
}
