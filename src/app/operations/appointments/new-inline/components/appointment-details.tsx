import * as React from 'react';
import { BriefcaseBusiness, Building2, CalendarIcon, Hash, Ship, Tag, UserCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

const vessels: ComboboxOption[] = [
	{ value: '1', label: 'mv Meridiaan Express' },
	{ value: '2', label: 'mv Meridiaan Cinco' },
	{ value: '3', label: 'mv Vertom Meridiaan' },
	{ value: '4', label: 'mv Gulf Meridiaan' },
	{ value: '5', label: 'mv Astra Meridiaan' }
];

const operators: ComboboxOption[] = [
	{ value: 'john', label: '<PERSON><PERSON> <PERSON><PERSON>' },
	{ value: 'robert', label: '<PERSON><PERSON>' },
	{ value: 'vicky', label: '<PERSON><PERSON>' }
];

const legalEntities: ComboboxOption[] = [
	{ value: '1', label: 'WPS Legal Singapore' },
	{ value: '2', label: 'WPS Legal Europe' },
	{ value: '3', label: 'WPS Legal Australia' }
];

const companies: ComboboxOption[] = [
	{ value: 'wilhelmsen-port-services', label: 'Wihelmsen Port Services S.A.' },
	{ value: 'varna-port-services', label: 'Varna Port Services Ltd.' },
	{ value: 'agencia-nabsa-maritima-sa', label: 'Agencia Maritima Nabsa SA' },
	{ value: 'blue-horizon-logistics', label: 'Blue Horizon Logistics' },
	{ value: 'oceanic-freight-solutions', label: 'Oceanic Freight Solutions' }
];

const types: ComboboxOption[] = [
	{ value: 'charterers-nominated', label: "Charterer's Nominated" },
	{ value: 'owners-nominated', label: "Owner's Nominated" },
	{ value: 'owners-protective', label: "Owner's Protective" },
	{ value: 'hub-agent', label: 'HUB agent' }
];

export default function AppointmentDetails() {
	const [, setSelectedVessel] = React.useState<string | null>(null);
	const [, setSelectedLegalEntity] = React.useState<string | null>(null);
	const [, setSelectedCompany] = React.useState<string | null>(null);
	const [, setSelectedType] = React.useState<string | null>(null);
	const [date, setDate] = React.useState<Date>();

	return (
		<div className="grid grid-cols-[auto_minmax(0px,1fr)] items-center gap-x-12 gap-y-2 px-2 py-4 text-sm">
			<div className="text-muted-foreground flex items-center gap-2">
				<Ship className="h-4 w-4" />
				Vessel
			</div>
			<div>
				<ComboboxButtonInline
					data={vessels}
					placeholder="Choose vessel"
					onSelect={value => setSelectedVessel(value)}
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Hash className="h-4 w-4" />
				Voyage ID
			</div>
			<div>
				<Input
					placeholder="Enter voyage ID"
					className="hover:bg-accent h-auto w-auto border-none px-2 py-1 font-medium shadow-none"
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Building2 className="h-4 w-4" />
				Legal entity
			</div>
			<div>
				<ComboboxButtonInline
					data={legalEntities}
					placeholder="Choose legal entity"
					onSelect={value => setSelectedLegalEntity(value)}
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<BriefcaseBusiness className="h-4 w-4" />
				Nominated agent
			</div>
			<div>
				<ComboboxButtonInline
					data={companies}
					placeholder="Choose nominated agent"
					onSelect={value => setSelectedCompany(value)}
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Tag className="h-4 w-4" />
				Nomination type
			</div>
			<div>
				<ComboboxButtonInline
					data={types}
					placeholder="Choose nomination type"
					onSelect={value => setSelectedType(value)}
				/>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<CalendarIcon className="h-4 w-4" />
				Appointed on
			</div>
			<div>
				<Popover>
					<PopoverTrigger asChild>
						<Button
							size="xs"
							variant="outline"
							className={cn(
								'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
								!date && 'text-muted-foreground'
							)}
						>
							{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
					</PopoverContent>
				</Popover>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<UserCircle className="h-4 w-4" />
				Operator
			</div>
			<div>
				<ComboboxButtonInline
					data={operators}
					placeholder="Choose operator"
					onSelect={value => setSelectedLegalEntity(value)}
				/>
			</div>
		</div>
	);
}
