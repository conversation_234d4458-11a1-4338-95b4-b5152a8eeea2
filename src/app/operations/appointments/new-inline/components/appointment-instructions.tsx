import { More<PERSON><PERSON><PERSON><PERSON>, Paperclip, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export default function AppointmentInstructions() {
	return (
		<div className="grid gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Instructions</h3>
			<div className="grid gap-4 px-2 py-4">
				<div className="min-h-32">
					<Input
						id="title"
						placeholder="Enter title"
						className="m-0 border-none px-0 text-base font-medium shadow-none focus-visible:ring-0 md:text-xl"
					/>
					<AutosizeTextarea
						placeholder="Write some or type '/' for commands and snippets"
						className="min-h-12 resize-none border-none bg-transparent px-0 ring-0 outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
					/>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline" size="sm" className="flex gap-1 bg-transparent">
						<Paperclip className="h-4 w-4" />
						Attach file
					</Button>
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="outline" size="icon" className="h-8 w-8 bg-transparent">
									<MoreHorizontal className="size-3.5" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>More actions</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<Separator />
				<div>
					<Button variant="secondary" size="sm" className="flex items-center gap-1">
						<Plus className="h-4 w-4" />
						Add instruction
					</Button>
				</div>
			</div>
		</div>
	);
}
