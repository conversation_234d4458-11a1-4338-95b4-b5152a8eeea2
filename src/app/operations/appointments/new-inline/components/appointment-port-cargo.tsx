import * as React from 'react';
import { Anchor, ArrowBigLeftDash, ArrowLeftRight, CalendarIcon } from 'lucide-react';
import AppointmentCargoItem from './appointment-cargo-item';
import { ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';

const ports: ComboboxOption[] = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' },
	{ value: 'hamburg', label: 'Hamburg, DE' },
	{ value: 'antwerp', label: 'Antwerp, BE' },
	{ value: 'southampton', label: 'Southampton, UK' },
	{ value: 'shanghai', label: 'Shanghai, CN' },
	{ value: 'los-angeles', label: 'Los Angeles, US' },
	{ value: 'sydney', label: 'Sydney, AU' }
];

const functions: ComboboxOption[] = [
	{ value: 'cargo-ops', label: 'Cargo ops' },
	{ value: 'bunkering', label: 'Bunkering' },
	{ value: 'shelter-layby', label: 'Shelter / Layby' },
	{ value: 'waiting-layby', label: 'Waiting / Layby' },
	{ value: 'lay-up', label: 'Lay up' },
	{ value: 'repairs', label: 'Repairs' },
	{ value: 'crew-change', label: 'Crew change' },
	{ value: 'clearance', label: 'Clearance' }
];

export default function AppointmentPortCargo() {
	const [date, setDate] = React.useState<Date>();
	const [, setSelectedPort] = React.useState<string | null>(null);
	const [selectedFunction, setSelectedFunction] = React.useState<string | null>(null);

	return (
		<div className="grid gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Port & Cargo details</h3>
			<div className="grid grid-cols-[130px_minmax(0px,1fr)] items-center gap-x-12 gap-y-2 px-2 py-4 text-sm">
				<div className="text-muted-foreground flex items-center gap-2">
					<Anchor className="size-4" />
					Port
				</div>
				<div>
					<ComboboxButtonInline
						data={ports}
						placeholder="Choose port"
						onSelect={value => setSelectedPort(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<ArrowLeftRight className="size-4" />
					Port function
				</div>
				<div>
					<ComboboxButtonInline
						data={functions}
						placeholder="Choose function"
						onSelect={value => setSelectedFunction(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<CalendarIcon className="size-4" />
					ETA
				</div>
				<div>
					<Popover>
						<PopoverTrigger asChild>
							<Button
								size="xs"
								variant="outline"
								className={cn(
									'aria-expanded:bg-accent aria-expanded:ring-primary hover:text-muted-foreground border-none bg-transparent text-sm shadow-none aria-expanded:ring-1',
									!date && 'text-muted-foreground'
								)}
							>
								{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="start">
							<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
						</PopoverContent>
					</Popover>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<ArrowBigLeftDash className="size-4" />
					Previous port
				</div>
				<div>
					<ComboboxButtonInline
						data={ports}
						placeholder="Choose previous port"
						onSelect={value => setSelectedPort(value)}
					/>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<ArrowBigLeftDash className="size-4" />
					Next port
				</div>
				<div>
					<ComboboxButtonInline
						data={ports}
						placeholder="Choose next port"
						onSelect={value => setSelectedPort(value)}
					/>
				</div>
			</div>
			{selectedFunction === 'cargo-ops' && <AppointmentCargoItem />}
		</div>
	);
}
