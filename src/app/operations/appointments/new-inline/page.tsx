import { Link } from 'react-router';
import AppointmentHeader from './components/appointment-header';
import AppointmentFooter from './components/appointment-footer';
import AppointmentDetails from './components/appointment-details';
import AppointmentPortCargo from './components/appointment-port-cargo';
import AppointmentInstructions from './components/appointment-instructions';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbList,
	BreadcrumbLink,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
export default function Page() {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<Link to="../">
									<BreadcrumbLink>Appointments</BreadcrumbLink>
								</Link>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>New appointment</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4">
				<div className="grid w-full max-w-3xl gap-4">
					<AppointmentHeader />
					<AppointmentDetails />
					<Separator />
					<AppointmentPortCargo />
					<Separator />
					<AppointmentInstructions />
				</div>
			</div>
			<AppointmentFooter />
		</>
	);
}
