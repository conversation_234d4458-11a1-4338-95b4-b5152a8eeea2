import * as React from 'react';
import { format } from 'date-fns';
import { File } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

import { Breadcrumb, BreadcrumbItem, BreadcrumbList, BreadcrumbPage } from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { ComboboxInline, ComboboxOption } from '@/components/ui/combobox-inline';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';

const companies: ComboboxOption[] = [
	{ value: 'wilhelmsen-port-services', label: 'Wihelmsen Port Services S.A.' },
	{ value: 'varna-port-services', label: 'Varna Port Services Ltd.' },
	{ value: 'agencia-nabsa-maritima-sa', label: 'Agencia Maritima Nabsa SA' },
	{ value: 'blue-horizon-logistics', label: 'Blue Horizon Logistics' },
	{ value: 'oceanic-freight-solutions', label: 'Oceanic Freight Solutions' }
];

const vessels: ComboboxOption[] = [
	{ value: 'meridiaan-express', label: 'mv Meridiaan Express' },
	{ value: 'meridiaan-cinco', label: 'mv Meridiaan Cinco' },
	{ value: 'vertom-meridiaan', label: 'mv Vertom Meridiaan' },
	{ value: 'gulf-meridiaan', label: 'mv Gulf Meridiaan' },
	{ value: 'astra-meridiaan', label: 'mv Astra Meridiaan' }
];

const ports: ComboboxOption[] = [
	{ value: 'roterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' }
];

const time: ComboboxOption[] = [
	{ value: '08:00', label: '08:00' },
	{ value: '09:00', label: '09:00' },
	{ value: '10:00', label: '10:00' },
	{ value: '11:00', label: '11:00' },
	{ value: '12:00', label: '12:00' },
	{ value: '13:00', label: '13:00' },
	{ value: '14:00', label: '14:00' },
	{ value: '15:00', label: '15:00' },
	{ value: '16:00', label: '16:00' },
	{ value: '17:00', label: '17:00' },
	{ value: '18:00', label: '18:00' },
	{ value: '19:00', label: '19:00' },
	{ value: '20:00', label: '20:00' },
	{ value: '21:00', label: '21:00' },
	{ value: '22:00', label: '22:00' },
	{ value: '23:00', label: '23:00' },
	{ value: '00:00', label: '00:00' }
];

const cargoes: ComboboxOption[] = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'iron-sulfate', label: 'Iron Sulfate' },
	{ value: 'other-liquid-bulk', label: 'Other liquid bulk' },
	{ value: 'crude-oil', label: 'Crude oil' },
	{ value: 'naphtha', label: 'Naphtha' },
	{ value: 'lpg', label: 'LPG' },
	{ value: 'petroleum-products', label: 'Petroleum products' }
];

const operations: ComboboxOption[] = [
	{ value: 'loading', label: 'Loading' },
	{ value: 'discharging', label: 'Discharging' }
];

export default function NewAppointment() {
	const [_, setSelectedCompany] = React.useState('');
	const [selectedVessel, setSelectedVessel] = React.useState('');
	const [selectedPort, setSelectedPort] = React.useState('');
	const [date, setDate] = React.useState<Date>();
	const [__, setSelectedTime] = React.useState('');
	const [___, setSelectedCargo] = React.useState('');
	const [____, setSelectedOperation] = React.useState('');

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbPage>New appointment</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4 pt-0">
				<div className="grid w-full max-w-3xl gap-8 px-4">
					<h2 className="text-center text-xl font-semibold">{`Let's get started with a new appointment`}</h2>
					<div contentEditable spellCheck="false" className="grid gap-8 leading-8 focus-visible:outline-none">
						<div>Dear Sirs,</div>
						<div>
							We are pleased to formally appoint
							<ComboboxInline
								data={companies}
								placeholder="Choose company"
								onSelect={value => setSelectedCompany(value)}
							/>
							as our authorized agent for our vessel,
							<ComboboxInline
								data={vessels}
								placeholder="Choose vessel"
								onSelect={value => setSelectedVessel(value)}
							/>
							, during its call at
							<ComboboxInline
								data={ports}
								placeholder="Choose port"
								onSelect={value => setSelectedPort(value)}
							/>
							.
						</div>
						<div>
							Our vessel, <b>{vessels.find(v => v.value === selectedVessel)?.label || selectedVessel}</b>,
							bearing IMO number <b>98762612</b>, and flying under the <b>The Netherlands flag</b>, is
							scheduled to arrive at{' '}
							<b>{ports.find(p => p.value === selectedPort)?.label || selectedPort}</b> on
							<Popover>
								<PopoverTrigger asChild>
									<Button
										variant={'ghost'}
										className={cn(
											'border-primary text-link h-8 w-auto justify-start border-0 border-b-1 border-dashed px-1.5 py-0 text-left text-base',
											!date && 'text-muted-foreground'
										)}
									>
										{date ? format(date, 'PPP') : <span>Pick a date</span>}
									</Button>
								</PopoverTrigger>
								<PopoverContent className="w-auto p-0" align="start">
									<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
								</PopoverContent>
							</Popover>{' '}
							at{' '}
							<ComboboxInline
								data={time}
								placeholder="hh:mm"
								onSelect={value => setSelectedTime(value)}
							/>
						</div>
						<div>
							The vessel will be handling
							<ComboboxInline
								data={cargoes}
								placeholder="Cargo type"
								onSelect={value => setSelectedCargo(value)}
							/>
							, with an estimated quantity of{' '}
							<Input
								className="border-primary text-link inline-flex h-8 w-[100px] border-0 border-b-1 border-dashed px-1.5 font-medium ring-0 focus-visible:ring-0 md:text-base"
								placeholder="0.000.000"
							/>{' '}
							mts, for{' '}
							<ComboboxInline
								data={operations}
								placeholder="Choose operation"
								onSelect={value => setSelectedOperation(value)}
							/>{' '}
							operation. We rely on you to coordinate all cargo-related procedures, liaise with shippers
							and receivers, and facilitate a timely turnaround.
						</div>
						<div>
							{`As our appointed agent, your responsibilities will include, but are not limited to,
							arranging berthing and pilotage, communicating with port authorities, assisting with crew
							changes, provisioning, and bunkering if needed. Additionally, we count on you to manage all
							operational aspects efficiently, ensuring that our vessel's stay in port is conducted
							without unnecessary delays or complications.`}
						</div>
						<div>
							We kindly request that you keep us updated with timely reports on all port-related matters,
							including cargo handling progress and any unforeseen challenges. Furthermore, we require
							that all port expenses and disbursements be submitted for prior approval before incurring
							major costs.
						</div>
						<div>
							Please confirm your acceptance of this appointment at your earliest convenience. We look
							forward to working with you and appreciate your dedication to the smooth handling of our
							vessel.
						</div>
					</div>
					<Separator />
					<h3 className="text-base font-semibold">Instructions</h3>
					<div className="grid gap-4">
						<div className="flex items-center gap-3">
							<Checkbox id="terms" />
							<label
								htmlFor="terms"
								className="text-base leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								Port agency instructions
							</label>
						</div>
						<div className="flex items-center gap-3">
							<Checkbox id="agency" />
							<label
								htmlFor="agency"
								className="text-base leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								Port agency instructions
							</label>
						</div>
						<div className="flex items-center gap-3">
							<Checkbox id="communications" />
							<label
								htmlFor="communications"
								className="text-base leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								Communications instructions
							</label>
						</div>
						<div className="flex items-center gap-3">
							<Checkbox id="transportation" />
							<label
								htmlFor="transportation"
								className="text-base leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
							>
								Transportation instructions
							</label>
						</div>
					</div>
					<Separator />
					<h3 className="text-base font-semibold">Attachments</h3>
					<div className="flex flex-col gap-2 sm:flex-row">
						<Button variant="outline" size="sm">
							<File />
							CargoInstructions.pdf
						</Button>
						<Button variant="outline" size="sm">
							<File />
							PortRestrictions.pdf
						</Button>
					</div>
					<Separator />
					<div className="pb-4">
						<Button>Appoint</Button>
					</div>
				</div>
			</div>
		</>
	);
}
