import { AnimatePresence, motion } from 'framer-motion';
import { useNavigate } from 'react-router';
import { <PERSON><PERSON><PERSON>, CheckCircle } from 'lucide-react';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbList,
	BreadcrumbLink,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export default function Page() {
	const navigate = useNavigate();

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls">Port calls</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls/ABX-123A">
									mv Baltic Trader (#ABX-123A)
								</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>Appointment (#APP-213)</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex items-center justify-end gap-2 px-4">
					<Button variant="ghost" size="xs" onClick={() => {}}>
						<Sparkles />
						<span>Operator One</span>
					</Button>
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center justify-center gap-4 p-4">
				<div className="w-full max-w-lg">
					<AnimatePresence>
						<motion.div
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: 20 }}
							transition={{ duration: 0.24 }}
						>
							<Card className="border-0 shadow-lg">
								<CardContent className="flex flex-col items-center gap-8 p-12 text-center">
									{/* Success Icon */}
									<div className="relative">
										<div className="flex h-20 w-20 items-center justify-center rounded-full">
											<CheckCircle className="h-12 w-12 text-emerald-600" strokeWidth={1.5} />
										</div>
									</div>

									{/* Main Message */}
									<div className="space-y-2">
										<h1 className="text-xl font-semibold">Your appointment request was sent to</h1>
									</div>

									{/* Company Details */}
									<div className="bg-accent rounded-l-2xl px-8 py-2">
										<h2 className="text-base font-semibold">Noordriver Shipping B.V.</h2>
										<p className="text-muted-foreground text-sm"><EMAIL></p>
									</div>

									{/* Description */}
									<div className="max-w-md">
										<p className="text-muted-foreground text-sm leading-relaxed">
											You will receive an e-mail & system notification once the requested party
											has accepted your request.
										</p>
									</div>

									{/* Action Button */}
									<Button
										className="bg-primary w-full rounded-lg px-8 py-3 font-medium text-white"
										size="lg"
										onClick={() => navigate('/operations/port-calls/ABX-123A')}
									>
										Back to Port Call
									</Button>
								</CardContent>
							</Card>
						</motion.div>
					</AnimatePresence>
				</div>
			</div>
		</>
	);
}
