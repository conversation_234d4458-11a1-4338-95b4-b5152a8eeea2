import { An<PERSON>, ArrowBigLeftDash, Box, Calendar } from 'lucide-react';
import AppointmentCargoItem from './appointment-cargo-item';
import { Badge } from '@/components/ui/badge';

export default function AppointmentPortCargo() {
	return (
		<div className="grid gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Port & Cargo details</h3>
			<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-4 px-2 py-4 text-sm">
				<div className="text-muted-foreground flex items-center gap-2">
					<Anchor className="size-4" />
					Port
				</div>
				<div className="flex h-5 items-center gap-2">
					Rotterdam, NL
					<Badge variant="secondary" className="rounded-full">
						<Box className="text-primary h-3 w-3" />
						<span>Cargo ops</span>
					</Badge>
				</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<Calendar className="size-4" />
					ETA
				</div>
				<div>17 Mar - 10:53</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<ArrowBigLeftDash className="size-4" />
					Previous port
				</div>
				<div>Amsterdam, NL</div>
				<div className="text-muted-foreground flex items-center gap-2">
					<ArrowBigLeftDash className="size-4" />
					Next port
				</div>
				<div>Singapore, SG</div>
			</div>
			<AppointmentCargoItem />
		</div>
	);
}
