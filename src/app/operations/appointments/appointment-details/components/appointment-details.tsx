import { BellPlus, BriefcaseBusiness, Building2, Calendar, Hash, Ship, Tag, UserCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function AppointmentDetails() {
	return (
		<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-12 gap-y-4 px-2 py-4 text-sm">
			<div className="text-muted-foreground flex items-center gap-2">
				<Ship className="h-4 w-4" />
				Vessel
			</div>
			<div>mv Meridiaan</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Hash className="h-4 w-4" />
				Voyage ID
			</div>
			<div>#VOY-123A</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Building2 className="h-4 w-4" />
				Legal entity
			</div>
			<div>Houston Legal S.A.</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<BriefcaseBusiness className="h-4 w-4" />
				Nominated agent
			</div>
			<div className="flex h-5 items-center gap-2">
				Local Agent Company Ltd.
				<Button variant="secondary" size="xs">
					<BellPlus className="size-3.5" />
					Send reminder
				</Button>
			</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Tag className="h-4 w-4" />
				Nomination type
			</div>
			<div>Owner&apos;s protective</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<Calendar className="h-4 w-4" />
				Appointed on
			</div>
			<div>17 Mar - 10:53</div>
			<div className="text-muted-foreground flex items-center gap-2">
				<UserCircle className="h-4 w-4" />
				Operator
			</div>
			<div className="flex items-center gap-2">
				<Avatar className="h-5 w-5 rounded-sm">
					<AvatarImage src="/avatars/avatar-md.webp" alt="John" />
					<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
						JD
					</AvatarFallback>
				</Avatar>
				John Doe
			</div>
		</div>
	);
}
