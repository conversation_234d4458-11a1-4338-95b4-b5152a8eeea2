import { Calendar, File } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function AppointmentInstructions() {
	return (
		<div className="grid gap-2 py-4">
			<h3 className="px-2 text-base font-semibold">Instructions</h3>
			<div className="grid gap-4 px-2 py-4">
				<div className="text-xl font-medium">Appointment instructions</div>
				<p className="text-sm">
					<PERSON><PERSON><PERSON> magna felis, maximus sed iaculis sit amet, aliquet eu lorem. Curabitur at dictum leo.
					Mauris pharetra lectus metus, eu lacinia massa blandit sed. Sed feugiat ullamcorper dui vel viverra.
					Vivamus consectetur consequat neque sit amet bibendum. Pellentesque efficitur purus imperdiet,
					luctus eros id, laoreet leo. Nam hendrerit pretium urna nec gravida. Vestibulum lacinia fringilla
					nunc a laoreet. Etiam finibus laoreet sem, eget tempus est semper sit amet.
				</p>
				<ul className="my-4 list-disc px-8 text-sm [&>li]:mt-2">
					<li>Ut hendrerit ligula in metus dignissim, non scelerisque diam tincidunt.</li>
					<li>Nam feugiat odio et est ornare, id hendrerit nibh imperdiet.</li>
					<li>Phasellus tincidunt urna at mollis blandit.</li>
					<li>Vivamus in nunc imperdiet, rutrum sapien vitae, lacinia mi.</li>
				</ul>
				<p className="text-sm">Proin sed erat malesuada, hendrerit.</p>
				<p></p>
				<div className="flex flex-col gap-2 sm:flex-row">
					<Button variant="outline" size="sm" className="text-muted-foreground">
						<File />
						Port-Restrictions-2024.pdf
					</Button>
					<Button variant="outline" size="sm" className="text-muted-foreground">
						<File />
						Iron_Ore.pdf
					</Button>
				</div>
			</div>

			<Separator />
			<div className="text-muted-foreground flex items-center gap-2 p-2 text-sm">
				<Calendar className="size-3.5" />
				Last updated on 17 Nov 2024 - 18:56 by Krastin Krastev
			</div>
		</div>
	);
}
