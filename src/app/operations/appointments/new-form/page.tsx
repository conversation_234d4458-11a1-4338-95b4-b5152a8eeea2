import { Link } from 'react-router';
import AppointmentDetails from './components/appointment-details';
import AppointmentPortCargo from './components/appointment-port-cargo';
import AppointmentNomination from './components/appointment-nomination';
import AppointmentInstructions from './components/appointment-instructions';
import AppointmentFooter from './components/appointment-footer';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbList,
	BreadcrumbLink,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
export default function NewAppointment() {
	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<Link to="../">
									<BreadcrumbLink>Appointments</BreadcrumbLink>
								</Link>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>New appointment</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="px-4">
					<ModeToggle />
				</div>
			</header>
			<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4 pt-0">
				<div className="grid w-full max-w-3xl gap-4">
					<h2 className="py-4 text-center text-xl font-semibold">
						Let&apos;s get started with a new appointment
					</h2>
					<AppointmentDetails />
					<AppointmentPortCargo />
					<AppointmentNomination />
					<AppointmentInstructions />
				</div>
			</div>
			<AppointmentFooter />
		</>
	);
}
