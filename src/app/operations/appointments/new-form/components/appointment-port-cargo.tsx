import * as React from 'react';
import { ArrowBigLeftDash, ArrowBigRightDash, ArrowLeftRight, CalendarIcon, MapPin } from 'lucide-react';
import AppointmentCargo from './appointment-cargo';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { ComboboxButton } from '@/components/ui/combobox-button';
import { Label } from '@/components/ui/label';

const ports: ComboboxOption[] = [
	{ value: 'rotterdam', label: 'Rotterdam, NL' },
	{ value: 'amsterdam', label: 'Amsterdam, NL' },
	{ value: 'varna', label: 'Varna, BG' },
	{ value: 'barcelona', label: 'Barcelona, BG' },
	{ value: 'singapore', label: 'Singapore, SG' },
	{ value: 'hamburg', label: 'Hamburg, DE' },
	{ value: 'antwerp', label: 'Antwerp, BE' },
	{ value: 'southampton', label: 'Southampton, UK' },
	{ value: 'shanghai', label: 'Shanghai, CN' },
	{ value: 'los-angeles', label: 'Los Angeles, US' },
	{ value: 'sydney', label: 'Sydney, AU' }
];

const functions: ComboboxOption[] = [
	{ value: 'cargo-ops', label: 'Cargo ops' },
	{ value: 'bunkering', label: 'Bunkering' },
	{ value: 'shelter-layby', label: 'Shelter / Layby' },
	{ value: 'waiting-layby', label: 'Waiting / Layby' },
	{ value: 'lay-up', label: 'Lay up' },
	{ value: 'repairs', label: 'Repairs' },
	{ value: 'crew-change', label: 'Crew change' },
	{ value: 'clearance', label: 'Clearance' }
];

const time: ComboboxOption[] = [
	{ value: '08:00', label: '08:00' },
	{ value: '09:00', label: '09:00' },
	{ value: '10:00', label: '10:00' },
	{ value: '11:00', label: '11:00' },
	{ value: '12:00', label: '12:00' },
	{ value: '13:00', label: '13:00' },
	{ value: '14:00', label: '14:00' },
	{ value: '15:00', label: '15:00' },
	{ value: '16:00', label: '16:00' },
	{ value: '17:00', label: '17:00' },
	{ value: '18:00', label: '18:00' },
	{ value: '19:00', label: '19:00' },
	{ value: '20:00', label: '20:00' },
	{ value: '21:00', label: '21:00' },
	{ value: '22:00', label: '22:00' },
	{ value: '23:00', label: '23:00' },
	{ value: '00:00', label: '00:00' },
	{ value: '01:00', label: '01:00' },
	{ value: '02:00', label: '02:00' },
	{ value: '03:00', label: '03:00' },
	{ value: '04:00', label: '04:00' },
	{ value: '05:00', label: '05:00' },
	{ value: '06:00', label: '06:00' },
	{ value: '07:00', label: '07:00' }
];

export default function AppointmentPortCargo() {
	const [date, setDate] = React.useState<Date>();
	const [, setSelectedPort] = React.useState<string | null>(null);
	const [selectedFunction, setSelectedFunction] = React.useState<string | null>(null);

	return (
		<Card className="flex-1 gap-6">
			<CardHeader>
				<CardTitle>Port & Cargo details</CardTitle>
			</CardHeader>
			<CardContent className="grid gap-4">
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					<div className="flex flex-col gap-1.5">
						<Label htmlFor="port" className="text-muted-foreground text-xs">
							Port
						</Label>
						<Combobox
							id="port"
							data={ports}
							placeholder="Port"
							icon={MapPin}
							onSelect={value => setSelectedPort(value)}
						/>
					</div>
					<div className="flex flex-col gap-1.5">
						<Label htmlFor="function" className="text-muted-foreground text-xs">
							Port function
						</Label>
						<Combobox
							id="function"
							data={functions}
							placeholder="Port function"
							icon={ArrowLeftRight}
							onSelect={value => setSelectedFunction(value)}
						/>
					</div>
					<div className="flex flex-col gap-1.5">
						<Label htmlFor="eta" className="text-muted-foreground text-xs">
							ETA
						</Label>
						<div className="a-datetime-filed">
							<Popover>
								<PopoverTrigger asChild>
									<Button
										id="eta"
										variant="outline"
										className={cn('rounded-r-none px-3', !date && 'text-muted-foreground')}
									>
										<CalendarIcon className="text-muted-foreground" />
										{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
									</Button>
								</PopoverTrigger>
								<PopoverContent className="w-auto p-0" align="start">
									<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
								</PopoverContent>
							</Popover>
							<Combobox data={time} placeholder="HH:MM" onSelect={value => setSelectedPort(value)} />
						</div>
					</div>
					<div className="flex flex-row items-center gap-2">
						<ComboboxButton
							data={ports}
							placeholder="Previous port"
							icon={ArrowBigLeftDash}
							onSelect={value => setSelectedPort(value)}
						/>
						<ComboboxButton
							data={ports}
							placeholder="Next port"
							icon={ArrowBigRightDash}
							onSelect={value => setSelectedPort(value)}
						/>
					</div>
				</div>
				{selectedFunction === 'cargo-ops' && <AppointmentCargo />}
			</CardContent>
		</Card>
	);
}
