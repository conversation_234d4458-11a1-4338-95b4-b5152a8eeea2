import * as React from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, Trash, Building2, BriefcaseBusiness, Plus, CalendarPlus, CalendarMinus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButton } from '@/components/ui/combobox-button';
import { Input } from '@/components/ui/input';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';

const commodity: ComboboxOption[] = [
	{ value: 'iron-ore', label: 'Ironore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'oil', label: 'Oil' },
	{ value: 'gas', label: 'Gas' },
	{ value: 'petroleum', label: 'Petroleum' }
];

const unit: ComboboxOption[] = [
	{ value: 'mts', label: 'mts' },
	{ value: 'kgs', label: 'kgs' },
	{ value: 'lts', label: 'lts' },
	{ value: 'cbm', label: 'cbm' }
];

const cargoFunction: ComboboxOption[] = [
	{ value: 'loading', label: 'Loading' },
	{ value: 'discharging', label: 'Discharging' },
	{ value: 'transshipment', label: 'Transshipment' },
	{ value: 'lightering', label: 'Lightering' }
];

const companies: ComboboxOption[] = [
	{ value: 'wilhelmsen-port-services', label: 'Wihelmsen Port Services S.A.' },
	{ value: 'varna-port-services', label: 'Varna Port Services Ltd.' },
	{ value: 'agencia-nabsa-maritima-sa', label: 'Agencia Maritima Nabsa SA' },
	{ value: 'blue-horizon-logistics', label: 'Blue Horizon Logistics' },
	{ value: 'oceanic-freight-solutions', label: 'Oceanic Freight Solutions' }
];

const laycan: ComboboxOption[] = [
	{ value: '2024-11-18', label: '18 Nov 24' },
	{ value: '2024-11-22', label: '22 Nov 24' },
	{ value: '2024-12-12', label: '12 Dec 24' }
];

export default function AppointmentCargo() {
	const [, setSelectedCommodity] = React.useState<string | null>(null);
	const [, setSelectedUnit] = React.useState<string | null>(null);
	const [, setSelectedFunction] = React.useState<string | null>(null);
	const [, setSelectedCharterer] = React.useState<string | null>(null);
	const [, setLaycanStart] = React.useState<string | null>(null);
	const [, setLaycanEnd] = React.useState<string | null>(null);
	const [expanded, setExpanded] = React.useState(true);
	const [date, setDate] = React.useState<Date>();

	return (
		<>
			<div className="rounded-md border">
				<div className="flex items-center justify-between p-2">
					<Button
						variant="ghost"
						className="text-muted-foreground h-auto gap-1 px-2 py-1 font-normal"
						onClick={() => {
							setExpanded(prev => !prev);
						}}
					>
						Cargo #1
						<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
					</Button>
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
									<Trash />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Delete cargo</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<AnimatePresence>
					{expanded && (
						<motion.div
							initial={{ height: 0, overflow: 'hidden' }}
							animate={{ height: 'auto', overflow: 'visible' }}
							exit={{ height: 0, overflow: 'hidden' }}
							transition={{ duration: 0.15 }}
						>
							<div className="grid grid-cols-1 gap-4 px-4 py-4 md:grid-cols-4">
								<div className="flex flex-col gap-1.5">
									<Label htmlFor="commodity" className="text-muted-foreground text-xs">
										Commodity
									</Label>
									<Combobox
										id="commodity"
										data={commodity}
										placeholder="Commodity"
										onSelect={value => setSelectedCommodity(value)}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label htmlFor="quantity" className="text-muted-foreground text-xs">
										Quantity
									</Label>
									<div className="a-quantity-filed">
										<Input
											id="quantity"
											placeholder="0"
											className="bg-background hover:bg-accent rounded-r-none"
										/>
										<Combobox
											data={unit}
											placeholder="mts"
											onSelect={value => setSelectedUnit(value)}
										/>
									</div>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label htmlFor="cargoFunction" className="text-muted-foreground text-xs">
										Cargo function
									</Label>
									<Combobox
										id="cargoFunction"
										data={cargoFunction}
										placeholder="Cargo function"
										onSelect={value => setSelectedFunction(value)}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label htmlFor="date" className="text-muted-foreground text-xs">
										C/P Date
									</Label>
									<Popover>
										<PopoverTrigger asChild>
											<Button
												id="date"
												variant="outline"
												className={cn('justify-start px-3', !date && 'text-muted-foreground')}
											>
												{date ? date.toLocaleDateString() : <span>DD/MM/YY</span>}
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0" align="start">
											<Calendar mode="single" selected={date} initialFocus onSelect={setDate} />
										</PopoverContent>
									</Popover>
								</div>
								<div className="flex flex-row items-center gap-2">
									<ComboboxButton
										data={laycan}
										placeholder="Laycan start"
										icon={CalendarPlus}
										onSelect={value => setLaycanStart(value)}
									/>
									<ComboboxButton
										data={laycan}
										placeholder="Laycan end"
										icon={CalendarMinus}
										onSelect={value => setLaycanEnd(value)}
									/>
									<ComboboxButton
										data={companies}
										placeholder="Charterer"
										icon={Building2}
										onSelect={value => setSelectedCharterer(value)}
									/>
									<ComboboxButton
										data={companies}
										placeholder="Charterer nominated agent"
										icon={BriefcaseBusiness}
										onSelect={value => setSelectedCharterer(value)}
									/>
								</div>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
			<div>
				<Button variant="secondary" size="sm" className="flex items-center gap-1">
					<Plus className="h-4 w-4" />
					Add cargo
				</Button>
			</div>
		</>
	);
}
