import * as React from 'react';
import { <PERSON>, CircleUser, <PERSON>h, Building2, ListOrdered } from 'lucide-react';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ComboboxButton } from '@/components/ui/combobox-button';
import { Label } from '@/components/ui/label';

const vessels: ComboboxOption[] = [
	{ value: '1', label: 'mv Meridiaan Express' },
	{ value: '2', label: 'mv Meridiaan Cinco' },
	{ value: '3', label: 'mv Vertom Meridiaan' },
	{ value: '4', label: 'mv Gulf Meridiaan' },
	{ value: '5', label: 'mv Astra Meridiaan' }
];

const operators: ComboboxOption[] = [
	{ value: 'john', label: '<PERSON><PERSON>' },
	{ value: 'robert', label: '<PERSON><PERSON> <PERSON>' },
	{ value: 'vicky', label: '<PERSON>. Encheva' }
];

const legalEntities: ComboboxOption[] = [
	{ value: '1', label: 'WPS Legal Singapore' },
	{ value: '2', label: 'WPS Legal Europe' },
	{ value: '3', label: 'WPS Legal Australia' }
];

export default function AppointmentDetails() {
	const [, setSelectedVessel] = React.useState<string | null>(null);
	const [, setSelectedOperator] = React.useState<string | null>(null);
	const [, setSelectedLegalEntity] = React.useState<string | null>(null);
	return (
		<Card className="flex-1 gap-6">
			<CardHeader>
				<CardTitle>Appointment details</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					<div className="flex flex-col gap-1.5">
						<Label htmlFor="vessel" className="text-muted-foreground text-xs">
							Vessel
						</Label>
						<Combobox
							id="vessel"
							data={vessels}
							placeholder="Vessel"
							icon={Ship}
							onSelect={value => setSelectedVessel(value)}
						/>
					</div>
					<div className="flex flex-col gap-1">
						<Label htmlFor="voyage" className="text-muted-foreground text-xs">
							Voyage number
						</Label>
						<Input
							id="voyage"
							icon={Hash}
							placeholder="Voyage number"
							className="bg-background hover:bg-accent"
						/>
					</div>
					<div className="flex flex-col gap-1">
						<Label htmlFor="reference" className="text-muted-foreground text-xs">
							Reference number
						</Label>
						<Input
							id="reference"
							icon={ListOrdered}
							placeholder="Reference number"
							className="bg-background hover:bg-accent"
						/>
					</div>
					<div className="flex flex-row gap-2">
						<ComboboxButton
							data={legalEntities}
							placeholder="Legal entity"
							icon={Building2}
							onSelect={value => setSelectedLegalEntity(value)}
						/>
						<ComboboxButton
							data={operators}
							placeholder="Operator"
							icon={CircleUser}
							onSelect={value => setSelectedOperator(value)}
						/>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
