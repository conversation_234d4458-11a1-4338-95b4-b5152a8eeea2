import * as React from 'react';
import { BriefcaseBusiness, Tag } from 'lucide-react';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const companies: ComboboxOption[] = [
	{ value: 'wilhelmsen-port-services', label: 'Wihelmsen Port Services S.A.' },
	{ value: 'varna-port-services', label: 'Varna Port Services Ltd.' },
	{ value: 'agencia-nabsa-maritima-sa', label: 'Agencia Maritima Nabsa SA' },
	{ value: 'blue-horizon-logistics', label: 'Blue Horizon Logistics' },
	{ value: 'oceanic-freight-solutions', label: 'Oceanic Freight Solutions' }
];

const types: ComboboxOption[] = [
	{ value: 'charterers-nominated', label: "Charterer's Nominated" },
	{ value: 'owners-nominated', label: "Owner's Nominated" },
	{ value: 'owners-protective', label: "Owner's Protective" },
	{ value: 'hub-agent', label: 'HUB agent' }
];

export default function AppointmentNomination() {
	const [, setSelectedCompany] = React.useState<string | null>(null);
	const [, setSelectedType] = React.useState<string | null>(null);
	return (
		<Card className="flex-1 gap-6">
			<CardHeader>
				<CardTitle>Agent nomination</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					<div className="flex flex-col gap-1.5">
						<Label htmlFor="agent" className="text-muted-foreground text-xs">
							Nominated agent
						</Label>
						<Combobox
							id="agent"
							data={companies}
							placeholder="Agent"
							icon={BriefcaseBusiness}
							onSelect={value => setSelectedCompany(value)}
						/>
					</div>
					<div className="flex flex-col gap-1.5">
						<Label htmlFor="type" className="text-muted-foreground text-xs">
							Nomination type
						</Label>
						<Combobox
							id="type"
							data={types}
							placeholder="Nomination type"
							icon={Tag}
							onSelect={value => setSelectedType(value)}
						/>
					</div>
					<div className="mt-5 flex items-center">
						<Checkbox id="sub-agent" />
						<Label htmlFor="sub-agent" className="ml-2">
							Use sub-agent
						</Label>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
