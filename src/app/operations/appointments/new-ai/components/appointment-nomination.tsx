import { useState } from 'react';
import { BriefcaseBusiness, Check, Search, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupCheckItem, RadioGroupRadioItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

const agents = [
	{
		id: '1',
		name: 'Noordriver Shipping B.V.',
		email: '<EMAIL>',
		rating: 4.5,
		portCalls: 6
	},
	{
		id: '2',
		name: 'LKL Oceantrade B.V.',
		email: '<EMAIL>',
		rating: 4.8,
		portCalls: 4
	},
	{
		id: '3',
		name: 'Locus International',
		email: '<EMAIL>',
		rating: 4.2,
		portCalls: 2
	}
];

export default function AppointmentNomination() {
	const [open, setOpen] = useState(false);
	const [nominationType, setNominationType] = useState('charterers-nominated');
	const [selectedAgent, setSelectedAgent] = useState('suggested');
	const [selectedCompany, setSelectedCompany] = useState<(typeof agents)[0] | null>(null);

	const renderNominatedSection = () => {
		// if (!selectedAgent) return null;

		switch (selectedAgent) {
			case 'suggested':
				return (
					<div>
						<Label className="text-muted-foreground mb-3 block text-xs">Suggested agents</Label>
						<RadioGroup value={selectedAgent} className="" onValueChange={setSelectedAgent}>
							<div className="flex flex-col gap-2">
								{agents.map(agent => (
									<Label
										key={agent.id}
										htmlFor={agent.id}
										className={`flex cursor-pointer items-center gap-4 rounded-lg border p-3 font-normal transition-colors ${
											selectedAgent === agent.id
												? 'bg-primary/10 border-primary/50'
												: 'hover:bg-accent'
										}`}
									>
										<RadioGroupCheckItem value={agent.id} id={agent.id} />
										<div className="flex flex-1 flex-col">
											<div className="text-md font-medium">{agent.name}</div>
											<div className="text-muted-foreground text-sm">{agent.email}</div>
										</div>
										<Badge variant="outline" className="rounded-full">
											<BriefcaseBusiness className="text-muted-foreground size-3" />
											<span className="font-medium">{agent.portCalls}</span>
											<span className="text-muted-foreground">Port calls</span>
										</Badge>
									</Label>
								))}
							</div>
						</RadioGroup>
					</div>
				);

			case 'selected':
				return selectedCompany ? (
					<div>
						<Label className="text-muted-foreground mb-3 block text-xs">Nominated agent</Label>
						<div className="bg-primary/10 border-primary/50 flex items-center gap-4 rounded-lg border p-3">
							<span className="bg-primary relative size-4 rounded-full">
								<Check
									strokeWidth={4}
									className="text-primary-foreground absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"
								/>
							</span>
							<div className="flex flex-1 flex-col">
								<div className="text-md font-medium">{selectedCompany.name}</div>
								<div className="text-muted-foreground text-sm">{selectedCompany.email}</div>
							</div>
							<Badge variant="outline" className="rounded-full">
								<BriefcaseBusiness className="text-muted-foreground size-3" />
								<span className="font-medium">{selectedCompany.portCalls}</span>
								<span className="text-muted-foreground">Port calls</span>
							</Badge>
							<Button
								variant="ghost"
								size="sm"
								className="text-muted-foreground h-8 w-8 p-0"
								onClick={() => setSelectedAgent('suggested')}
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>
					</div>
				) : null;

			default:
				return (
					<div>
						<Label className="text-muted-foreground mb-3 block text-xs">Suggested agents</Label>
						<RadioGroup value={selectedAgent} className="" onValueChange={setSelectedAgent}>
							<div className="flex flex-col gap-2">
								{agents.map(agent => (
									<Label
										key={agent.id}
										htmlFor={agent.id}
										className={`flex cursor-pointer items-center gap-4 rounded-lg border p-3 font-normal transition-colors ${
											selectedAgent === agent.id
												? 'bg-primary/10 border-primary/50'
												: 'hover:bg-accent'
										}`}
									>
										<RadioGroupCheckItem value={agent.id} id={agent.id} />
										<div className="flex flex-1 flex-col">
											<div className="text-md font-medium">{agent.name}</div>
											<div className="text-muted-foreground text-sm">{agent.email}</div>
										</div>
										<Badge variant="outline" className="rounded-full">
											<BriefcaseBusiness className="text-muted-foreground size-3" />
											<span className="font-medium">{agent.portCalls}</span>
											<span className="text-muted-foreground">Port calls</span>
										</Badge>
									</Label>
								))}
							</div>
						</RadioGroup>
					</div>
				);
		}
	};

	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Agent Nomination</h3>
			<div>
				<Label className="text-muted-foreground mb-3 block text-xs">Nomination type</Label>
				<RadioGroup value={nominationType} className="h-7" onValueChange={setNominationType}>
					<div className="flex flex-wrap gap-2">
						<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
							<RadioGroupRadioItem value="charterers-nominated" id="charterers-nominated" />
							<Label htmlFor="charterers-nominated" className="cursor-pointer">
								Charterer&apos;s nominated
							</Label>
						</Badge>
						<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
							<RadioGroupRadioItem value="owners-nominated" id="owners-nominated" />
							<Label htmlFor="owners-nominated" className="cursor-pointer">
								Owner&apos;s nominated
							</Label>
						</Badge>
						<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
							<RadioGroupRadioItem value="owners-protective" id="owners-protective" />
							<Label htmlFor="owners-protective" className="cursor-pointer">
								Owner&apos;s protective
							</Label>
						</Badge>
						<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
							<RadioGroupRadioItem value="hub-agent" id="hub-agent" />
							<Label htmlFor="hub-agent" className="cursor-pointer">
								HUB agent
							</Label>
						</Badge>
					</div>
				</RadioGroup>
			</div>
			<Card>
				<CardHeader>
					<Popover open={open} onOpenChange={setOpen}>
						<PopoverTrigger>
							<Input placeholder="Search agent..." icon={Search} className="bg-background" />
						</PopoverTrigger>
						<PopoverContent className="j-full p-0" align="start">
							<Command>
								<CommandInput placeholder="Search agent or enter email" />
								<CommandList>
									<CommandEmpty>No results found.</CommandEmpty>
									<CommandGroup heading="Suggestions">
										{agents.map(agent => (
											<CommandItem
												key={agent.id}
												value={agent.name}
												className="flex-row justify-between"
												onSelect={() => {
													setSelectedCompany(agent);
													setSelectedAgent('selected');
													setOpen(false);
												}}
											>
												<div className="flex flex-col">
													<div className="font-medium">{agent.name}</div>
													<div className="text-muted-foreground text-sm">{agent.email}</div>
												</div>
												<Badge variant="outline" className="rounded-full">
													<BriefcaseBusiness className="text-muted-foreground size-3" />
													<span className="font-medium">{agent.portCalls}</span>
													<span className="text-muted-foreground">Port calls</span>
												</Badge>
											</CommandItem>
										))}
									</CommandGroup>
								</CommandList>
							</Command>
						</PopoverContent>
					</Popover>
				</CardHeader>
				<CardContent className="space-y-4">{renderNominatedSection()}</CardContent>
			</Card>
		</div>
	);
}
