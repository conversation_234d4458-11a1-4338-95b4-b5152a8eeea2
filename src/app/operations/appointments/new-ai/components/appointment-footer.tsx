import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	AlertDialog,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger
} from '@/components/ui/alert-dialog';

export default function AppointmentFooter() {
	const navigate = useNavigate();
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const handleCreateAppointment = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			void navigate('/operations/appointments/abx-123a/success');
		}, 500);
	};

	const handleDiscardAppointment = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			void navigate('/operations/appointments');
		}, 500);
	};

	return (
		<div className="bg-panel flex w-full flex-col items-center border-t px-4 shadow-2xl">
			<div className="flex w-full max-w-3xl items-center justify-end gap-2 py-2">
				<AlertDialog>
					<AlertDialogTrigger asChild>
						<Button variant="secondary" size="sm">
							Discard
						</Button>
					</AlertDialogTrigger>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>Discard the appointment?</AlertDialogTitle>
							<AlertDialogDescription>
								Discarding will delete the appointment and all entered information. This action cannot
								be undone.
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>Cancel</AlertDialogCancel>
							<Button variant="default" size="sm" disabled={isLoading} onClick={handleDiscardAppointment}>
								{isLoading ? (
									<>
										<Loader2 className="h-4 w-4 animate-spin" />
										<span>Discarding...</span>
									</>
								) : (
									'Discard'
								)}
							</Button>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>

				<Button variant="default" size="sm" disabled={isLoading} onClick={handleCreateAppointment}>
					{isLoading ? (
						<>
							<Loader2 className="h-4 w-4 animate-spin" />
							<span>Appointing...</span>
						</>
					) : (
						'Appoint agent'
					)}
				</Button>
			</div>
		</div>
	);
}
