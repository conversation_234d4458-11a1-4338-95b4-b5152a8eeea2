import { Download, Edit3 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';

export default function AppointmentHeader() {
	return (
		<div className="flex w-full items-center gap-4">
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">Appointment</h2>
				<Badge variant="outline" className="rounded-full">
					<Edit3 className="text-muted-foreground h-3 w-3" />
					<span className="text-muted-foreground">Draft</span>
				</Badge>
			</div>
			<div className="flex items-center justify-end gap-2">
				<Button variant="ghost" size="xs" className="text-muted-foreground hidden">
					<Download />
					Download
				</Button>
			</div>
		</div>
	);
}
