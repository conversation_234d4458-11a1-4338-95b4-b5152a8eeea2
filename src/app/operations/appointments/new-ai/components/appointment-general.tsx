import { <PERSON><PERSON>, ArrowBigLeftDash, ArrowBigRightDash, Building2, Calendar, Hash, Ship, Text, User } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function AppointmentGeneral() {
	return (
		<div className="flex w-full flex-col items-center gap-6 lg:flex-row">
			<div className="w-full flex-1">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<Ship className="size-4" />
						Vessel
					</div>
					<div className="flex items-center gap-2">
						<div className="font-semibold">mv Baltic Trader</div>
						<div className="text-muted-foreground">(23123519)</div>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Hash className="size-4" />
						Voyage ID
					</div>
					<div>EM-2935-CN</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Text className="size-4" />
						Reference
					</div>
					<div>VC-12455</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Anchor className="size-4" />
						Port
					</div>
					<div>Rotterdam, NL</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="size-4" />
						ETA
					</div>
					<div>17 Mar - 08:02</div>
				</div>
			</div>
			<div className="w-full flex-1">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
					<div className="h-5"></div>
					<div className="h-5"></div>

					<div className="text-muted-foreground flex items-center gap-2">
						<Building2 className="size-4" />
						Legal entity
					</div>
					<div>LG Panama Ltd.</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<User className="size-4" />
						Operator
					</div>
					<div className="flex items-center gap-2">
						<Avatar className="size-5 rounded-sm">
							<AvatarImage src="/static/media/avatar-md.webp" alt="J. Doe" />
							<AvatarFallback className="bg-primary text-2xs rounded-md font-medium text-white">
								JD
							</AvatarFallback>
						</Avatar>
						J. Todorov
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<ArrowBigLeftDash className="size-4" />
						Previous port
					</div>
					<div>Antwerp, BE</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<ArrowBigRightDash className="size-4" />
						Next port
					</div>
					<div>Hamburg, DE</div>
				</div>
			</div>
		</div>
	);
}
