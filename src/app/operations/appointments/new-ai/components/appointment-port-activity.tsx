import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/ui/date-picker';
import { RadioGroup, RadioGroupRadioItem } from '@/components/ui/radio-group';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';
import { Separator } from '@/components/ui/separator';

const commodities: ComboboxOption[] = [
	{ value: 'iron-ore', label: 'Iron Ore' },
	{ value: 'coal', label: 'Coal' },
	{ value: 'grain', label: 'Grain' },
	{ value: 'crude-oil', label: 'Crude Oil' },
	{ value: 'lng', label: 'LNG' }
];

const units: ComboboxOption[] = [
	{ value: 'mts', label: 'mts' },
	{ value: 'cbm', label: 'cbm' },
	{ value: 'long-tons', label: 'Long tons' },
	{ value: 'short-tons', label: 'Short tons' },
	{ value: 'barrels', label: 'Barrels' },
	{ value: 'liters', label: 'Liters' }
];

const fuelTypes: ComboboxOption[] = [
	{ value: 'mgo', label: 'MGO (Marine Gas Oil)' },
	{ value: 'hfo', label: 'HFO (Heavy Fuel Oil)' },
	{ value: 'vlsfo', label: 'VLSFO (Very Low Sulfur Fuel Oil)' },
	{ value: 'lng', label: 'LNG (Liquefied Natural Gas)' }
];

const canals: ComboboxOption[] = [
	{ value: 'laden', label: 'Laden' },
	{ value: 'ballast', label: 'Ballast' }
];

const towages: ComboboxOption[] = [
	{ value: 'yes', label: 'Yes' },
	{ value: 'no', label: 'No' }
];

const activities: ComboboxOption[] = [
	{ value: 'survey', label: 'Survey' },
	{ value: 'crew-change', label: 'Crew change' },
	{ value: 'spare-parts-delivery', label: 'Spare parts delivery' },
	{ value: 'waste-disposal', label: 'Waste disposal' },
	{ value: 'provisions', label: 'Provisions' },
	{ value: 'ctm', label: 'CTM' },
	{ value: 'other', label: 'Other' }
];

const currencies: ComboboxOption[] = [
	{ value: 'usd', label: 'USD' },
	{ value: 'eur', label: 'EUR' },
	{ value: 'gbp', label: 'GBP' },
	{ value: 'aud', label: 'AUD' },
	{ value: 'cad', label: 'CAD' }
];

const datePickerClassName = 'h-9';

export default function AppointmentPortActivity() {
	const [portActivity, setPortActivity] = useState('loading');
	const [selectedActivity, setSelectedActivity] = useState<string | null>(null);
	const [_selectedCommodity, setSelectedCommodity] = useState<string | null>(null);
	const [_selectedTowage, setSelectedTowage] = useState<string | null>(null);
	const [_selectedCanal, setSelectedCanal] = useState<string | null>(null);
	const [_selectedUnit, setSelectedUnit] = useState<string | null>(null);
	const [_selectedCurrency, setSelectedCurrency] = useState<string | null>(null);
	const [_selectedFuelType, setSelectedFuelType] = useState<string | null>(null);
	const [cpDate, setCpDate] = useState<Date | undefined>(new Date());
	const [laycanFrom, setLaycanFrom] = useState<Date | undefined>();
	const [laycanTo, setLaycanTo] = useState<Date | undefined>();
	const [surveyDate, setSurveyDate] = useState<Date | undefined>(new Date());

	const renderActivitySection = () => {
		if (!selectedActivity) return null;

		switch (selectedActivity) {
			case 'survey':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-2">
							<div className="text-md col-span-2 font-semibold">Survey</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Survey company</Label>
								<Input placeholder="Enter company" className="bg-background" />
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Survey date</Label>
								<DatePicker
									date={surveyDate}
									setDate={setSurveyDate}
									className={datePickerClassName}
									placeholder="dd/mm/yy"
								/>
							</div>
							<div className="col-span-2 flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Additional information</Label>
								<Input placeholder="Enter additional info" className="bg-background" />
							</div>
						</div>
					</>
				);

			case 'crew-change':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-3">
							<div className="text-md col-span-3 font-semibold">Crew change</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">On signers</Label>
								<Input type="number" placeholder="0" className="bg-background" />
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Off signers</Label>
								<Input type="number" placeholder="0" className="bg-background" />
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Crewing company</Label>
								<Input placeholder="Enter company" className="bg-background" />
							</div>
							<div className="col-span-3 flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Additional information</Label>
								<Input placeholder="Enter additional info" className="bg-background" />
							</div>
						</div>
					</>
				);

			case 'spare-parts-delivery':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-2">
							<div className="text-md col-span-2 font-semibold">Spare parts delivery</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Package information</Label>
								<Input placeholder="Enter package info" className="bg-background" />
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">AWB number</Label>
								<Input placeholder="Enter AWB number" className="bg-background" />
							</div>
						</div>
					</>
				);

			case 'waste-disposal':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-2">
							<div className="text-md col-span-2 font-semibold">Waste disposal</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Quantity</Label>
								<Input type="number" placeholder="0" className="bg-background" />
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Unit of measure</Label>
								<Combobox
									data={units}
									placeholder="Choose unit"
									search={false}
									onSelect={setSelectedUnit}
								/>
							</div>
						</div>
					</>
				);

			case 'provisions':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4">
							<div className="text-md font-semibold">Provisions</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Provisions required</Label>
								<Input placeholder="Enter provisions info" className="bg-background" />
							</div>
						</div>
					</>
				);

			case 'ctm':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4 md:grid-cols-3">
							<div className="text-md col-span-3 font-semibold">CTM</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Amount</Label>
								<Input type="number" placeholder="0" className="bg-background" />
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Currency</Label>
								<Combobox
									data={currencies}
									placeholder="Choose currency"
									onSelect={setSelectedCurrency}
								/>
							</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Captain name</Label>
								<Input placeholder="Enter captain name" className="bg-background" />
							</div>
							<div className="col-span-3 flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Additional information</Label>
								<Input placeholder="Enter additional info" className="bg-background" />
							</div>
						</div>
					</>
				);

			case 'other':
				return (
					<>
						<div className="col-span-3">
							<Separator />
						</div>
						<div className="bg-secondary/20 col-span-3 grid grid-cols-1 gap-4 rounded-md border p-4">
							<div className="text-md font-semibold">Other</div>
							<div className="flex flex-col gap-1.5">
								<Label className="text-muted-foreground text-xs">Other requirements</Label>
								<Input placeholder="Enter other requirements" className="bg-background" />
							</div>
						</div>
					</>
				);

			default:
				return null;
		}
	};

	const renderActivityCard = () => {
		switch (portActivity) {
			case 'loading':
				return (
					<Card>
						<CardHeader>
							<CardTitle>Loading</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Commodity</Label>
									<Combobox
										initialValue="iron-ore"
										data={commodities}
										placeholder="Choose commodity"
										onSelect={setSelectedCommodity}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Quantity</Label>
									<Input placeholder="0.000.00" className="bg-background" value="12,500.123" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Unit of measure</Label>
									<Combobox
										initialValue="mts"
										data={units}
										placeholder="Unit"
										search={false}
										onSelect={setSelectedUnit}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">C/P date</Label>
									<DatePicker
										date={cpDate}
										setDate={setCpDate}
										className={datePickerClassName}
										placeholder="dd/mm/yy"
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Laycan from</Label>
									<DatePicker
										date={laycanFrom}
										setDate={setLaycanFrom}
										className={datePickerClassName}
										placeholder="dd/mm/yy"
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Laycan to</Label>
									<DatePicker
										date={laycanTo}
										setDate={setLaycanTo}
										className={datePickerClassName}
										placeholder="dd/mm/yy"
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Charterer</Label>
									<Input placeholder="Enter company" className="bg-background" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">
										Charterer&apos;s nominated agent
									</Label>
									<Input placeholder="Enter company" className="bg-background" />
								</div>
							</div>
						</CardContent>
					</Card>
				);

			case 'discharging':
				return (
					<Card>
						<CardHeader>
							<CardTitle>Discharging</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Commodity</Label>
									<Combobox
										initialValue="iron-ore"
										data={commodities}
										placeholder="Choose commodity"
										onSelect={setSelectedCommodity}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Quantity</Label>
									<Input placeholder="0.000.00" className="bg-background" value="12,500.123" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Unit of measure</Label>
									<Combobox
										initialValue="mts"
										data={units}
										placeholder="Unit"
										search={false}
										onSelect={setSelectedUnit}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">C/P date</Label>
									<DatePicker
										date={cpDate}
										setDate={setCpDate}
										className={datePickerClassName}
										placeholder="dd/mm/yy"
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Charterer</Label>
									<Input placeholder="Enter company" className="bg-background" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">
										Charterer&apos;s nominated agent
									</Label>
									<Input placeholder="Enter company" className="bg-background" />
								</div>
							</div>
						</CardContent>
					</Card>
				);

			case 'bunkering':
				return (
					<Card>
						<CardHeader>
							<CardTitle>Bunkering</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<div className="col-span-3 flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Bunker company</Label>
									<Input placeholder="Enter company" className="bg-background" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Fuel type</Label>
									<Combobox
										initialValue="mgo"
										data={fuelTypes}
										placeholder="Choose fuel type"
										search={false}
										onSelect={setSelectedFuelType}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Fuel quantity</Label>
									<Input type="number" placeholder="0" className="bg-background" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Unit of measure</Label>
									<Combobox
										initialValue="cbm"
										data={units}
										placeholder="Choose unit"
										search={false}
										onSelect={setSelectedUnit}
									/>
								</div>
							</div>
						</CardContent>
					</Card>
				);

			case 'non-commercial':
				return (
					<Card>
						<CardHeader>
							<CardTitle>Non-commercial</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<div className="col-span-3 flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Activity</Label>
									<Combobox
										data={activities}
										placeholder="Choose activity"
										search={false}
										onSelect={setSelectedActivity}
									/>
								</div>

								{renderActivitySection()}
							</div>
						</CardContent>
					</Card>
				);

			case 'canal-transit':
				return (
					<Card>
						<CardHeader>
							<CardTitle>Canal Transit</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Condition</Label>
									<Combobox
										data={canals}
										placeholder="Choose condition"
										search={false}
										onSelect={setSelectedCanal}
									/>
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Direction</Label>
									<Input placeholder="Enter direction" className="bg-background" />
								</div>
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Towage</Label>
									<Combobox
										data={towages}
										placeholder="Choose"
										search={false}
										onSelect={setSelectedTowage}
									/>
								</div>
							</div>
						</CardContent>
					</Card>
				);

			case 'repair':
				return (
					<Card>
						<CardHeader>
							<CardTitle>Repair</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 gap-4">
								<div className="flex flex-col gap-1.5">
									<Label className="text-muted-foreground text-xs">Repair location</Label>
									<Input placeholder="Enter repair location" className="bg-background" />
								</div>
							</div>
						</CardContent>
					</Card>
				);

			default:
				return null;
		}
	};

	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Port Activity</h3>
			<RadioGroup value={portActivity} onValueChange={setPortActivity}>
				<div className="flex flex-wrap gap-2">
					<Badge className="hover:bg-accent/50 hidden cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="empty" id="empty" />
						<Label htmlFor="empty" className="cursor-pointer font-normal">
							Empty
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="loading" id="loading" />
						<Label htmlFor="loading" className="cursor-pointer font-normal">
							Loading
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="discharging" id="discharging" />
						<Label htmlFor="discharging" className="cursor-pointer font-normal">
							Discharging
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="bunkering" id="bunkering" />
						<Label htmlFor="bunkering" className="cursor-pointer font-normal">
							Bunkering
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="non-commercial" id="non-commercial" />
						<Label htmlFor="non-commercial" className="cursor-pointer font-normal">
							Non-commercial
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="canal-transit" id="canal-transit" />
						<Label htmlFor="canal-transit" className="cursor-pointer font-normal">
							Canal transit
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="repair" id="repair" />
						<Label htmlFor="repair" className="cursor-pointer font-normal">
							Repair
						</Label>
					</Badge>
					<Badge className="hover:bg-accent/50 hidden cursor-pointer rounded-full p-1.5" variant="outline">
						<RadioGroupRadioItem value="turnaround" id="turnaround" />
						<Label htmlFor="turnaround" className="cursor-pointer font-normal">
							Turnaround
						</Label>
					</Badge>
				</div>
			</RadioGroup>
			{renderActivityCard()}
		</div>
	);
}
