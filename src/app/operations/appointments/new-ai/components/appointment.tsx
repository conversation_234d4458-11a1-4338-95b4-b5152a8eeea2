import AppointmentHeader from './appointment-header';
import Appointment<PERSON>ener<PERSON> from './appointment-general';
import AppointmentPortActivity from './appointment-port-activity';
import AppointmentNomination from './appointment-nomination';
import AppointmentInstructions from './appointment-instructions';
import AppointmentActivity from './appointment-activity';
import AppointmentFooter from './appointment-footer';
import { Separator } from '@/components/ui/separator';

export default function Appointment() {
	return (
		<div className="flex flex-1 flex-col overflow-hidden">
			<div className="flex w-full flex-col items-center overflow-auto p-4">
				<div className="flex w-full max-w-3xl flex-col gap-6">
					<AppointmentHeader />
					<AppointmentGeneral />
					<Separator />
					<AppointmentPortActivity />
					<Separator />
					<AppointmentNomination />
					<Separator />
					<AppointmentInstructions />
					<Separator />
					<AppointmentActivity />
					<Separator className="bg-transparent" />
				</div>
			</div>
			<AppointmentFooter />
		</div>
	);
}
