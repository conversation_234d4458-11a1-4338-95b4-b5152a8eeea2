import { useState } from 'react';
import { Paperclip, Plus, Trash2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';
import { Separator } from '@/components/ui/separator';

interface Instruction {
	id: string;
	title: string;
	content: string;
}

export default function AppointmentInstructions() {
	const [instructions, setInstructions] = useState<Instruction[]>([{ id: '1', title: '', content: '' }]);

	const addInstruction = () => {
		const newInstruction: Instruction = {
			id: Date.now().toString(),
			title: '',
			content: ''
		};
		setInstructions(prev => [...prev, newInstruction]);
	};

	const removeInstruction = (id: string) => {
		if (instructions.length > 1) {
			setInstructions(prev => prev.filter(instruction => instruction.id !== id));
		}
	};

	const updateInstruction = (id: string, field: 'title' | 'content', value: string) => {
		setInstructions(prev =>
			prev.map(instruction => (instruction.id === id ? { ...instruction, [field]: value } : instruction))
		);
	};

	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Appointment Instructions</h3>
			<Card>
				<CardContent className="space-y-4 pt-6">
					{instructions.map((instruction, index) => (
						<div key={instruction.id}>
							{/* Instruction */}
							<div className="min-h-32">
								<div className="flex items-center gap-2">
									<div className="flex-1">
										<Input
											id={`title-${instruction.id}`}
											placeholder="Enter title"
											value={instruction.title}
											className="m-0 border-none px-0 text-base font-medium shadow-none focus-visible:ring-0 md:text-xl"
											onChange={e => updateInstruction(instruction.id, 'title', e.target.value)}
										/>
									</div>
									{index > 0 && (
										<Button
											variant="ghost"
											size="sm"
											className="text-muted-foreground h-8 w-8 p-0"
											onClick={() => removeInstruction(instruction.id)}
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									)}
								</div>
								<AutosizeTextarea
									placeholder="Write some or type '/' for commands and snippets"
									value={instruction.content}
									className="min-h-12 resize-none border-none bg-transparent px-0 ring-0 outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
									onChange={e => updateInstruction(instruction.id, 'content', e.target.value)}
								/>
							</div>
							<div className="flex items-center gap-2">
								<Button variant="outline" size="sm" className="flex gap-1 bg-transparent">
									<Paperclip className="h-4 w-4" />
									Attach file
								</Button>
							</div>
							{index < instructions.length - 1 && <Separator className="my-4" />}
						</div>
					))}

					<Separator />

					{/* Add instruction button */}
					<Button variant="secondary" size="sm" className="flex items-center gap-1" onClick={addInstruction}>
						<Plus className="h-4 w-4" />
						Add instruction
					</Button>
				</CardContent>
			</Card>
		</div>
	);
}
