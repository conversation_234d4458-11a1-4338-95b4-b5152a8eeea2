import { File } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function AppointmentInstructions() {
	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Appointment Instructions</h3>
			<div className="bg-card grid gap-4 rounded-lg border p-6 text-sm">
				<div className="text-xl font-medium">Instructions title</div>
				<p className="text-sm">
					<PERSON><PERSON><PERSON> magna felis, maximus sed iaculis sit amet, aliquet eu lorem. Curabitur at dictum leo.
					Mauris pharetra lectus metus, eu lacinia massa blandit sed. Sed feugiat ullamcorper dui vel viverra.
					Vivamus consectetur consequat neque sit amet bibendum. Pellentesque efficitur purus imperdiet,
					luctus eros id, laoreet leo. Nam hendrerit pretium urna nec gravida. Vestibulum lacinia fringilla
					nunc a laoreet. Etiam finibus laoreet sem, eget tempus est semper sit amet.
				</p>
				<ul className="my-4 list-disc px-8 text-sm [&>li]:mt-2">
					<li>Ut hendrerit ligula in metus dignissim, non scelerisque diam tincidunt.</li>
					<li>Nam feugiat odio et est ornare, id hendrerit nibh imperdiet.</li>
					<li>Phasellus tincidunt urna at mollis blandit.</li>
					<li>Vivamus in nunc imperdiet, rutrum sapien vitae, lacinia mi.</li>
				</ul>
				<p className="text-sm">Proin sed erat malesuada, hendrerit.</p>
				<p></p>
				<div className="flex flex-col gap-2 sm:flex-row">
					<Button variant="outline" size="sm" className="text-muted-foreground">
						<File />
						Port-Restrictions-2024.pdf
					</Button>
					<Button variant="outline" size="sm" className="text-muted-foreground">
						<File />
						Iron_Ore.pdf
					</Button>
				</div>
			</div>
		</div>
	);
}
