import { Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function AppointmentNomination() {
	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Agent Nomination</h3>
			<div className="bg-card grid gap-4 rounded-lg border p-4 text-sm">
				<div className="flex items-center gap-4">
					<Clock className="size-4 text-amber-500" />
					<div className="flex flex-1 flex-col">
						<div className="text-md font-medium">Noordriver Shipping B.V.</div>
						<div className="text-muted-foreground text-sm"><EMAIL></div>
					</div>
					<div className="flex-1 text-center">
						<Badge variant="outline" className="bg-secondary rounded-full">
							Charterer&apos;s nominated
						</Badge>
					</div>
					<div className="text-muted-foreground flex-1 text-right text-sm">08 Jun 24 - 10:53</div>
				</div>
			</div>
		</div>
	);
}
