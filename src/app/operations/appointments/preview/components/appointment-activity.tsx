import { Clock } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

export default function AppointmentActivity() {
	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Activity</h3>
			<ul className="a-activity-list w-full">
				<li className="flex flex-col">
					<div className="flex flex-row items-center gap-4">
						<Avatar className="h-5 w-5 rounded-sm">
							<AvatarImage src="/static/media/avatar-md.webp" alt="J. Doe" />
							<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
								J.D
							</AvatarFallback>
						</Avatar>
						<div className="flex flex-row items-center gap-2 text-sm">
							<span className="font-medium"><PERSON></span>
							<span className="text-muted-foreground hidden sm:block">appointed agent</span>
							<Badge variant="outline" className="rounded-full">
								<Clock className="size-3 text-amber-500" />
								<span>Pending</span>
							</Badge>
							<span className="text-muted-foreground text-lg">•</span>
							<span className="text-muted-foreground text-xs">23 min ago</span>
						</div>
					</div>
				</li>
			</ul>
		</div>
	);
}
