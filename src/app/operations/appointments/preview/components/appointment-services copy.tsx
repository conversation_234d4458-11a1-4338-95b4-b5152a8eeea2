import { useState } from 'react';
import { Plus, Search } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Label } from '@/components/ui/label';

interface Service {
	id: string;
	label: string;
	checked: boolean;
}

const availableServices: Service[] = [
	{ id: 'restrictions', label: 'Restrictions', checked: false },
	{ id: 'load-discharge-rates', label: 'Load / Discharge rates', checked: false },
	{ id: 'weather-information', label: 'Weather information', checked: false },
	{ id: 'holidays', label: 'Holidays', checked: false },
	{ id: 'cash-to-master', label: 'Cash to master', checked: true },
	{ id: 'crew-change', label: 'Crew change', checked: false },
	{ id: 'spare-parts-delivery', label: 'Spare parts delivery', checked: false },
	{ id: 'underwater-survey', label: 'Underwater survey', checked: false },
	{ id: 'hold-cleaning', label: 'Hold cleaning', checked: false },
	{ id: 'hull-cleaning', label: 'Hull cleaning', checked: false }
];

export default function AppointmentServices() {
	const [showServices, setShowServices] = useState(false);
	const [services, setServices] = useState<Service[]>(availableServices);
	const [searchTerm, setSearchTerm] = useState('');
	const [isPopoverOpen, setIsPopoverOpen] = useState(false);

	const filteredServices = services.filter(service => service.label.toLowerCase().includes(searchTerm.toLowerCase()));

	const handleServiceToggle = (serviceId: string) => {
		setServices(prev =>
			prev.map(service => (service.id === serviceId ? { ...service, checked: !service.checked } : service))
		);
	};

	return (
		<div className="flex w-full flex-col gap-4">
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<h3 className="text-base font-semibold">Additional Services</h3>
					<Switch id="additional-services" checked={showServices} onCheckedChange={setShowServices} />
				</div>
				<p className="text-muted-foreground text-sm">Include or exclude services</p>

				{/* Show / Hide Card */}
				{showServices && (
					<Card>
						<CardContent className="pt-6">
							<div className="flex flex-col gap-4">
								<div className="flex items-center justify-between">
									<p className="text-muted-foreground text-sm">Dropdown / Manage Services</p>
								</div>

								<Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
									<PopoverTrigger asChild>
										<Button
											variant="secondary"
											className="bg-muted hover:bg-muted/80 text-muted-foreground w-fit"
										>
											<Plus className="mr-2 h-4 w-4" />
											Include
										</Button>
									</PopoverTrigger>
									<PopoverContent className="w-80 p-0" align="start">
										<div className="p-4">
											<div className="relative mb-4">
												<Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
												<Input
													placeholder="Search service"
													value={searchTerm}
													className="pl-9"
													onChange={e => setSearchTerm(e.target.value)}
												/>
											</div>
											<div className="space-y-3">
												{filteredServices.map(service => (
													<div key={service.id} className="flex items-center space-x-2">
														<Checkbox
															id={service.id}
															checked={service.checked}
															onCheckedChange={() => handleServiceToggle(service.id)}
														/>
														<Label
															htmlFor={service.id}
															className="text-sm leading-none font-normal peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
														>
															{service.label}
														</Label>
													</div>
												))}
											</div>
										</div>
									</PopoverContent>
								</Popover>

								{/* Display selected services */}
								<div className="space-y-2">
									{services
										.filter(service => service.checked)
										.map(service => (
											<div
												key={service.id}
												className="flex items-center justify-between rounded-md border p-3"
											>
												<span className="text-sm">{service.label}</span>
												<Button
													variant="ghost"
													size="sm"
													className="text-muted-foreground hover:text-foreground h-auto p-1"
													onClick={() => handleServiceToggle(service.id)}
												>
													×
												</Button>
											</div>
										))}
								</div>
							</div>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
