import { ArrowLeftRight, Box } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export default function AppointmentPortActivity() {
	return (
		<div className="flex w-full flex-col gap-4">
			<h3 className="text-base font-semibold">Port Activity</h3>
			<div className="bg-card grid gap-4 rounded-lg border p-4 text-sm">
				<div className="flex items-center gap-2">
					<Box className="text-primary size-4" />
					<span className="text-md font-medium">2,500.000</span>mts
					<Separator className="w-4" />
					<span className="text-md font-medium">Iron Ore</span>
					<Badge variant="outline" className="bg-secondary rounded-full">
						<ArrowLeftRight className="size-3" />
						<span>Loading</span>
					</Badge>
				</div>
				<div className="grid grid-cols-[140px_minmax(0px,1fr)] gap-3 text-sm sm:grid-cols-[140px_minmax(120px,_1fr)_120px_minmax(120px,_1fr)]">
					<div className="text-muted-foreground truncate" title="C/P Date">
						C/P Date
					</div>
					<div>30 Mar 2024</div>
					<div className="text-muted-foreground truncate"></div>
					<div></div>
					<div className="text-muted-foreground truncate" title="Laycan start">
						Laycan start
					</div>
					<div>10 Mar 2024</div>
					<div className="text-muted-foreground truncate" title="Laycan end">
						Laycan end
					</div>
					<div>28 Mar 2024</div>
					<div className="text-muted-foreground truncate" title="Charterer">
						Charterer
					</div>
					<div>Charterer Company S.A.</div>
					<div className="text-muted-foreground truncate" title="Charterer’s nominated agent">
						Charterer’s nominated agent
					</div>
					<div>Charterer Agent S.A.</div>
				</div>
			</div>
		</div>
	);
}
