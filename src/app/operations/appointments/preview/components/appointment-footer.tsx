import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	AlertDialog,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger
} from '@/components/ui/alert-dialog';

export default function AppointmentFooter() {
	const navigate = useNavigate();
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const handleWithdrawAppointment = () => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
			void navigate('/operations/appointments/new-ai');
		}, 500);
	};

	return (
		<div className="bg-panel flex w-full flex-col items-center border-t px-4 shadow-2xl">
			<div className="flex w-full max-w-3xl items-center justify-end gap-2 py-2">
				<AlertDialog>
					<AlertDialogTrigger asChild>
						<Button size="sm">Withdraw</Button>
					</AlertDialogTrigger>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>Withdraw the appointment?</AlertDialogTitle>
							<AlertDialogDescription>
								Withdrawing this appointment will reopen the form for editing. This action does not
								delete your appointment, but it will pause any ongoing processing.
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>Cancel</AlertDialogCancel>
							<Button
								variant="default"
								size="sm"
								disabled={isLoading}
								onClick={handleWithdrawAppointment}
							>
								{isLoading ? (
									<>
										<Loader2 className="h-4 w-4 animate-spin" />
										<span>Withdrawing...</span>
									</>
								) : (
									'Withdraw'
								)}
							</Button>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
			</div>
		</div>
	);
}
