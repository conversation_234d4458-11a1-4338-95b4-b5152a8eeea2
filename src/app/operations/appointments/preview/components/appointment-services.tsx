import { useState } from 'react';
import { Minus, Plus, Trash2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { ComboboxOption } from '@/components/ui/combobox';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Checkbox } from '@/components/ui/checkbox';

interface Service {
	id: string;
	label: string;
	checked: boolean;
}

const serviceTypes: ComboboxOption[] = [
	{ value: 'port-agency', label: 'Port Agency' },
	{ value: 'customs-clearance', label: 'Customs Clearance' },
	{ value: 'cargo-handling', label: 'Cargo Handling' },
	{ value: 'ship-supply', label: 'Ship Supply' },
	{ value: 'crew-services', label: 'Crew Services' },
	{ value: 'technical-services', label: 'Technical Services' }
];

const availableServices: Service[] = [
	{ id: 'restrictions', label: 'Restrictions', checked: false },
	{ id: 'load-discharge-rates', label: 'Load / Discharge rates', checked: false },
	{ id: 'weather-information', label: 'Weather information', checked: false },
	{ id: 'holidays', label: 'Holidays', checked: false },
	{ id: 'cash-to-master', label: 'Cash to master', checked: true },
	{ id: 'crew-change', label: 'Crew change', checked: false },
	{ id: 'spare-parts-delivery', label: 'Spare parts delivery', checked: false },
	{ id: 'underwater-survey', label: 'Underwater survey', checked: false },
	{ id: 'hold-cleaning', label: 'Hold cleaning', checked: false },
	{ id: 'hull-cleaning', label: 'Hull cleaning', checked: false }
];

export default function AppointmentServices() {
	const [showServices, setShowServices] = useState(false);
	const [services, setServices] = useState<Service[]>(availableServices);

	const handleServiceToggle = (serviceId: string) => {
		setServices(prev =>
			prev.map(service => (service.id === serviceId ? { ...service, checked: !service.checked } : service))
		);
	};

	return (
		<div className="flex w-full flex-col gap-4">
			<div className="flex flex-col gap-2">
				<div className="flex items-center gap-2">
					<h3 className="text-base font-semibold">Additional Services</h3>
					<Switch id="additional-services" checked={showServices} onCheckedChange={setShowServices} />
				</div>
				<div className="text-muted-foreground text-sm">Include or exclude services</div>

				{/* Show / Hide Card */}
				{showServices && (
					<Card>
						<CardContent>
							<div className="text-muted-foreground pt-4 text-sm">Include services</div>
							<div className="grid grid-cols-3 gap-4 border-b py-3">
								<div className="flex items-center gap-2">
									<Plus className="size-4 text-emerald-500" />
									<div className="text-sm font-semibold">Cash to master</div>
								</div>
								<div className="col-span-2 flex items-center gap-2">
									<div className="w-full">
										<Input placeholder="Enter description" className="bg-background flex-1" />
									</div>
									<Button variant="ghost" size="icon" className="h-8 w-8">
										<Trash2 className="size-4" />
									</Button>
								</div>
							</div>
							<div className="text-muted-foreground pt-4 text-sm">Exclude services</div>
							<div className="grid grid-cols-3 gap-4 border-b py-3">
								<div className="flex items-center gap-2">
									<Plus className="text-destructive size-4" />
									<div className="text-sm font-semibold">Direct billing</div>
								</div>
								<div className="col-span-2 flex items-center gap-2">
									<div className="w-full">
										<Input placeholder="Enter description" className="bg-background flex-1" />
									</div>
									<Button variant="ghost" size="icon" className="h-8 w-8">
										<Trash2 className="size-4" />
									</Button>
								</div>
							</div>
						</CardContent>
						<CardFooter className="gap-2">
							<Popover>
								<PopoverTrigger asChild>
									<Button variant="secondary" size="sm" className="flex items-center gap-1">
										<Plus className="size-4" />
										Include
									</Button>
								</PopoverTrigger>
								<PopoverContent className="w-64 p-0" align="start">
									<Command>
										<CommandInput placeholder="Search service" />
										<CommandList>
											<CommandEmpty>No services found.</CommandEmpty>
											<CommandGroup>
												{services.map(service => (
													<CommandItem
														key={service.id}
														value={service.label}
														onSelect={() => handleServiceToggle(service.id)}
													>
														<Checkbox
															id={service.id}
															checked={service.checked}
															className="pointer-events-none"
														/>
														<span className="flex-1">{service.label}</span>
													</CommandItem>
												))}
											</CommandGroup>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
							<Popover>
								<PopoverTrigger asChild>
									<Button variant="secondary" size="sm" className="flex items-center gap-1">
										<Minus className="size-4" />
										Exclude
									</Button>
								</PopoverTrigger>
								<PopoverContent className="w-64 p-0" align="start">
									<Command>
										<CommandInput placeholder="Search service" />
										<CommandList>
											<CommandEmpty>No services found.</CommandEmpty>
											<CommandGroup>
												{services.map(service => (
													<CommandItem
														key={service.id}
														value={service.label}
														onSelect={() => handleServiceToggle(service.id)}
													>
														<Checkbox
															id={service.id}
															checked={service.checked}
															className="pointer-events-none"
														/>
														<span className="flex-1">{service.label}</span>
													</CommandItem>
												))}
											</CommandGroup>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
						</CardFooter>
					</Card>
				)}
			</div>
		</div>
	);
}
