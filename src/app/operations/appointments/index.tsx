import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const AppointmentsPage = lazy(() => import('./page'));
const NewAppointmentFormPage = lazy(() => import('./new-form/page'));
const NewAppointmentTextPage = lazy(() => import('./new-text/page'));
const NewAppointmentInlinePage = lazy(() => import('./new-inline/page'));
const NewAppointmentAIPage = lazy(() => import('./new-ai/page'));
const NewAppointmentPreviewPage = lazy(() => import('./preview/page'));
const AppointmentDetailsPage = lazy(() => import('./appointment-details/page'));
const AppointmentSuccessPage = lazy(() => import('./success/page'));

export default function Appointments() {
	return (
		<Routes>
			<Route index element={<AppointmentsPage />} />
			<Route path="new-form" element={<NewAppointmentFormPage />} />
			<Route path="new-text" element={<NewAppointmentTextPage />} />
			<Route path="new-inline" element={<NewAppointmentInlinePage />} />
			<Route path="new-ai" element={<NewAppointmentAIPage />} />
			<Route path=":id/preview" element={<NewAppointmentPreviewPage />} />
			<Route path=":id" element={<AppointmentDetailsPage />} />
			<Route path=":id/success" element={<AppointmentSuccessPage />} />
		</Routes>
	);
}
