import { Box, Clock, Navigation, Anchor, Hammer, Fuel, Briefcase, Hourglass, Pen, HandMetal } from 'lucide-react';

export const portFunctions = [
	{
		value: 'cargo_operations',
		label: 'Cargo ops',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'repairs',
		label: 'Repairs',
		color: 'text-primary',
		icon: Hammer
	},
	{
		value: 'bunkering',
		label: 'Bunkering',
		color: 'text-primary',
		icon: Fuel
	},
	{
		value: 'crew_change',
		label: 'Crew change',
		color: 'text-primary',
		icon: Briefcase
	},
	{
		value: 'shelter_layby',
		label: 'Shelter layby',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'lay_up',
		label: 'Lay up',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'waiting_layby',
		label: 'Waiting layby',
		color: 'text-primary',
		icon: Hourglass
	},
	{
		value: 'dry_docking',
		label: 'Dry docking',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'other',
		label: 'Other',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'documentation',
		label: 'Documentation',
		color: 'text-primary',
		icon: Pen
	},
	{
		value: 'purging',
		label: 'Purging',
		color: 'text-primary',
		icon: HandMetal
	},
	{
		value: 'cruise_call',
		label: 'Cruise call',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'canal_transit',
		label: 'Canal transit',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'delivery_redelivery',
		label: 'Delivery/redelivery',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'clearance',
		label: 'Clearance',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'husbandry',
		label: 'Husbandry',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'cleaning',
		label: 'Cleaning',
		color: 'text-primary',
		icon: Box
	},
	{
		value: 'ace',
		label: 'ACE',
		color: 'text-primary',
		icon: Box
	}
];

export const ports = [
	{
		value: 'Rotterdam, NL',
		label: 'Rotterdam, NL'
	},
	{
		value: 'Hamburg, DE',
		label: 'Hamburg, DE'
	},
	{
		value: 'Antwerp, BE',
		label: 'Antwerp, BE'
	},
	{
		value: 'Southampton, UK',
		label: 'Southampton, UK'
	}
];

export const statuses = [
	{
		value: 'pending',
		label: 'Pending',
		color: 'text-amber-500',
		icon: Clock
	},
	{
		value: 'en-route',
		label: 'En route',
		color: 'text-amber-500',
		icon: Navigation
	},
	{
		value: 'anchored',
		label: 'Anchored',
		color: 'text-blue-500',
		icon: Anchor
	}
];
export const operators = [
	{
		value: 'john',
		label: 'J. Doe',
		short: 'JD',
		avatar: ''
	},
	{
		value: 'robert',
		label: 'R. Smith',
		short: '',
		avatar: '/static/media/avatar-md.webp' // TODO: this needs to come from CDN
	}
];
