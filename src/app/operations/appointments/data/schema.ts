import { z } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const appointmentSchema = z.object({
	fileId: z.string(),
	vesselImo: z.string(),
	vesselName: z.string(),
	portFunction: z.string(),
	portName: z.string(),
	portCountryCode: z.string(),
	eta: z.string(),
	agentName: z.string(),
	status: z.string(),
	operatorName: z.string(),
	requestDate: z.string()
});

export type appointment = z.infer<typeof appointmentSchema>;
