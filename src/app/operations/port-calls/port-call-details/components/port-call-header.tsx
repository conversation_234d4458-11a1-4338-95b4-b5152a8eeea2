import { CircleFlag } from 'react-circle-flags';
import { Box } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function PortCallHeader() {
	return (
		<div className="flex items-center gap-4">
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">mv Baltic Trader</h2>
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					<Box className="text-primary size-3" />
					<span>Cargo ops</span>
				</Badge>
			</div>
			<div className="flex items-center gap-2">
				<CircleFlag countryCode="nl" className="h-4" />
				<span className="text-sm font-medium">Rotterdam, NL</span>
			</div>
		</div>
	);
}
