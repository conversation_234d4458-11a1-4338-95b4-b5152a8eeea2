import { formatDateFullDay, formatTime } from '@/common/utils/formatUtils';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const events = [
	{
		id: '1',
		event: 'Loading ',
		type: 'default',
		dateFrom: '2024-11-14T09:15:00.000Z',
		dateTo: '2024-11-14T14:30:00.000Z',
		cargo: 'Iron Ore',
		cargoQty: '25,000.000',
		cargoUnit: 'mts',
		holds: 2,
		crane: 'LHM06',
		gangs: 2
	},
	{
		id: '2',
		event: 'Rain',
		type: 'default',
		dateFrom: '2024-11-14T14:30:00.000Z',
		dateTo: '2024-11-14T16:45:00.000Z',
		cargo: '',
		cargoQty: '',
		cargoUnit: '',
		holds: '',
		crane: '',
		gangs: ''
	},
	{
		id: '3',
		event: 'Loading ',
		type: 'default',
		dateFrom: '2024-11-14T16:45:00.000Z',
		dateTo: '2024-11-15T08:30:00.000Z',
		cargo: 'Iron Ore',
		cargoQty: '15,000.000',
		cargoUnit: 'mts',
		holds: 2,
		crane: 'LHM06',
		gangs: 2
	}
];

export default function SofOperations() {
	return (
		<div className="flex w-full flex-col items-center gap-4">
			<h3 className="w-full max-w-3xl text-base font-semibold">Operations Log</h3>
			<div className="w-full max-w-6xl">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className="w-[130px] border-t">Date from</TableHead>
							<TableHead className="w-[70px] border-t">From</TableHead>
							<TableHead className="w-[130px] border-t">Date to</TableHead>
							<TableHead className="w-[70px] border-t">To</TableHead>
							<TableHead className="border-t">Event</TableHead>
							<TableHead className="border-t">Cargo</TableHead>
							<TableHead className="border-t">Quantity</TableHead>
							<TableHead className="w-[70px] border-t text-center">Holds</TableHead>
							<TableHead className="w-[70px] border-t text-center">Crane</TableHead>
							<TableHead className="w-[70px] border-t text-center">Gangs</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{events.map(event => (
							<TableRow key={event.id}>
								<TableCell>
									<span className="text-muted-foreground">{formatDateFullDay(event.dateFrom)}</span>
								</TableCell>
								<TableCell>{formatTime(event.dateFrom)}</TableCell>
								<TableCell>
									<span className="text-muted-foreground">{formatDateFullDay(event.dateTo)}</span>
								</TableCell>
								<TableCell>{formatTime(event.dateTo)}</TableCell>
								<TableCell>{event.event}</TableCell>
								<TableCell>{event.cargo}</TableCell>
								<TableCell>
									{event.cargoQty} {event.cargoUnit}
								</TableCell>
								<TableCell className="text-center">{event.holds}</TableCell>
								<TableCell className="text-center">{event.crane}</TableCell>
								<TableCell className="text-center">{event.gangs}</TableCell>
							</TableRow>
						))}
					</TableBody>
					<TableFooter>
						<TableRow>
							<TableCell colSpan={10} className="text-right">
								<div className="flex justify-end gap-6">
									<div>Total cargo quantity</div>
									<div className="font-bold">40,000.000 mts</div>
								</div>
							</TableCell>
						</TableRow>
					</TableFooter>
				</Table>
			</div>
		</div>
	);
}
