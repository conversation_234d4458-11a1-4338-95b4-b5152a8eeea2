import { Separator } from '@/components/ui/separator';

export default function SofRemarks() {
	return (
		<div className="flex w-full flex-col items-center">
			<div className="grid w-full max-w-3xl gap-4">
				<h3 className="text-base font-semibold">Agent&apos;s Remarks</h3>
				<div className="grid gap-4 py-4">
					<p className="text-sm leading-6">Cargo was stowed in an open area. Partly wet and rusty.</p>
					<p className="text-sm leading-6">
						<b>Note:</b>
						<br></br>
						Ensure timely receipt of vessel notifications, including ETA and Notice of Readiness (NOR).
						Verify vessel compliance with ISPS requirements and other port-specific restrictions. Confirm
						pre-funding of port disbursements by Owners to avoid delays.
					</p>
					<div className="my-4 text-sm">
						<b>Loading operations:</b>
						<ul className="list-disc px-8 text-sm [&amp;>li]:mt-2">
							<li>Monitor adherence to agreed loading rates (e.g., 3,500 MT/day SSHINC).</li>
							<li>Coordinate loading sequence if multiple grades or splits are involved.</li>
							<li>Verify hold cleanliness and report surveyor findings to Shippers/Charterers.</li>
							<li>Note any deviations in ballast pumping schedule that may cause delays.</li>
						</ul>
					</div>
				</div>
				<Separator />
			</div>
		</div>
	);
}
