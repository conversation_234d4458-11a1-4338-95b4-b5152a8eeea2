import { Pen<PERSON><PERSON><PERSON><PERSON>, Timer } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export default function SofActivity() {
	return (
		<div className="flex w-full flex-col items-center">
			<div className="grid w-full max-w-3xl gap-4">
				<h3 className="text-base font-semibold">Activity</h3>
				<ul className="a-activity-list p-2">
					<li className="flex flex-col">
						<div className="flex flex-row items-center gap-4">
							<Avatar className="h-5 w-5 rounded-sm">
								<AvatarImage src="" alt="J. Doe" />
								<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
									J.D
								</AvatarFallback>
							</Avatar>
							<div className="flex flex-row items-center gap-2 text-sm">
								<span className="font-medium"><PERSON></span>
								<span className="text-muted-foreground hidden sm:block">created a SOF</span>
								<Badge variant="outline" className="rounded-full">
									<PencilLine className="text-muted-foreground h-3 w-3" />
									<span>Draft</span>
								</Badge>
								<span className="text-muted-foreground text-lg">•</span>
								<span className="text-muted-foreground text-xs">3 days ago</span>
							</div>
						</div>
						<div className="relative flex min-h-4 flex-row">
							<Separator orientation="vertical" className="a-list-sep absolute left-[9px]"></Separator>
						</div>
					</li>
					<li className="flex flex-col">
						<div className="flex flex-row items-center gap-4">
							<div className="flex size-5 items-center justify-center">
								<Timer className="text-muted-foreground h-4 w-4" />
							</div>
							<div className="flex flex-row items-center gap-2 text-sm">
								<span className="font-medium">John Doe</span>
								<span className="text-muted-foreground hidden sm:block">added a SOF event</span>
								<span>Arrived roads</span>
								<span className="text-muted-foreground text-lg">•</span>
								<span className="text-muted-foreground text-xs">3 days ago</span>
							</div>
						</div>
						<div className="relative flex min-h-4 flex-row">
							<Separator orientation="vertical" className="a-list-sep absolute left-[9px]"></Separator>
						</div>
					</li>
					<li className="flex flex-col">
						<div className="flex flex-row items-center gap-4">
							<div className="flex size-5 items-center justify-center">
								<Timer className="text-muted-foreground h-4 w-4" />
							</div>
							<div className="flex flex-row items-center gap-2 text-sm">
								<span className="font-medium">John Doe</span>
								<span className="text-muted-foreground hidden sm:block">added a SOF event</span>
								<span>NOR tendered</span>
								<span className="text-muted-foreground text-lg">•</span>
								<span className="text-muted-foreground text-xs">3 days ago</span>
							</div>
						</div>
						<div className="relative flex min-h-4 flex-row">
							<Separator orientation="vertical" className="a-list-sep absolute left-[9px]"></Separator>
						</div>
					</li>
				</ul>
			</div>
		</div>
	);
}
