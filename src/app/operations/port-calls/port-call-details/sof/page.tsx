import { useState } from 'react';
import { BarChartBig } from 'lucide-react';
import Sof from './components/sof';
import SofInsights from './components/sof-insights';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger, SidebarProvider, Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';

export default function Page() {
	const [open, setOpen] = useState(false);

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls">Port calls</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls/ABX-123A">
									mv Baltic Trader (#ABX-123A)
								</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>Statement of facts</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex gap-2 px-4">
					<ModeToggle />
					<Button variant="outline" onClick={() => setOpen(open => !open)}>
						<BarChartBig />
						<span>Insights</span>
					</Button>
				</div>
			</header>

			<SidebarProvider
				style={{ '--sidebar-width': '25rem' } as React.CSSProperties}
				open={open}
				className="relative flex min-h-0 flex-1 flex-row overflow-hidden pt-4"
				onOpenChange={setOpen}
			>
				<Sof />
				<Sidebar variant="floating" side="right">
					<SidebarContent className="bg-panel flex flex-col gap-0">
						<SofInsights />
					</SidebarContent>
				</Sidebar>
			</SidebarProvider>
		</>
	);
}
