import { useState } from 'react';
import { PanelRight } from 'lucide-react';
import PortCall from './components/port-call';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger, SidebarProvider, Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { ModeToggle } from '@/components/theme/mode-toggle';
import { Button } from '@/components/ui/button';

export default function Page() {
	const [open, setOpen] = useState(false);

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls">Port calls</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>
									mv Baltic Trader <span className="text-muted-foreground">(#ABX-123A)</span>
								</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<div className="flex gap-2 px-4">
					<ModeToggle />
					<Button variant="outline" onClick={() => setOpen(open => !open)}>
						<span>Port call info</span>
						<PanelRight />
					</Button>
				</div>
			</header>

			<SidebarProvider
				style={{ '--sidebar-width': '25rem' } as React.CSSProperties}
				open={open}
				className="relative flex min-h-0 flex-1 flex-row overflow-hidden pt-4"
				onOpenChange={setOpen}
			>
				<PortCall />
				<Sidebar variant="custom" side="right">
					<SidebarContent className="bg-panel flex flex-col gap-0">
						<div className="flex flex-row items-center p-6">
							<h3 className="flex-1 text-base font-semibold">Port Call Info Opi</h3>
							<SidebarTrigger className="-ml-1" />
						</div>
						<div className="flex flex-1 flex-col gap-2 overflow-auto p-4">
							<p className="text-muted-foreground px-2 text-sm">Port call info will be displayed here.</p>
						</div>
					</SidebarContent>
				</Sidebar>
			</SidebarProvider>
		</>
	);
}
