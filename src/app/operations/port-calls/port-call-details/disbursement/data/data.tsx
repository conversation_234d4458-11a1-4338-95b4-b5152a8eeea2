import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ine, <PERSON><PERSON><PERSON><PERSON>, FileChartColumn, RefreshCcw } from 'lucide-react';

export const services = [
	{
		id: '1',
		service: 'Agency fee',
		costCenter: 'Agency services',
		vendor: 'Lead Agent Ltd.',
		currency: 'USD',
		pdaPrice: '1,500.00',
		fdaPrice: '1,500.00',
		aiPrice: '1,500.00',
		varianceAmount: 0.0,
		variance: 0.0,
		issue: 'Recoverable',
		issueType: 'recoverable',
		aiRef: 'dataRecoverable',
		comments: 2,
		attachments: 1
	},
	{
		id: '2',
		service: 'Pilotage',
		costCenter: 'Pilotage',
		vendor: 'Pilotage Services',
		currency: 'USD',
		pdaPrice: '2,500.00',
		fdaPrice: '2,750.00',
		aiPrice: '2,500.00',
		varianceAmount: 250.0,
		variance: 10.0,
		issue: 'Tariff Mismatch',
		issueType: 'alert',
		aiRef: 'dataPilotage',
		comments: 0,
		attachments: 2
	},
	{
		id: '3',
		service: 'Towage',
		costCenter: 'Towage',
		vendor: 'Towage Services',
		currency: 'USD',
		pdaPrice: '4,000.00',
		fdaPrice: '4,900.00',
		aiPrice: '4,000.00',
		varianceAmount: 900.0,
		variance: 12.5,
		issue: 'Overcharge',
		issueType: 'alert',
		aiRef: 'dataTowage',
		comments: 0,
		attachments: 1
	},
	{
		id: '4',
		service: 'Harbour dues',
		costCenter: 'Port dues',
		vendor: 'Berthing Services SA',
		currency: 'USD',
		pdaPrice: '780.00',
		fdaPrice: '780.00',
		aiPrice: '780.00',
		varianceAmount: 0.0,
		variance: 0.0,
		issue: '',
		issueType: '',
		comments: 0,
		attachments: 1
	},
	{
		id: '5',
		service: 'Shifting',
		costCenter: 'Towage',
		vendor: 'Shifting Services',
		currency: 'USD',
		pdaPrice: '500.00',
		fdaPrice: '800.00',
		aiPrice: '500.00',
		varianceAmount: 300.0,
		variance: 12.0,
		issue: 'Overcharge',
		issueType: 'alert',
		aiRef: 'dataShifting',
		comments: 3,
		attachments: 1
	},
	{
		id: '6',
		service: 'Harbour dues',
		costCenter: 'Port dues',
		vendor: 'Berthing Services SA',
		currency: 'USD',
		pdaPrice: '1,000.00',
		fdaPrice: '1,000.00',
		aiPrice: '1,000.00',
		varianceAmount: 0.0,
		variance: 0.0,
		issue: '',
		issueType: '',
		comments: 0,
		attachments: 1
	},
	{
		id: '7',
		service: 'Pilotage fees',
		costCenter: 'Pilotage',
		vendor: 'Pilotage Services',
		currency: 'USD',
		pdaPrice: '1,200.00',
		fdaPrice: '1,300.00',
		aiPrice: '1,200.00',
		varianceAmount: 100.0,
		variance: 1.0,
		issue: 'Wrong Payment',
		issueType: 'warning',
		aiRef: 'dataWrongPayment',
		comments: 0,
		attachments: 1
	},
	{
		id: '8',
		service: 'Mooring',
		costCenter: 'Port dues',
		vendor: 'Mooring Services',
		currency: 'USD',
		pdaPrice: '500.00',
		fdaPrice: '500.00',
		aiPrice: '500.00',
		varianceAmount: 0.0,
		variance: 0.0,
		issue: '',
		issueType: '',
		comments: 1,
		attachments: 1
	},
	{
		id: '9',
		service: 'Unmooring',
		costCenter: 'Port dues',
		vendor: 'Mooring Services Ltd.',
		currency: 'USD',
		pdaPrice: '1,000.00',
		fdaPrice: '1,000.00',
		aiPrice: '1,000.00',
		varianceAmount: 0.0,
		variance: 0.0,
		issue: '',
		issueType: '',
		comments: 0,
		attachments: 1
	}
];

export const chat = {
	responses: [
		{
			id: '1',
			text: `This is a **Final Disbursement Account** for **mv Baltic Trader** calling at the port of **Rotterdam**. It details total port expenses of **$23,169**, comprised of **12 services**.

I've identified **4 issues**, representing **potential savings of $4,253**.
			`,
			type: 'greeting',
			suggestions: [
				{
					id: '1',
					icon: FileChartColumn,
					color: 'text-primary',
					refResponse: 'benchmark',
					text: 'Benchmark'
				},
				{
					id: '2',
					icon: AlertTriangle,
					color: 'text-destructive',
					refResponse: 'issues',
					text: 'Issues (4)'
				},
				{
					id: '3',
					icon: ChartLine,
					color: 'text-sky-500',
					refResponse: 'performance-score',
					text: 'Performance'
				}
			]
		},
		{
			id: 'issues',
			text: `Based on my analysis, I have identified **4 issues** with the disbursement. You can potentially save up to **$4,253.00**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'pilotage-tariff-mismatch',
					type: 'alert',
					amount: '2,500.00',
					title: 'Pilotage Tariff Mismatch',
					excerpt: '',
					text: ''
				},
				{
					id: '2',
					refResponse: 'towage-overcharge',
					type: 'alert',
					amount: '4,000.00',
					title: 'Towage Overcharge'
				},
				{
					id: '3',
					refResponse: 'shifting-overcharge',
					type: 'alert',
					amount: '500.00',
					title: 'Shifting Overcharge'
				},
				{
					id: '4',
					refResponse: 'pilotage-overcharge',
					type: 'warning',
					amount: '1,200.00',
					title: 'Pilotage Wrong Payment'
				}
			],
			suggestions: [
				{
					id: '1',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes from the agent'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};

export const dataChecks = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, all checks have been completed successfully.

> * ✅ Check 1 - Checked
> * ✅ Check 2 - Checked
> * ✅ Check 3 - Checked
> * ✅ Check 4 - Checked
			`,
			type: '',
			suggestions: [
				{
					id: '1',
					icon: CheckCheck,
					color: '',
					refResponse: 'approve',
					text: 'Approve DA'
				}
			]
		}
	]
};

export const dataRecoverable = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, I have identified that Agency fee is marked as **Recoverable**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'agency-fee',
					type: 'recoverable',
					amount: '1,500.00',
					title: 'Agency Fee Recoverable',
					excerpt: 'Recoverable amount is $1,500.00.',
					text: ''
				}
			]
		}
	]
};

export const dataPilotage = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, I have identified that Pilotage is marked as **Tariff Mismatch**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'pilotage-tariff-mismatch',
					type: 'alert',
					amount: '2,500.00',
					title: 'Pilotage Tariff Mismatch',
					excerpt: 'Billed Rate Code for 50k GRT. Vessel GRT is 45k, requires Rate Code P02 ($3800)',
					text: ''
				}
			],
			suggestions: [
				{
					id: '1',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes from the agent'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};

export const dataTowage = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, I have identified that Towage is marked as **Overcharge**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'towage-overcharge',
					type: 'alert',
					amount: '2,500.00',
					title: 'Towage Overcharge',
					excerpt: 'Billed Rate Code for 50k GRT. Vessel GRT is 45k, requires Rate Code P02 ($3800)',
					text: `Owners warrant that the vessel can load and sail without any bagging. Strapping
and/or securing of the cargo, if any, to be done by the Owners at their time, risk
and expense.`
				}
			],
			suggestions: [
				{
					id: '1',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes from the agent'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};

export const dataShifting = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, I have identified that Shifting is marked as **Overcharge**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'shifting-overcharge',
					type: 'alert',
					amount: '2,500.00',
					title: 'Shifting Overcharge',
					excerpt: 'Billed Rate Code for 50k GRT. Vessel GRT is 45k, requires Rate Code P02 ($3800)',
					text: ''
				}
			],
			suggestions: [
				{
					id: '1',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes from the agent'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};

export const dataWrongPayment = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, I have identified that Pilotage fees is marked as **Wrong Payment**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'pilotage-wrong-payment',
					type: 'warning',
					amount: '1,200.00',
					title: 'Pilotage Wrong Payment',
					excerpt: 'Wrong payment amount is $1,200.00.',
					text: ''
				}
			],
			suggestions: [
				{
					id: '1',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes from the agent'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};

export const dataAllIssues = {
	responses: [
		{
			id: '1',
			text: `Based on my analysis, I have identified **4 issues** with the disbursement. You can potentially save up to **$4,253.00**.`,
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'pilotage-tariff-mismatch',
					type: 'alert',
					amount: '2,500.00',
					title: 'Pilotage Tariff Mismatch',
					excerpt: '',
					text: ''
				},
				{
					id: '2',
					refResponse: 'towage-overcharge',
					type: 'alert',
					amount: '4,000.00',
					title: 'Towage Overcharge'
				},
				{
					id: '3',
					refResponse: 'shifting-overcharge',
					type: 'alert',
					amount: '500.00',
					title: 'Shifting Overcharge'
				},
				{
					id: '4',
					refResponse: 'pilotage-overcharge',
					type: 'warning',
					amount: '1,200.00',
					title: 'Pilotage Wrong Payment'
				}
			],
			suggestions: [
				{
					id: '1',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes from the agent'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};

export const dataSingleIssue = {
	responses: [
		{
			id: '1',
			text: '',
			type: '',
			issues: [
				{
					id: '1',
					refResponse: 'pilotage-tariff-mismatch',
					type: 'alert',
					amount: '2,500.00',
					title: 'Pilotage Tariff Mismatch',
					excerpt: '',
					text: `*Billed Rate Code for 50k GRT. Vessel GRT is 45k,
requires Rate Code 'P02' ($3800).*

Owners warrant that the vessel can load and sail without any bagging. Strapping
and/or securing of the cargo, if any, to be done by the Owners at their time, risk
and expense.

Please request maximum safe quantity of cargo try close to 7000 mts

In case final cargo quantity loaded is less than requested, please issue a dead-
freight claim`
				}
			],
			suggestions: [
				{
					id: '1',
					icon: BookOpenCheck,
					color: '',
					refResponse: 'view-clause-7',
					text: 'View clause #7'
				},
				{
					id: '2',
					icon: RefreshCcw,
					color: '',
					refResponse: 'request-changes',
					text: 'Request changes'
				}
			]
		},
		{
			id: 'request-changes',
			text: 'Let me prepare a draft request email for you.',
			type: 'email'
		}
	]
};
