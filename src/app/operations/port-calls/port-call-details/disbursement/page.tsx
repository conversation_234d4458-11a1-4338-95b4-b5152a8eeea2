import { useState } from 'react';
import { useNavigate } from 'react-router';
import { Download, <PERSON>, Sparkles, ChartLine } from 'lucide-react';
import { chat } from './data/data';
import Disbursement from './components/disbursement';
import DisbursementServicePanel from './components/disbursement-service-panel';
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger, SidebarProvider, Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import Chat from '@/app/mapbox/components/chat/chat';
import { ChatData } from '@/app/mapbox/components/chat/context/chat-context';

export default function Page() {
	const [openServicePanel, setOpenServicePanel] = useState(false);
	const [openAiPanel, setOpenAiPanel] = useState(false);
	const [openRefPanel, setOpenRefPanel] = useState(false);
	const [currentChatData, setCurrentChatData] = useState<ChatData>(chat);
	const navigate = useNavigate();

	return (
		<>
			<header className="flex h-16 shrink-0 items-center justify-between gap-2">
				<div className="flex items-center gap-2 px-4">
					<SidebarTrigger className="-ml-1" />
					<Separator orientation="vertical" className="mr-2 h-4" />
					<Breadcrumb>
						<BreadcrumbList>
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls">Port calls</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbLink href="/operations/port-calls/ABX-123A">
									mv Baltic Trader (#ABX-123A)
								</BreadcrumbLink>
							</BreadcrumbItem>
							<BreadcrumbSeparator />
							<BreadcrumbItem>
								<BreadcrumbPage>
									Final DA <span className="text-muted-foreground">(#DA-019-MNT)</span>
								</BreadcrumbPage>
							</BreadcrumbItem>
						</BreadcrumbList>
					</Breadcrumb>
				</div>
				<TooltipProvider delayDuration={100}>
					<div className="flex items-center justify-end gap-2 px-4">
						<div className="hidden items-center justify-end gap-2">
							<Tooltip>
								<TooltipTrigger asChild>
									<Button size="icon" variant="ghost" className="text-muted-foreground h-7 w-7">
										<ChartLine />
									</Button>
								</TooltipTrigger>
								<TooltipContent>Benchmark</TooltipContent>
							</Tooltip>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button size="icon" variant="ghost" className="text-muted-foreground h-7 w-7">
										<Files />
									</Button>
								</TooltipTrigger>
								<TooltipContent>Invoices</TooltipContent>
							</Tooltip>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button size="icon" variant="ghost" className="text-muted-foreground h-7 w-7">
										<Download />
									</Button>
								</TooltipTrigger>
								<TooltipContent>Download</TooltipContent>
							</Tooltip>
							<Separator orientation="vertical" className="mx-2 h-4" />
						</div>
						<Button
							variant="ghost"
							size="xs"
							onClick={() => {
								setCurrentChatData(chat);
								setOpenAiPanel(true);
								setOpenServicePanel(false);
							}}
						>
							<Sparkles />
							<span>Operator One</span>
						</Button>
					</div>
				</TooltipProvider>
			</header>
			<SidebarProvider
				style={{ '--sidebar-width': '28rem' } as React.CSSProperties}
				open={openAiPanel || openServicePanel || openRefPanel}
				className="relative flex min-h-0 flex-1 flex-row overflow-hidden pt-4"
			>
				<Disbursement
					setAiData={data => {
						setCurrentChatData(data);
						setOpenRefPanel(true);
						setOpenAiPanel(false);
						setOpenServicePanel(false);
					}}
					isServicePanelOpen={openServicePanel}
					setAllIssuesData={data => {
						setCurrentChatData(data);
						setOpenAiPanel(true);
						setOpenServicePanel(false);
						setOpenRefPanel(false);
					}}
					onOpenServicePanel={() => {
						setOpenServicePanel(open => !open);
						setOpenAiPanel(false);
						setOpenRefPanel(false);
					}}
				/>

				<Sidebar variant="custom" side="right">
					<SidebarContent className="bg-panel/75 flex flex-col gap-0">
						{openAiPanel && (
							<Chat
								key={JSON.stringify(currentChatData)} // Add a key to force re-render when data changes
								chatId="disbursement-123"
								data={currentChatData}
								backButton={false}
								chatTitle="mv Baltic Trader AI Panel"
								collapseButton
								expandButton
								onExpandPanel={() => void navigate('chat')}
								onOpenVoyagePanel={() => setOpenAiPanel(open => !open)}
							/>
						)}
						{openServicePanel && (
							<DisbursementServicePanel
								isOpen={openServicePanel}
								setAiData={data => {
									setCurrentChatData(data);
									setOpenRefPanel(true);
									setOpenAiPanel(false);
									setOpenServicePanel(false);
								}}
								onOpenRefPanel={() => {
									setOpenRefPanel(true);
									setOpenAiPanel(false);
									setOpenServicePanel(false);
								}}
								onClose={() => setOpenServicePanel(false)}
							/>
						)}
						{openRefPanel && (
							<Chat
								key={JSON.stringify(currentChatData)} // Add a key to force re-render when data changes
								chatId="disbursement-321"
								data={currentChatData}
								backButton
								chatTitle="mv Baltic Trader Ref Panel"
								collapseButton={false}
								expandButton
								onExpandPanel={() => void navigate('chat')}
								onOpenVoyagePanel={() => {
									setOpenServicePanel(true);
									setOpenRefPanel(false);
									setOpenAiPanel(false);
								}}
							/>
						)}
					</SidebarContent>
				</Sidebar>
			</SidebarProvider>
		</>
	);
}
