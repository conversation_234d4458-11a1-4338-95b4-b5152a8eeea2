import DisbursementActivity from './disbursement-activity';
import DisbursementCosts from './disbursement-costs';
import DisbursementFooter from './disbursement-footer';
import DisbursementGeneral from './disbursement-general';
import DisbursementHeader from './disbursement-header';
import DisbursementPaymentDetails from './disbursement-payment-details';
import { Separator } from '@/components/ui/separator';
import { ChatData } from '@/app/mapbox/components/chat/context/chat-context';

interface Props {
	onOpenServicePanel?: () => void;
	setAiData?: (data: ChatData) => void;
	setAllIssuesData?: (data: ChatData) => void;
	isServicePanelOpen?: boolean;
}

export default function Disbursement({ onOpenServicePanel, setAiData, setAllIssuesData, isServicePanelOpen }: Props) {
	return (
		<div className="flex flex-1 flex-col">
			<div className="flex w-full flex-1 flex-col items-center gap-6 overflow-auto p-4">
				<DisbursementHeader />
				<DisbursementGeneral setAllIssuesData={setAllIssuesData} />
				<Separator className="max-w-3xl" />
				<DisbursementCosts
					setAiData={setAiData}
					isServicePanelOpen={isServicePanelOpen}
					onOpenServicePanel={onOpenServicePanel}
				/>
				<Separator className="max-w-3xl bg-transparent" />
				<DisbursementPaymentDetails />
				<Separator className="max-w-3xl" />
				<DisbursementActivity />
			</div>
			<DisbursementFooter />
		</div>
	);
}
