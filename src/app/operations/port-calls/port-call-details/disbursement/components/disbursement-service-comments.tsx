import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const comments = [
	{
		id: '1',
		name: '<PERSON><PERSON>',
		comment: 'Aranea eum crux convoco cui sto acies. Amplitudo despecto vobis.',
		date: '3d ago'
	}
];

export default function DisbursementServiceComments() {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="grid gap-2 px-4">
			<div className="text-muted-foreground text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Comments
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-1">
							{comments.map(item => (
								<div key={item.id} className="flex flex-col gap-2 px-2 text-sm">
									<div className="flex items-center gap-2">
										<Avatar className="h-5 w-5 rounded-sm">
											<AvatarImage src="" alt="" />
											<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
												MP
											</AvatarFallback>
										</Avatar>
										<span className="text-medium">{item.name}</span>
										<span className="text-muted-foreground text-lg">•</span>
										<span className="text-muted-foreground">{item.date}</span>
									</div>
									<div className="text-sm">
										<i>{item.comment}</i>
									</div>
								</div>
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
