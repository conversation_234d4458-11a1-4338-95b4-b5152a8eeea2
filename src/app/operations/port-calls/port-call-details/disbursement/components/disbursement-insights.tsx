import { PanelRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Props {
	onOpen?: () => void;
}

export default function DisbursementInsights({ onOpen }: Props) {
	return (
		<>
			<div className="flex flex-row items-center p-6">
				<h3 className="flex-1 text-base font-semibold">Insights</h3>
				<Button variant="outline" onClick={onOpen}>
					<PanelRight />
				</Button>
			</div>
			<div className="flex flex-1 flex-col gap-2 overflow-auto p-4">
				<p className="text-muted-foreground px-2 text-sm">Disbursement insights will be displayed here.</p>
			</div>
		</>
	);
}
