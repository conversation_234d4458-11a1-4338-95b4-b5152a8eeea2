import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { TriangleAlert, ChevronDown, ChevronRight, CircleAlert, CheckCheck } from 'lucide-react';
import { dataChecks } from '../data/data';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChatData } from '@/app/mapbox/components/chat/context/chat-context';

const issues = [
	{
		id: '1',
		issue: 'SOF Check',
		checks: '9/9',
		description: '',
		type: 'checked'
	},
	{
		id: '2',
		issue: 'C/P Check',
		checks: '4/4',
		description: '',
		type: 'checked'
	},
	{
		id: '3',
		issue: 'Port Tariffs Check',
		checks: '6/6',
		description: 'Tariff Mismatch',
		type: 'alert'
	},
	{
		id: '4',
		issue: 'Payment Check',
		checks: '3/4',
		description: 'Wrong Payment',
		type: 'warning'
	}
];

interface Props {
	setAiData?: (data: ChatData) => void;
	onOpenRefPanel?: () => void;
}

export default function DisbursementServiceAiCheck({ setAiData, onOpenRefPanel }: Props) {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="grid gap-2 px-4">
			<div className="text-muted-foreground text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					AI Check
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-1">
							{issues.map(item => (
								<Button
									key={item.id}
									variant="ghost"
									className="group/issue h-auto justify-start bg-transparent px-2"
									onClick={() => {
										setAiData?.(dataChecks);
										onOpenRefPanel?.();
									}}
								>
									{item.type === 'warning' && <CircleAlert className="text-amber-500" />}
									{item.type === 'alert' && <TriangleAlert className="text-red-500" />}
									{item.type === 'checked' && <CheckCheck className="text-emerald-500" />}
									<div className="flex flex-1 items-center gap-2 text-left text-wrap">
										{item.issue}
										{item.checks && (
											<Badge
												variant="secondary"
												className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs opacity-0 transition-opacity group-hover/issue:opacity-100"
											>
												{item.checks}
											</Badge>
										)}
									</div>
									{item.description && (
										<div className="text-muted-foreground font-normal text-wrap">
											{item.description}
										</div>
									)}
									<ChevronRight className="text-muted-foreground opacity-0 transition-opacity group-hover/issue:opacity-100" />
								</Button>
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
