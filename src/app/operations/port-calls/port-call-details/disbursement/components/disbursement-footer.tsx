import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	DialogClose
} from '@/components/ui/dialog';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';

export default function DisbursementFooter() {
	const requestMessageBody = `Dear [Port Agent's Name],

I am writing to request changes to the disbursement for mv Suez Navigator. 

I have identified 4 issues with the disbursement.

Thank you for your attention to this matter.`;

	const approveMessageBody = `Dear [Port Agent's Name],

I am writing to approve the disbursement for mv Suez Navigator. 

Thank you for your attention to this matter.`;

	const [requestMessage, setRequestMessage] = useState(requestMessageBody);
	const [approveMessage, setApproveMessage] = useState(approveMessageBody);

	return (
		<div className="bg-panel flex w-full flex-col items-center border-t px-4 shadow-2xl">
			<div className="flex w-full max-w-3xl items-center justify-end gap-2 py-2">
				<Dialog>
					<DialogTrigger asChild>
						<Button variant="secondary" size="sm">
							Request Changes
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Request Changes</DialogTitle>
							<DialogDescription>Subtitle/info text</DialogDescription>
						</DialogHeader>
						<AutosizeTextarea
							placeholder="Write a message"
							value={requestMessage}
							onChange={e => setRequestMessage(e.target.value)}
						/>

						<DialogFooter>
							<DialogClose asChild>
								<Button type="button" variant="secondary">
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit">Request</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
				<Dialog>
					<DialogTrigger asChild>
						<Button variant="default" size="sm">
							Approve
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>Approve</DialogTitle>
							<DialogDescription>Subtitle/info text</DialogDescription>
						</DialogHeader>
						<AutosizeTextarea
							placeholder="Write a message"
							value={approveMessage}
							onChange={e => setApproveMessage(e.target.value)}
						/>

						<DialogFooter>
							<DialogClose asChild>
								<Button type="button" variant="secondary">
									Cancel
								</Button>
							</DialogClose>
							<Button type="submit" variant="default">
								Approve
							</Button>
						</DialogFooter>
					</DialogContent>
				</Dialog>
			</div>
		</div>
	);
}
