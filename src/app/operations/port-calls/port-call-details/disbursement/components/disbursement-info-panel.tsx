import { useState } from 'react';
import { PanelLeft, ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';

interface Props {
	onClose?: () => void;
}

export default function DisbursementInfoPanel({ onClose }: Props) {
	const [expanded, setExpanded] = useState(false);

	return (
		<>
			<div className="flex h-16 items-center gap-3 p-4">
				<div className="flex-1 px-2 leading-none font-semibold">
					Final DA <span className="text-muted-foreground text-sm font-normal">(#DA-019-MNT)</span>
				</div>
				<div className="flex items-center gap-2">
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									size="icon"
									variant="ghost"
									className="text-muted-foreground h-7 w-7"
									onClick={onClose}
								>
									<PanelLeft />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Collapse</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
			</div>
			<div className="grid gap-6 py-6">
				<div className="px-6">
					<div className="text-muted-foreground text-sm">
						<Button
							variant="ghost"
							className="h-auto gap-1 px-2 py-1 font-normal"
							onClick={() => {
								setExpanded(prev => !prev);
							}}
						>
							DA Info
							<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
						</Button>
					</div>
				</div>
				<Separator className="bg-border/50" />
				<div className="px-6">
					<div className="text-muted-foreground text-sm">
						<Button
							variant="ghost"
							className="h-auto gap-1 px-2 py-1 font-normal"
							onClick={() => {
								setExpanded(prev => !prev);
							}}
						>
							Issues
							<Badge variant="secondary" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
								4
							</Badge>
							<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
						</Button>
					</div>
				</div>
				<Separator className="bg-border/50" />
				<div className="px-6">
					<div className="text-muted-foreground text-sm">
						<Button
							variant="ghost"
							className="h-auto gap-1 px-2 py-1 font-normal"
							onClick={() => {
								setExpanded(prev => !prev);
							}}
						>
							Invoices
							<Badge variant="secondary" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
								9
							</Badge>
							<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
						</Button>
					</div>
				</div>
				<Separator className="bg-border/50" />
				<div className="px-6">
					<div className="text-muted-foreground text-sm">
						<Button
							variant="ghost"
							className="h-auto gap-1 px-2 py-1 font-normal"
							onClick={() => {
								setExpanded(prev => !prev);
							}}
						>
							Analytics
							<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
						</Button>
					</div>
				</div>
				<Separator className="bg-border/50" />
			</div>
		</>
	);
}
