import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, Paperclip, MoveUpRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

const attachments = [
	{
		id: '1',
		name: 'Invoice Pilotage #134A.pdf',
		amount: '1,800.00'
	}
];

export default function DisbursementServiceAttachments() {
	const [expanded, setExpanded] = useState(true);

	return (
		<div className="grid gap-2 px-4">
			<div className="text-muted-foreground text-sm">
				<Button
					variant="ghost"
					className="h-auto gap-1 px-2 py-1 font-normal"
					onClick={() => {
						setExpanded(prev => !prev);
					}}
				>
					Attachments
					<ChevronDown className={`text-muted-foreground ${expanded ? '' : 'rotate-270'}`} />
				</Button>
			</div>
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-1">
							{attachments.map(item => (
								<Button
									key={item.id}
									variant="ghost"
									className="group/issue h-auto justify-start bg-transparent px-2"
								>
									<Paperclip className="text-primary" />
									<div className="flex flex-1 items-center gap-2 text-left font-normal text-wrap">
										{item.name}
									</div>
									<div>
										<span className="text-muted-foreground text-2xs">USD</span>{' '}
										<span className="font-medium">2,500.00</span>
									</div>
									<MoveUpRight className="text-muted-foreground opacity-0 transition-opacity group-hover/issue:opacity-100" />
								</Button>
							))}
						</div>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
