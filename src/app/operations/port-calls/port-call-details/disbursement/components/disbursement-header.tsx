import { Download, Clock, Files } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export default function DisbursementHeader() {
	return (
		<div className="flex w-full max-w-3xl items-center gap-4">
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">Final DA</h2>
				<Badge variant="outline" className="rounded-full">
					<Clock className="h-3 w-3 text-amber-500" />
					<span className="text-muted-foreground">Pending approval</span>
				</Badge>
			</div>
			<div className="flex items-center justify-end gap-2">
				<Button variant="ghost" size="xs" className="text-muted-foreground">
					<Files />
					Invoices
					<Badge variant="secondary" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
						9
					</Badge>
				</Button>

				<Button variant="ghost" size="xs" className="text-muted-foreground">
					<Download />
					Download
				</Button>
			</div>
		</div>
	);
}
