import { BetweenHorizontalStart, Settings2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

const columns = [
	{
		value: 'service-item',
		label: 'Service item'
	},
	{
		value: 'cost-center',
		label: 'Cost center'
	},
	{
		value: 'accounting-code',
		label: 'Accounting code'
	},
	{
		value: 'vendor',
		label: 'Vendor'
	},
	{
		value: 'pda-price',
		label: 'PDA Price'
	},
	{
		value: 'fda-price',
		label: 'FDA Price'
	},
	{
		value: 'variance',
		label: 'Variance'
	},
	{
		value: 'ai-check',
		label: 'AI Check'
	}
];

export default function DisbursementTableDisplayOptions() {
	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button variant="ghost" size="xs" className="text-muted-foreground">
					<Settings2 />
					Display
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-82 p-0" align="end">
				<div className="p-4">
					<h5 className="text-sm leading-none font-medium">Display options</h5>
				</div>
				<Separator className="bg-border/50" />
				<div className="flex items-center gap-2 p-4">
					<Label className="text-muted-foreground flex flex-1 items-center gap-2 text-xs">
						<BetweenHorizontalStart className="size-3.5" /> Group by
					</Label>
					<Select>
						<SelectTrigger className="h-8 w-1/2 text-xs">
							<SelectValue placeholder="None" defaultValue="none" />
						</SelectTrigger>
						<SelectContent className="bg-background">
							<SelectGroup>
								<SelectItem value="none" defaultChecked className="text-xs">
									None
								</SelectItem>
								<SelectItem value="cost-center" className="text-xs">
									Cost center
								</SelectItem>
								<SelectItem value="acc-code" className="text-xs">
									Accounting code
								</SelectItem>
								<SelectItem value="vendor" className="text-xs">
									Vendor
								</SelectItem>
							</SelectGroup>
						</SelectContent>
					</Select>
				</div>
				<Separator className="bg-border/50" />
				<div className="text-muted-foreground px-4 pt-3 text-xs">Display columns</div>
				<ToggleGroup
					type="multiple"
					variant="default"
					className="flex-wrap gap-1.5 p-4 text-xs"
					size="sm"
					defaultValue={['service-item', 'cost-center', 'pda-price', 'fda-price', 'variance', 'ai-check']}
				>
					{columns.map(column => (
						<ToggleGroupItem
							key={column.value}
							size="sm"
							className="text-muted-foreground h-7 min-w-auto flex-none rounded-md text-xs capitalize first:rounded-md last:rounded-md"
							value={column.value}
						>
							{column.label}
						</ToggleGroupItem>
					))}
				</ToggleGroup>
				<Separator className="bg-border/50" />
				<div className="flex items-center justify-end gap-2 px-4 py-3">
					<Button variant="ghost" size="xs" className="text-muted-foreground">
						Reset to default
					</Button>
				</div>
			</PopoverContent>
		</Popover>
	);
}
