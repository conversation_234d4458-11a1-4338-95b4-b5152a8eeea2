import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronsUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function DisbursementServicePrice() {
	const [expanded, setExpanded] = useState(false);
	return (
		<div className="flex flex-col gap-2 px-4">
			<AnimatePresence>
				{expanded && (
					<motion.div
						initial={{ height: 0, overflow: 'hidden' }}
						animate={{ height: 'auto', overflow: 'visible' }}
						exit={{ height: 0, overflow: 'hidden' }}
						transition={{ duration: 0.15 }}
					>
						<div className="grid gap-2 pb-2 text-sm">
							<div className="flex h-7 items-center justify-between px-2">
								<div className="text-muted-foreground">Initial price</div>
								<div>
									<span className="text-muted-foreground text-2xs">USD</span>{' '}
									<span className="font-medium">2,500.00</span>
								</div>
							</div>
							<div className="flex h-7 items-center justify-between px-2">
								<div className="text-muted-foreground">Discount (10%)</div>
								<div>
									<span className="text-muted-foreground text-2xs">USD</span>{' '}
									<span className="font-medium">250.00</span>
								</div>
							</div>
							<div className="flex h-7 items-center justify-between px-2">
								<div className="text-muted-foreground">Tax (20%)</div>
								<div>
									<span className="text-muted-foreground text-2xs">USD</span>{' '}
									<span className="font-medium">500.00</span>
								</div>
							</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>
			<Button
				variant="outline"
				className="bg-accent/50 w-full px-3"
				onClick={() => {
					setExpanded(prev => !prev);
				}}
			>
				FDA Price
				<ChevronsUpDown className="text-muted-foreground" />
				<div className="flex-1 text-right">
					<span className="text-muted-foreground text-2xs">USD</span>{' '}
					<span className="text-base font-semibold">2,750.00</span>
				</div>
			</Button>
		</div>
	);
}
