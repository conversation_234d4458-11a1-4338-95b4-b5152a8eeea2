import { useState } from 'react';
import { Repeat2, Hash, BriefcaseBusiness, Check, PanelRight, Layers2, Wallet } from 'lucide-react';
import { toast } from 'sonner';
import { services } from '../data/data';
import DisbursementServiceAiCheck from './disbursement-service-ai-check';
import DisbursementServicePrice from './disbursement-service-price';
import DisbursementServiceAttachments from './disbursement-service-attachments';
import DisbursementServiceComments from './disbursement-service-comments';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';
import { ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { ChatData } from '@/app/mapbox/components/chat/context/chat-context';

interface Props {
	onClose?: () => void;
	setAiData?: (data: ChatData) => void;
	onOpenRefPanel?: () => void;
	isOpen?: boolean;
}

const costCenters: ComboboxOption[] = [
	{ value: 'pilotage', label: 'Pilotage' },
	{ value: 'towage', label: 'Towage' },
	{ value: 'lines-men', label: 'Lines men' },
	{ value: 'port-dues', label: 'Port dues' },
	{ value: 'cargo-expenses', label: 'Cargo expenses' },
	{ value: 'husbandry-expenses', label: 'Husbandry expenses' },
	{ value: 'agency-services', label: 'Agency services' },
	{ value: 'taxes', label: 'Taxes' }
];

const accCodes: ComboboxOption[] = [
	{ value: 'acc-123-gh', label: 'ACC-123-GH' },
	{ value: 'acc-456-ij', label: 'ACC-456-IJ' },
	{ value: 'acc-789-kl', label: 'ACC-789-KL' }
];

export default function DisbursementServicePanel({ onClose, setAiData, onOpenRefPanel }: Props) {
	const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>({});
	const [_selectedCostCenter, setSelectedCostCenter] = useState<string | null>('1');
	const [_selectedAccCode, setSelectedAccCode] = useState<string | null>('1');

	const toggleCheck = (id: string) => {
		const newState = !checkedItems[id];
		setCheckedItems(prev => ({
			...prev,
			[id]: newState
		}));

		if (newState) {
			const service = services.find(s => s.id === id);
			toast(`${service?.service} has been approved`, {
				description: <p className="text-muted-foreground text-xs">The requesting party will be notified.</p>,
				action: {
					label: 'Undo',
					onClick: () => {
						setCheckedItems(prev => ({
							...prev,
							[id]: false
						}));
					}
				}
			});
		}
	};

	return (
		<div className="flex h-full flex-col">
			<div className="flex h-16 items-center gap-2 p-4">
				<Button
					variant="ghost"
					className={cn(
						'text-muted-foreground border-muted-foreground mx-1 h-auto rounded-full border p-0.5',
						checkedItems['1']
							? 'bg-primary border-primary hover:bg-primary'
							: 'border-muted-foreground hover:border-primary text-muted-foreground hover:text-primary'
					)}
					onClick={() => toggleCheck('1')}
				>
					<Check className={cn('size-3', checkedItems['1'] ? 'text-white' : '')} />
				</Button>
				<div className="flex-1 px-1 leading-none font-semibold">Agency fee</div>
				<div className="flex items-center gap-2">
					<TooltipProvider delayDuration={100}>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									size="icon"
									variant="ghost"
									className="text-muted-foreground h-7 w-7"
									onClick={onClose}
								>
									<PanelRight />
								</Button>
							</TooltipTrigger>
							<TooltipContent>Collapse</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
			</div>
			<div className="flex flex-1 flex-col gap-6 overflow-auto">
				<Separator className="bg-transparent" />
				<DisbursementServicePrice />
				<div className="grid gap-2 px-6">
					<div className="group/combo flex items-center">
						<div className="text-muted-foreground min-w-[140px] text-sm text-nowrap">Cost center</div>
						<ComboboxButtonInline
							icon={Layers2}
							className="group-hover/combo:bg-accent w-full text-xs font-normal"
							data={costCenters}
							placeholder="---"
							initialValue="pilotage"
							onSelect={value => setSelectedCostCenter(value)}
						/>
					</div>
					<div className="group/combo flex items-center">
						<div className="text-muted-foreground min-w-[140px] text-sm text-nowrap">Accounting code</div>
						<ComboboxButtonInline
							icon={Hash}
							className="group-hover/combo:bg-accent w-full text-xs font-normal"
							data={accCodes}
							placeholder="---"
							initialValue="acc-123-gh"
							onSelect={value => setSelectedAccCode(value)}
						/>
					</div>
					<div className="flex h-7 items-center">
						<div className="text-muted-foreground min-w-[140px] text-sm text-nowrap">Account of</div>
						<Badge variant="outline" className="rounded-full text-xs">
							<Wallet className="text-muted-foreground size-3.5" />
							Charterer cost
						</Badge>
					</div>
					<div className="flex h-7 items-center">
						<div className="text-muted-foreground min-w-[140px] text-sm text-nowrap">Cost type</div>
						<Badge variant="outline" className="rounded-full text-xs">
							<Repeat2 className="size-3.5 text-emerald-500" />
							Recoverable
						</Badge>
					</div>
					<div className="flex h-7 items-center">
						<div className="text-muted-foreground min-w-[140px] text-sm text-nowrap">Vendor</div>
						<Badge variant="outline" className="rounded-full text-xs">
							<BriefcaseBusiness className="text-muted-foreground size-3.5" />
							Vendor Company Ltd.
						</Badge>
					</div>
				</div>
				<Separator className="bg-border/50" />
				<DisbursementServiceAiCheck setAiData={setAiData} onOpenRefPanel={onOpenRefPanel} />
				<Separator className="bg-border/50" />
				<DisbursementServiceAttachments />
				<Separator className="bg-border/50" />
				<DisbursementServiceComments />
				<Separator className="bg-transparent" />
			</div>
		</div>
	);
}
