import { useState, useEffect } from 'react';
import { Anchor, ArrowUpRight, BriefcaseBusiness, Calendar, Hash, LoaderCircle } from 'lucide-react';
import { dataAllIssues } from '../data/data';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { ChatData } from '@/app/mapbox/components/chat/context/chat-context';

interface Props {
	setAllIssuesData?: (data: ChatData) => void;
}

export default function DisbursementGeneral({ setAllIssuesData }: Props) {
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const timer = setTimeout(() => {
			setLoading(false);
		}, 5000);

		return () => clearTimeout(timer);
	}, []);

	return (
		<div className="flex w-full max-w-3xl flex-col items-center gap-6 lg:flex-row">
			<div className="w-full flex-1">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<Hash className="size-4" />
						Disbursement ID
					</div>
					<div className="flex items-center gap-2">
						<Badge variant="outline" className="rounded-full border-emerald-500">
							<span>FDA</span>
						</Badge>
						<span>#DA-019-MNT</span>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<BriefcaseBusiness className="size-4" />
						Local agent
					</div>
					<div>Local Agent Company Ltd.</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Anchor className="size-4" />
						Port
					</div>
					<div>Rotterdam, NL</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="size-4" />
						Arrived
					</div>
					<div>17 Mar - 08:02</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="size-4" />
						Sailed
					</div>
					<div>22 Mar - 18:30</div>
				</div>
			</div>
			<div className="w-full flex-1">
				<div className="bg-card grid gap-6 rounded-xl border p-6 text-sm">
					<div className="grid gap-2">
						<div className="flex items-center justify-between">
							<div>FDA Price</div>
							<div className="flex items-end gap-2">
								<span className="text-muted-foreground text-xs">USD</span>
								<span className="text-xl leading-6 font-bold">23,169.00</span>
							</div>
						</div>
						<Progress value={100} />
					</div>
					<div className="grid gap-2">
						<div className="flex items-center justify-between">
							<div className="text-muted-foreground">PDA Price</div>
							<div className="flex items-end gap-2">
								<span className="text-muted-foreground text-xs">USD</span>
								<span className="text-muted-foreground text-xl leading-6 font-medium">18,873.00</span>
							</div>
						</div>
						<Progress value={78} />
					</div>
					{loading ? (
						<div className="flex h-7 w-full items-center gap-2">
							<LoaderCircle className="text-primary size-4 animate-spin" />
							<div className="text-muted-foreground text-sm">AI screening...</div>
						</div>
					) : (
						<div className="flex items-center justify-between">
							<Button
								variant="ghost"
								size="xs"
								className="text-muted-foreground -ml-2"
								onClick={() => {
									setAllIssuesData?.(dataAllIssues);
								}}
							>
								<Badge variant="destructive" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
									4
								</Badge>
								<div className="flex items-center gap-1">
									Issues
									<ArrowUpRight className="text-muted-foreground size-3.5" />
								</div>
							</Button>
							<div>
								<span className="text-muted-foreground text-xs">Potential savings:</span>{' '}
								<span className="font-semibold text-emerald-500">$4,253.00</span>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
