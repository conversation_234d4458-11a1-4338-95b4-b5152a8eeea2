import { useState, useEffect } from 'react';
import {
	Check,
	ChevronRight,
	CircleAlert,
	LoaderCircle,
	MessageSquare,
	MoveUp,
	Paperclip,
	Repeat2,
	TriangleAlert
} from 'lucide-react';
import { toast } from 'sonner';
import {
	dataSingleIssue,
	dataRecoverable,
	dataAllIssues,
	services,
	dataWrongPayment,
	dataPilotage,
	dataTowage,
	dataShifting
} from '../data/data';
import DisbursementTableDisplayOptions from './disbursement-table-display-options';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ComboboxOption } from '@/components/ui/combobox';
import { ComboboxButtonInline } from '@/components/ui/combobox-button-inline';
import { Separator } from '@/components/ui/separator';
import { ChatData } from '@/app/mapbox/components/chat/context/chat-context';

interface Props {
	onOpenServicePanel?: () => void;
	setAiData?: (data: ChatData) => void;
	isServicePanelOpen?: boolean;
}

const costCenters: ComboboxOption[] = [
	{ value: 'pilotage', label: 'Pilotage' },
	{ value: 'towage', label: 'Towage' },
	{ value: 'lines-men', label: 'Lines men' },
	{ value: 'port-dues', label: 'Port dues' },
	{ value: 'cargo-expenses', label: 'Cargo expenses' },
	{ value: 'husbandry-expenses', label: 'Husbandry expenses' },
	{ value: 'agency-services', label: 'Agency services' },
	{ value: 'taxes', label: 'Taxes' }
];

export default function DisbursementCosts({ onOpenServicePanel, setAiData, isServicePanelOpen }: Props) {
	const [loading, setLoading] = useState(true);
	const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>({});
	const [_selectedCostCenter, setSelectedCostCenter] = useState<string | null>('1');
	const [clickedRowId, setClickedRowId] = useState<string | null>(null);

	// Reset clickedRowId when service panel is closed
	useEffect(() => {
		if (!isServicePanelOpen) {
			setClickedRowId(null);
		}
	}, [isServicePanelOpen]);

	// Create a registry of all available chat data objects
	const chatDataRegistry: Record<string, ChatData> = {
		dataPilotage,
		dataRecoverable,
		dataAllIssues,
		dataSingleIssue,
		dataTowage,
		dataWrongPayment,
		dataShifting
	};

	// Helper function to get the appropriate ChatData based on the aiRef string
	const getChatDataFromRef = (aiRef: string | undefined): ChatData => {
		if (!aiRef) return dataSingleIssue;

		// Look up the aiRef in the registry
		return chatDataRegistry[aiRef] || dataSingleIssue;
	};

	useEffect(() => {
		const timer = setTimeout(() => {
			setLoading(false);
		}, 5000);

		return () => clearTimeout(timer);
	}, []);

	const toggleCheck = (id: string) => {
		const newState = !checkedItems[id];
		setCheckedItems(prev => ({
			...prev,
			[id]: newState
		}));

		if (newState) {
			const service = services.find(s => s.id === id);
			toast(`${service?.service} has been approved`, {
				description: <p className="text-muted-foreground text-xs">The requesting party will be notified.</p>,
				action: {
					label: 'Undo',
					onClick: () => {
						setCheckedItems(prev => ({
							...prev,
							[id]: false
						}));
					}
				}
			});
		}
	};

	return (
		<TooltipProvider>
			<div className="flex w-full flex-col items-center gap-4">
				<div className="flex w-full max-w-3xl items-center justify-between gap-4">
					<h3 className="text-base font-semibold">Disbursement Costs</h3>
					<DisbursementTableDisplayOptions />
				</div>
				<div className="w-full max-w-6xl">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent">
								<TableHead colSpan={2} className="border-t text-xs">
									Service item
								</TableHead>
								<TableHead className="w-[160px] border-t px-4 text-xs">Cost center</TableHead>
								<TableHead className="hidden w-[180px] border-t text-xs">Vendor</TableHead>
								<TableHead className="w-[130px] border-t border-r text-right text-xs">
									PDA Price
								</TableHead>
								<TableHead className="w-[130px] border-t border-r text-right text-xs">
									FDA Price
								</TableHead>
								<TableHead className="border-t text-xs">Variance</TableHead>
								<TableHead className="border-t text-xs">AI Check</TableHead>
								<TableHead className="border-t text-right text-xs"></TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{services.map(service => (
								<TableRow
									key={service.id}
									className={cn(
										'group/item group/combo hover:bg-muted/20',
										clickedRowId === service.id && 'bg-muted/20'
									)}
								>
									<TableCell className="w-4">
										<div className="flex items-center justify-center">
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														variant="ghost"
														className={cn(
															'text-muted-foreground border-muted-foreground h-auto rounded-full border p-0.5',
															checkedItems[service.id]
																? 'bg-primary border-primary hover:bg-primary'
																: 'border-muted-foreground hover:border-primary text-muted-foreground hover:text-primary'
														)}
														onClick={() => toggleCheck(service.id)}
													>
														<Check
															className={cn(
																'size-3',
																checkedItems[service.id] ? 'text-white' : ''
															)}
														/>
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													{checkedItems[service.id] ? 'Unapprove' : 'Approve'}
												</TooltipContent>
											</Tooltip>
										</div>
									</TableCell>
									<TableCell
										className="cursor-pointer"
										onClick={() => {
											setClickedRowId(service.id);
											onOpenServicePanel?.();
										}}
									>
										<div className="flex items-center gap-2">
											<div className="flex-1 text-nowrap">{service.service}</div>
											<div className="text-muted-foreground flex items-center gap-1 opacity-0 transition-opacity duration-200 group-hover/item:opacity-100">
												<span className="text-2xs">View</span>
												<ChevronRight className="size-3" />
											</div>
										</div>
									</TableCell>
									<TableCell>
										<ComboboxButtonInline
											className="group-hover/combo:bg-accent w-full font-normal"
											data={costCenters}
											placeholder="---"
											initialValue={
												costCenters.find(cc => cc.label === service.costCenter)?.value
											}
											onSelect={value => setSelectedCostCenter(value)}
										/>
									</TableCell>
									<TableCell className="hidden">
										<span className="text-muted-foreground">{service.vendor}</span>
									</TableCell>
									<TableCell
										className="border-r text-right"
										onClick={() => {
											setClickedRowId(service.id);
											onOpenServicePanel?.();
										}}
									>
										<div className="flex items-end justify-end gap-1">
											<span className="text-muted-foreground text-2xs">{service.currency}</span>
											<span className="font-normal">{service.pdaPrice}</span>
										</div>
									</TableCell>
									<TableCell
										className="border-r text-right"
										onClick={() => {
											setClickedRowId(service.id);
											onOpenServicePanel?.();
										}}
									>
										<div className="flex items-end justify-end gap-1">
											<span className="text-muted-foreground text-2xs">{service.currency}</span>
											<span className="font-semibold">{service.fdaPrice}</span>
										</div>
									</TableCell>
									<TableCell
										onClick={() => {
											setClickedRowId(service.id);
											onOpenServicePanel?.();
										}}
									>
										<div className="flex items-center gap-2">
											{service.variance > 0 ? (
												<>
													<MoveUp className="text-destructive size-3" />
													<span className="text-muted-foreground text-xs">
														+{service.variance}%
													</span>
												</>
											) : (
												<span className="text-muted-foreground text-xs"></span>
											)}
										</div>
									</TableCell>
									<TableCell>
										{loading ? (
											<LoaderCircle className="text-muted-foreground/50 size-4 animate-spin" />
										) : service.issue ? (
											<Tooltip>
												<TooltipTrigger>
													<Badge
														variant="secondary"
														className="bg-secondary/50 rounded-full"
														onClick={() => {
															const chatData = getChatDataFromRef(service.aiRef);
															setAiData?.(chatData);
														}}
													>
														{service.issueType === 'warning' && (
															<CircleAlert className="size-3 text-amber-500" />
														)}
														{service.issueType === 'alert' && (
															<TriangleAlert className="size-3 text-red-500" />
														)}
														{service.issueType === 'recoverable' && (
															<Repeat2 className="size-3 text-emerald-500" />
														)}
														<span className="text-nowrap">{service.issue}</span>
													</Badge>
												</TooltipTrigger>
												<TooltipContent>
													<div className="grid gap-2 p-1">
														<div className="text-muted-foreground py-1">AI Check</div>
														<div className="grid gap-2">
															<Button
																variant="ghost"
																size="xs"
																className="-mx-2"
																onClick={() => {
																	const chatData = getChatDataFromRef(service.aiRef);
																	setAiData?.(chatData);
																}}
															>
																{service.issueType === 'warning' && (
																	<CircleAlert className="size-3 text-amber-500" />
																)}
																{service.issueType === 'alert' && (
																	<TriangleAlert className="size-3 text-red-500" />
																)}
																{service.issueType === 'recoverable' && (
																	<Repeat2 className="size-3 text-emerald-500" />
																)}
																<div className="flex-1 text-left">{service.issue}</div>
																<div className="font-semibold">${service.aiPrice}</div>
															</Button>
															<Separator className="bg-border/50" />
															<div className="text-muted-foreground max-w-xs leading-5">
																Billed Rate Code for 50k GRT. Vessel GRT is 45k,
																<br />
																requires Rate Code &apos;P02&apos; ($3800).
															</div>
														</div>
													</div>
												</TooltipContent>
											</Tooltip>
										) : (
											''
										)}
									</TableCell>
									<TableCell
										className="text-right"
										onClick={() => {
											setClickedRowId(service.id);
											onOpenServicePanel?.();
										}}
									>
										<div className="flex items-center justify-end gap-2">
											{service.comments > 0 && (
												<Tooltip>
													<TooltipTrigger>
														<Badge
															variant="outline"
															className="text-muted-foreground rounded-full"
														>
															<MessageSquare className="size-3" />
															<span>{service.comments}</span>
														</Badge>
													</TooltipTrigger>
													<TooltipContent>Comments</TooltipContent>
												</Tooltip>
											)}
											{service.attachments > 0 && (
												<Tooltip>
													<TooltipTrigger>
														<Badge
															variant="outline"
															className="text-muted-foreground rounded-full"
														>
															<Paperclip className="text-primary size-3" />
															<span>{service.attachments}</span>
														</Badge>
													</TooltipTrigger>
													<TooltipContent>Attachments</TooltipContent>
												</Tooltip>
											)}
										</div>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
						<TableFooter>
							<TableRow>
								<TableCell colSpan={3} className="text-right">
									Total
								</TableCell>
								<TableCell className="border-r text-right">
									<div className="flex items-end justify-end gap-1">
										<span className="text-muted-foreground text-2xs">USD</span>
										<span className="text-md font-bold">18,873.00</span>
									</div>
								</TableCell>
								<TableCell className="border-r text-right">
									<div className="flex items-end justify-end gap-1">
										<span className="text-muted-foreground text-2xs">USD</span>
										<span className="text-md font-bold">23,169.00</span>
									</div>
								</TableCell>
								<TableCell>
									<div className="flex items-center gap-2">
										<MoveUp className="text-destructive size-3" />
										<span className="text-muted-foreground text-xs">+16.1%</span>
									</div>
								</TableCell>
								<TableCell colSpan={2} className="text-right"></TableCell>
							</TableRow>
						</TableFooter>
					</Table>
				</div>
			</div>
		</TooltipProvider>
	);
}
