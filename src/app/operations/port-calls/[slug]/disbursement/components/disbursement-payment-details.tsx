import { ShieldCheck } from 'lucide-react';

export default function DisbursementPaymentDetails() {
	return (
		<div className="flex w-full max-w-3xl flex-col items-center gap-4">
			<h3 className="w-full text-base font-semibold">Payment details</h3>
			<div className="grid w-full gap-4 rounded-md border p-4 text-sm">
				<div className="flex items-center gap-2">
					<div className="font-medium">United Overseas Bank (SGD)</div>
					<ShieldCheck className="size-4 text-emerald-500" />
				</div>
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-3 text-sm sm:grid-cols-[auto_minmax(0px,_1fr)_auto_minmax(0px,_1fr)]">
					<div className="text-muted-foreground">IBAN</div>
					<div>SDJSDFT9384RU534H</div>
					<div className="text-muted-foreground">Account No</div>
					<div>ACC-123-456-789-0</div>
					<div className="text-muted-foreground">Swift</div>
					<div>SWIFT-123-12212</div>
					<div className="text-muted-foreground">Beneficiary</div>
					<div>Raffles Place Branch</div>
				</div>
			</div>
		</div>
	);
}
