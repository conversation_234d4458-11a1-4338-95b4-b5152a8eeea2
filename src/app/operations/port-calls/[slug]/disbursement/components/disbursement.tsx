import DisbursementActivity from './disbursement-activity';
import DisbursementCosts from './disbursement-costs';
import DisbursementFooter from './disbursement-footer';
import DisbursementGeneral from './disbursement-general';
import DisbursementHeader from './disbursement-header';
import DisbursementPaymentDetails from './disbursement-payment-details';
import { Separator } from '@/components/ui/separator';

export default function Disbursement() {
	return (
		<div className="flex w-full flex-col">
			<div className="w-full overflow-auto">
				<div className="flex flex-1 flex-col items-center gap-6 p-4">
					<DisbursementHeader />
					<DisbursementGeneral />
					<Separator className="max-w-3xl" />
					<DisbursementCosts />
					<Separator className="max-w-3xl bg-transparent" />
					<DisbursementPaymentDetails />
					<Separator className="max-w-3xl" />
					<DisbursementActivity />
				</div>
			</div>
			<DisbursementFooter />
		</div>
	);
}
