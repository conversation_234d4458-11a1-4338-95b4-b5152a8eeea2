import { Check, MessageSquare, Paperclip, TrendingUp } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { services } from '../data/data';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export default function DisbursementCosts() {
	const [checkedItems, setCheckedItems] = useState<Record<string, boolean>>({});

	const toggleCheck = (id: string) => {
		const newState = !checkedItems[id];
		setCheckedItems(prev => ({
			...prev,
			[id]: newState
		}));

		if (newState) {
			const service = services.find(s => s.id === id);
			toast(`${service?.service} has been approved`, {
				action: {
					label: 'Undo',
					onClick: () => {
						setCheckedItems(prev => ({
							...prev,
							[id]: false
						}));
					}
				}
			});
		}
	};

	return (
		<div className="flex w-full flex-col items-center gap-4">
			<h3 className="w-full max-w-3xl text-base font-semibold">Disbursement Costs</h3>
			<div className="w-full max-w-6xl">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className="w-[220px] border-t">Service item</TableHead>
							<TableHead className="w-[130px] border-t">Cost center</TableHead>
							<TableHead className="w-[180px] border-t">Vendor</TableHead>
							<TableHead className="w-[130px] border-t border-r text-right">PDA Price</TableHead>
							<TableHead className="w-[130px] border-t border-r text-right">FDA Price</TableHead>
							<TableHead className="w-[130px] border-t">Variance</TableHead>
							<TableHead className="border-t text-right"></TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{services.map(service => (
							<TableRow key={service.id}>
								<TableCell>
									<div className="flex items-center gap-3">
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														variant="ghost"
														className={cn(
															'text-muted-foreground border-muted-foreground h-auto rounded-full border p-0.5',
															checkedItems[service.id]
																? 'bg-primary border-primary hover:bg-primary'
																: 'border-muted-foreground hover:border-primary text-muted-foreground hover:text-primary'
														)}
														onClick={() => toggleCheck(service.id)}
													>
														<Check
															className={cn(
																'size-3',
																checkedItems[service.id]
																	? 'text-primary-foreground'
																	: ''
															)}
														/>
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													{checkedItems[service.id] ? 'Unapprove' : 'Approve'}
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
										{service.service}
									</div>
								</TableCell>
								<TableCell>
									<span className="text-muted-foreground">{service.costCenter}</span>
								</TableCell>
								<TableCell>
									<span className="text-muted-foreground">{service.vendor}</span>
								</TableCell>
								<TableCell className="border-r text-right">
									<div className="flex items-end justify-end gap-1">
										<span className="text-muted-foreground text-2xs">{service.currency}</span>
										<span className="font-medium">{service.pdaPrice}</span>
									</div>
								</TableCell>
								<TableCell className="border-r text-right">
									<div className="flex items-end justify-end gap-1">
										<span className="text-muted-foreground text-2xs">{service.currency}</span>
										<span className="font-medium">{service.fdaPrice}</span>
									</div>
								</TableCell>
								<TableCell>
									<div className="flex items-center gap-2">
										<TrendingUp className="size-4 text-red-500" />
										<span className="text-muted-foreground text-2xs">{service.variance}</span>
									</div>
								</TableCell>
								<TableCell className="text-right">
									<div className="flex items-center justify-end gap-2">
										<TooltipProvider>
											{service.comments > 0 && (
												<Tooltip>
													<TooltipTrigger>
														<Badge
															variant="outline"
															className="text-muted-foreground rounded-full"
														>
															<MessageSquare className="size-3" />
															<span>{service.comments}</span>
														</Badge>
													</TooltipTrigger>
													<TooltipContent>Comments</TooltipContent>
												</Tooltip>
											)}
											{service.attachments > 0 && (
												<Tooltip>
													<TooltipTrigger>
														<Badge
															variant="outline"
															className="text-muted-foreground rounded-full"
														>
															<Paperclip className="text-primary size-3" />
															<span>{service.attachments}</span>
														</Badge>
													</TooltipTrigger>
													<TooltipContent>Attachments</TooltipContent>
												</Tooltip>
											)}
										</TooltipProvider>
									</div>
								</TableCell>
							</TableRow>
						))}
					</TableBody>
					<TableFooter>
						<TableRow>
							<TableCell colSpan={3} className="text-right">
								Total
							</TableCell>
							<TableCell className="border-r text-right">
								<div className="flex items-end justify-end gap-1">
									<span className="text-muted-foreground text-2xs">USD</span>
									<span className="font-bold">18,873.00</span>
								</div>
							</TableCell>
							<TableCell className="border-r text-right">
								<div className="flex items-end justify-end gap-1">
									<span className="text-muted-foreground text-2xs">USD</span>
									<span className="font-bold">23,169.00</span>
								</div>
							</TableCell>
							<TableCell>
								<div className="flex items-center gap-2">
									<TrendingUp className="size-4 text-red-500" />
									<span className="text-muted-foreground text-xs">+16.12%</span>
								</div>
							</TableCell>
							<TableCell className="text-right"></TableCell>
						</TableRow>
					</TableFooter>
				</Table>
			</div>
		</div>
	);
}
