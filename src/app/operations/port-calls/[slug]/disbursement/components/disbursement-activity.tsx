import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Text } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function DisbursementActivity() {
	return (
		<div className="flex w-full max-w-3xl flex-col items-center gap-4">
			<Tabs defaultValue="approval" className="w-full">
				<div className="flex items-center justify-between gap-2">
					<h3 className="text-base font-semibold">Activity</h3>
					<TabsList className="flex border bg-transparent px-0.5">
						<TabsTrigger
							value="approval"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Approval Flow
						</TabsTrigger>
						<TabsTrigger
							value="activity"
							className="data-[state=active]:bg-accent/50 data-[state=active]:border-accent flex-1 border border-transparent data-[state=active]:border"
						>
							Activity Log
						</TabsTrigger>
					</TabsList>
				</div>
				<TabsContent value="approval" className="flex flex-1 flex-col gap-6 pt-4">
					<ul className="a-activity-list w-full">
						<li className="flex flex-col">
							<div className="flex flex-row items-center gap-4">
								<Avatar className="h-5 w-5 rounded-sm">
									<AvatarImage src="/static/media/avatar-md.webp" alt="J. Doe" />
									<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
										J.D
									</AvatarFallback>
								</Avatar>
								<div className="flex flex-row items-center gap-2 text-sm">
									<span className="font-medium">John Doe</span>
									<span className="text-muted-foreground hidden sm:block">
										submitted FDA for approval
									</span>
									<Badge variant="outline" className="rounded-full">
										<Clock className="size-3 text-amber-500" />
										<span>Pending approval</span>
									</Badge>
									<span className="text-muted-foreground text-lg">•</span>
									<span className="text-muted-foreground text-xs">23 min ago</span>
								</div>
							</div>
							<div className="relative flex min-h-4 flex-row">
								<Separator
									orientation="vertical"
									className="a-list-sep absolute left-[9px]"
								></Separator>
								<div className="flex flex-row items-center gap-2 px-9 py-1 pb-4 text-sm">
									<Text className="size-3" />
									<i>Approve the DA</i>
								</div>
							</div>
						</li>
						<li className="flex flex-col">
							<div className="flex flex-row items-center gap-4">
								<Avatar className="h-5 w-5 rounded-sm">
									<AvatarImage src="" alt="M. Phillips" />
									<AvatarFallback className="bg-primary text-2xs flex h-full w-full items-center justify-center rounded-md font-medium text-white">
										M.P
									</AvatarFallback>
								</Avatar>
								<div className="flex flex-row items-center gap-2 text-sm">
									<span className="font-medium">Michael Phillips</span>
									<span className="text-muted-foreground hidden sm:block">approved the FDA</span>
									<Badge variant="outline" className="rounded-full">
										<CheckCheck className="size-3 text-emerald-500" />
										<span>Approved</span>
									</Badge>
									<span className="text-muted-foreground text-lg">•</span>
									<span className="text-muted-foreground text-xs">50 min ago</span>
								</div>
							</div>
							<div className="relative flex min-h-4 flex-row">
								<Separator
									orientation="vertical"
									className="a-list-sep absolute left-[9px]"
								></Separator>
								<div className="flex flex-row items-center gap-2 px-9 py-1 pb-4 text-sm">
									<Text className="size-3" />
									<i>Thanks!</i>
								</div>
							</div>
						</li>
					</ul>
				</TabsContent>
				<TabsContent value="activity" className="flex flex-1 flex-col gap-6 pt-4">
					[Activity Log]
				</TabsContent>
			</Tabs>
		</div>
	);
}
