import { Download, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export default function DisbursementHeader() {
	return (
		<div className="flex w-full max-w-3xl items-center gap-4">
			<div className="flex flex-1 items-center gap-2">
				<h2 className="text-xl font-semibold">Final DA</h2>
				<Badge variant="outline" className="rounded-full">
					<Clock className="h-3 w-3 text-amber-500" />
					<span className="text-muted-foreground">Pending approval</span>
				</Badge>
			</div>
			<Button variant="outline" size="sm">
				<Download />
				Download
			</Button>
		</div>
	);
}
