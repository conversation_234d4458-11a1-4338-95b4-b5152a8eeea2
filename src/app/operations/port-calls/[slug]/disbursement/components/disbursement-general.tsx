import { Alert<PERSON><PERSON>gle, Anchor, BriefcaseBusiness, Calendar, Hash } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export default function DisbursementGeneral() {
	return (
		<div className="flex w-full max-w-3xl flex-col items-center gap-6 lg:flex-row">
			<div className="w-full flex-1">
				<div className="grid grid-cols-[auto_minmax(0px,1fr)] gap-x-8 gap-y-4 text-sm">
					<div className="text-muted-foreground flex items-center gap-2">
						<Hash className="size-4" />
						Disbursement ID
					</div>
					<div className="flex items-center gap-2">
						<Badge variant="outline" className="rounded-full border-emerald-500">
							<span>FDA</span>
						</Badge>
						<span>#DA-019-MNT</span>
					</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<BriefcaseBusiness className="size-4" />
						Local agent
					</div>
					<div>Local Agent Company Ltd.</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Anchor className="size-4" />
						Port
					</div>
					<div>Rotterdam, NL</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="size-4" />
						Arrived
					</div>
					<div>17 Mar - 08:02</div>
					<div className="text-muted-foreground flex items-center gap-2">
						<Calendar className="size-4" />
						Sailed
					</div>
					<div>22 Mar - 18:30</div>
				</div>
			</div>
			<div className="w-full flex-1">
				<div className="bg-card grid gap-6 rounded-xl border p-6 text-sm">
					<div className="grid gap-2">
						<div className="flex items-center justify-between">
							<div>FDA Price</div>
							<div className="flex items-end gap-2">
								<span className="text-muted-foreground text-xs">USD</span>
								<span className="text-xl leading-6 font-bold">23,169.00</span>
							</div>
						</div>
						<Progress value={100} />
					</div>
					<div className="grid gap-2">
						<div className="flex items-center justify-between">
							<div className="text-muted-foreground">PDA Price</div>
							<div className="flex items-end gap-2">
								<span className="text-muted-foreground text-xs">USD</span>
								<span className="text-muted-foreground text-xl leading-6 font-medium">18,873.00</span>
							</div>
						</div>
						<Progress value={78} />
					</div>
					<div className="flex items-center gap-2 text-sm">
						<AlertTriangle className="size-3.5 text-amber-500" />
						<div className="text-muted-foreground">
							FDA price is <span className="text-primary-foreground">16.12% higher</span> than the PDA.
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
