import { PencilLine } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { formatDateFullDay, formatTime, formatTotalHours } from '@/common/utils/formatUtils';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const events = [
	{
		id: '1',
		event: 'Arrived roads',
		type: 'default',
		dateFrom: '2024-11-14T09:15:00.000Z',
		dateTo: '2024-11-14T14:30:00.000Z',
		remarks: '<PERSON><PERSON><PERSON> arrived on pilot station',
		duration: 5.25
	},
	{
		id: '2',
		event: 'Berthing',
		type: 'default',
		dateFrom: '2024-11-14T14:30:00.000Z',
		dateTo: '2024-11-14T16:45:00.000Z',
		remarks: '',
		duration: 2.25
	},
	{
		id: '3',
		event: 'Commenced loading',
		type: 'waiting',
		dateFrom: '2024-11-14T16:45:00.000Z',
		dateTo: '2024-11-15T08:30:00.000Z',
		remarks: '',
		duration: 15.75
	},
	{
		id: '4',
		event: 'Rain',
		type: 'stoppage',
		dateFrom: '2024-11-15T08:30:00.000Z',
		dateTo: '2024-11-15T11:15:00.000Z',
		remarks: 'Rain, loading not allowed',
		duration: 2.75
	},
	{
		id: '5',
		event: 'Completed loading',
		type: 'working',
		dateFrom: '2024-11-15T11:15:00.000Z',
		dateTo: '2024-11-15T16:40:00.000Z',
		remarks: '',
		duration: 5.42
	}
];
const types = [
	{
		type: 'default',
		color: 'bg-muted-foreground'
	},
	{
		type: 'waiting',
		color: 'bg-amber-500'
	},
	{
		type: 'working',
		color: 'bg-emerald-500'
	},
	{
		type: 'stoppage',
		color: 'bg-red-500'
	}
];

export default function SofGeneral() {
	return (
		<div className="flex w-full flex-col items-center gap-4">
			<div className="flex w-full max-w-3xl items-center gap-2">
				<h3 className="text-base font-semibold">SOF Split 1</h3>
				<Badge variant="outline" className="text-muted-foreground rounded-full">
					<PencilLine className="size-3" />
					<span>Draft</span>
				</Badge>
			</div>
			<div className="w-full max-w-6xl">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className="w-[240px] border-t">Event</TableHead>
							<TableHead className="w-[130px] border-t">Date from</TableHead>
							<TableHead className="w-[70px] border-t">From</TableHead>
							<TableHead className="w-[130px] border-t">Date to</TableHead>
							<TableHead className="w-[70px] border-t">To</TableHead>
							<TableHead className="border-t">Remarks</TableHead>
							<TableHead className="w-[120px] border-t text-right">Duration</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{events.map(event => (
							<TableRow key={event.id}>
								<TableCell>
									<div className="flex items-center gap-2">
										<span
											className={cn(
												'bg-muted-foreground block h-1.5 w-1.5 rounded-full',
												types.find(t => t.type === event.type)?.color || 'bg-muted-foreground'
											)}
										></span>
										<span>{event.event}</span>
									</div>
								</TableCell>
								<TableCell>
									<span className="text-muted-foreground">{formatDateFullDay(event.dateFrom)}</span>
								</TableCell>
								<TableCell>{formatTime(event.dateFrom)}</TableCell>
								<TableCell>
									<span className="text-muted-foreground">{formatDateFullDay(event.dateTo)}</span>
								</TableCell>
								<TableCell>{formatTime(event.dateTo)}</TableCell>
								<TableCell>
									<i className="text-muted-foreground">{event.remarks}</i>
								</TableCell>
								<TableCell className="text-right">{formatTotalHours(event.duration)}</TableCell>
							</TableRow>
						))}
					</TableBody>
					<TableFooter>
						<TableRow>
							<TableCell colSpan={7} className="text-right">
								<div className="flex justify-end gap-6">
									<div>Total duration</div>
									<div className="font-bold">{formatTotalHours(26.67)}</div>
								</div>
							</TableCell>
						</TableRow>
					</TableFooter>
				</Table>
			</div>
		</div>
	);
}
