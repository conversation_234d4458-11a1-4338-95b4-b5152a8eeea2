import SofActivity from './sof-activity';
import SofG<PERSON>al from './sof-general';
import SofHeader from './sof-header';
import SofOperations from './sof-operations';
import SofRemarks from './sof-remarks';
import { Separator } from '@/components/ui/separator';

export default function Sof() {
	return (
		<div className="w-full overflow-auto">
			<div className="flex flex-1 flex-col items-center gap-6 p-4">
				<SofHeader />
				<SofGeneral />
				<Separator className="bg-transparent" />
				<SofOperations />
				<Separator className="bg-transparent" />
				<SofRemarks />
				<SofActivity />
			</div>
		</div>
	);
}
