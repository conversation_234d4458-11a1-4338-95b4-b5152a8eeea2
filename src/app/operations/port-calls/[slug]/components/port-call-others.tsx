export default function PortCallOthers() {
	const links = [
		{
			name: 'Nomination information',
			url: '#'
		},
		{
			name: 'Instructions',
			url: '#'
		},
		{
			name: 'Itinerary',
			url: '#'
		},
		{
			name: 'Cargo details',
			url: '#'
		},
		{
			name: 'Drafts',
			url: '#'
		},
		{
			name: 'Bunkers',
			url: '#'
		},
		{
			name: 'Services',
			url: '#'
		},
		{
			name: 'Crew change',
			url: '#'
		},
		{
			name: 'Payments',
			url: '#'
		}
	];
	return (
		<div className="grid gap-4">
			<h3 className="text-base font-semibold">Others</h3>
			<ul className="text-link list-disc pl-4 text-sm [&>li]:mt-2">
				{links.map(link => (
					<li key={link.name}>
						<a href={link.url} className="text-link hover:underline">
							{link.name}
						</a>
					</li>
				))}
			</ul>
		</div>
	);
}
