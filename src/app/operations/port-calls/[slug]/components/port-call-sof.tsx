import { Timer } from 'lucide-react';
import { useNavigate } from 'react-router';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { statuses as globalStatuses } from '@/constants/statuses';

const sof = [
	{
		id: '1',
		name: 'SOF Split 1',
		terminal: 'Shell Terminal 13',
		status: 'draft',
		hrs: '35',
		min: '24'
	},
	{
		id: '2',
		name: 'SOF Split 2',
		terminal: 'Shell Europoort (33-3)',
		status: 'approved',
		hrs: '12',
		min: '28'
	}
];

const StatusBadge = ({ status }: { status: string }) => {
	const statusConfig = globalStatuses.find(s => s.value === status);

	if (!statusConfig) return <span>{status}</span>;

	const { icon: Icon, color, label } = statusConfig;

	return (
		<Badge variant="outline" className="rounded-full">
			{Icon && <Icon className={cn('h-3 w-3', color)} />}
			<span className="text-muted-foreground">{label}</span>
		</Badge>
	);
};

export default function PortCallSof() {
	const navigate = useNavigate();
	return (
		<div className="grid gap-4">
			<h3 className="text-base font-semibold">
				Statement of facts <span className="text-muted-foreground font-normal">({sof.length})</span>
			</h3>
			<Table>
				<TableBody>
					{sof.map(item => (
						<TableRow key={item.id} onClick={() => navigate(`/operations/port-calls/ABX-123A/sof`)}>
							<TableCell className="w-1/2">
								<div className="flex items-center gap-2">
									<Timer className="text-muted-foreground h-4 w-4" />
									<span className="font-medium">{item.name}</span>
									<span className="text-muted-foreground text-lg">•</span>
									<span className="text-muted-foreground">{item.terminal}</span>
								</div>
							</TableCell>
							<TableCell>
								<StatusBadge status={item.status} />
							</TableCell>
							<TableCell className="text-right">
								<div className="flex items-center justify-end gap-2">
									<div className="flex gap-1">
										<span className="font-medium">{item.hrs}</span>
										<span className="text-muted-foreground">hrs</span>
									</div>
									<div className="flex gap-1">
										<span className="font-medium">{item.min}</span>
										<span className="text-muted-foreground">min</span>
									</div>
								</div>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</Table>
		</div>
	);
}
