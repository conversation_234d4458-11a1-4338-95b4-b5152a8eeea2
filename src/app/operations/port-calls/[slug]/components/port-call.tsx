import PortCallDisbursements from './port-call-disbursements';
import PortCallGeneral from './port-call-general';
import PortCallHeader from './port-call-header';
import PortCallOthers from './port-call-others';
import PortCallSof from './port-call-sof';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const tabClassName =
	'data-[state=active]:border-primary -mb-[1px] rounded-none border-b-2 border-transparent px-1 py-3 data-[state=active]:shadow-none';

export default function PortCall() {
	return (
		<div className="flex flex-1 flex-col items-center gap-4 overflow-auto p-4">
			<div className="grid w-full max-w-3xl gap-4">
				<PortCallHeader />
				<Tabs defaultValue="overview" className="w-full">
					<TabsList className="h-auto w-full justify-start gap-4 rounded-none border-b bg-transparent p-0">
						<TabsTrigger value="overview" className={tabClassName}>
							Overview
						</TabsTrigger>
						<TabsTrigger value="ops" className={tabClassName}>
							Ops
						</TabsTrigger>
						<TabsTrigger value="docs" className={tabClassName}>
							Docs
						</TabsTrigger>
						<TabsTrigger value="financial" className={tabClassName}>
							Financial
						</TabsTrigger>
						<TabsTrigger value="laytime" className={tabClassName}>
							Laytime
						</TabsTrigger>
						<TabsTrigger value="insights" className={tabClassName}>
							Insights
						</TabsTrigger>
					</TabsList>
					<TabsContent value="overview">
						<div className="my-6 grid gap-6">
							<PortCallGeneral />
							<Separator className="bg-transparent" />
							<PortCallSof />
							<Separator className="bg-transparent" />
							<PortCallDisbursements />
							<Separator className="bg-transparent" />
							<PortCallOthers />
						</div>
					</TabsContent>
					<TabsContent value="ops">Ops</TabsContent>
					<TabsContent value="docs">Docs</TabsContent>
					<TabsContent value="financial">Financial</TabsContent>
					<TabsContent value="laytime">Laytime</TabsContent>
					<TabsContent value="insights">Insights</TabsContent>
				</Tabs>
			</div>
		</div>
	);
}
