import { Route, Routes } from 'react-router';
import { lazy } from 'react';

const PortCallsPage = lazy(() => import('./page'));
const PortCallDetailsPage = lazy(() => import('./port-call-details/page'));
const SOFPage = lazy(() => import('./port-call-details/sof/page'));
const DisbursementPage = lazy(() => import('./port-call-details/disbursement/page'));
const DisbursementChatPage = lazy(() => import('./port-call-details/disbursement/chat/page'));

export default function PortCalls() {
	return (
		<Routes>
			<Route index element={<PortCallsPage />} />
			<Route path=":id" element={<PortCallDetailsPage />} />
			<Route path=":id/sof" element={<SOFPage />} />
			<Route path=":id/disbursement" element={<DisbursementPage />} />
			<Route path=":id/disbursement" element={<DisbursementPage />} />
			<Route path=":id/disbursement/chat" element={<DisbursementChatPage />} />
		</Routes>
	);
}
