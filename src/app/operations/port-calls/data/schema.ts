import { z } from 'zod';

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const portCallSchema = z.object({
	fileId: z.string(),
	vesselImo: z.string(),
	vesselName: z.string(),
	portFunction: z.string(),
	portName: z.string(),
	portCountryCode: z.string(),
	eta: z.string(),
	etd: z.string(),
	agentName: z.string(),
	status: z.string(),
	operatorName: z.string()
});

export type portCall = z.infer<typeof portCallSchema>;
