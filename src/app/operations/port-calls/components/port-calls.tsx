import { columns } from './data-table-columns';
import { DataTable } from './data-table';
import { DataTableSkeleton } from '@/components/data-table/data-table-skeleton';
import { usePortCalls } from '@/hooks/portCall/use-port-calls';

export default function PortCalls() {
	const { portCalls, loading: portCallsLoading } = usePortCalls();
	if (portCallsLoading) {
		return <DataTableSkeleton />;
	}

	return <DataTable data={portCalls} columns={columns} />;
}
