import { Root, createRoot } from 'react-dom/client';
import { BrowserRouter, Route, Routes } from 'react-router';
import { lazy, Suspense } from 'react';

const Layout = lazy(() => import('./app/layout'));
const HomePage = lazy(() => import('./app/mapbox/page'));
const HomePageOld = lazy(() => import('./app/page'));
const InboxPage = lazy(() => import('./app/inbox/page'));
const TaskPage = lazy(() => import('./app/tasks/page'));
const AIPage = lazy(() => import('./app/ai/page'));

const Voyages = lazy(() => import('./app/operations/voyages'));
const PortCalls = lazy(() => import('./app/operations/port-calls'));
const Appointments = lazy(() => import('./app/operations/appointments'));

const EnquiriesPage = lazy(() => import('./app/operations/enquiries/page'));
const PaymentsPage = lazy(() => import('./app/financial/payments/page'));
const CashflowPage = lazy(() => import('./app/financial/cashflow/page'));
const Disbursements = lazy(() => import('./app/financial/disbursements'));
const QuickQuotePage = lazy(() => import('./app/quick-quote/page'));
const Agents = lazy(() => import('./app/directory/agents'));
const Ports = lazy(() => import('./app/directory/ports'));
const Laytime = lazy(() => import('./app/laytime'));
const MyFleetPage = lazy(() => import('./app/my-fleet'));
const MyCargoes = lazy(() => import('./app/my-cargoes'));
const MapboxPage = lazy(() => import('./app/mapbox/page'));
const Admin = lazy(() => import('./app/admin'));
const Settings = lazy(() => import('./app/settings'));

const getRoot = (id: string = 'root'): Root => {
	let rootElement = document.getElementById(id);
	if (!rootElement) {
		rootElement = document.createElement('div');
		rootElement.setAttribute('id', id);
		document.body.appendChild(rootElement);
	}

	const root = createRoot(rootElement);
	return root;
};

getRoot().render(
	<BrowserRouter basename={process.env.BASE_PATH}>
		<Routes>
			<Route
				element={
					<Suspense>
						<Layout />
					</Suspense>
				}
			>
				<Route index element={<HomePage />} />
				<Route path="/home" element={<HomePageOld />} />
				<Route path="/inbox" element={<InboxPage />} />
				<Route path="/tasks" element={<TaskPage />} />
				<Route path="/ai" element={<AIPage />} />
				<Route path="/operations">
					<Route path="voyages/*" element={<Voyages />} />
					<Route path="port-calls/*" element={<PortCalls />} />
					<Route path="appointments/*" element={<Appointments />} />
					<Route path="enquiries" element={<EnquiriesPage />} />
				</Route>
				<Route path="/financial">
					<Route path="disbursements/*" element={<Disbursements />} />
					<Route path="payments" element={<PaymentsPage />} />
					<Route path="cashflow" element={<CashflowPage />} />
				</Route>
				<Route path="/quick-quote" element={<QuickQuotePage />} />
				<Route path="/directory">
					<Route path="agents/*" element={<Agents />} />
					<Route path="ports/*" element={<Ports />} />
				</Route>
				<Route path="/laytime/*" element={<Laytime />} />
				<Route path="/my-fleet/*" element={<MyFleetPage />} />
				<Route path="/my-cargoes/*" element={<MyCargoes />} />
				<Route path="/mapbox" element={<MapboxPage />} />
				<Route path="/admin/*" element={<Admin />} />
				<Route path="/settings/*" element={<Settings />} />
				<Route path="*" element={<div>404 Not Found!</div>} />
			</Route>
		</Routes>
	</BrowserRouter>
);
