import { jwtDecode } from 'jwt-decode';
import { ComponentProps, ReactElement, ReactNode, PropsWithChildren, cloneElement } from 'react';

import {
	AuthenticationProvider,
	User,
	UserType,
	UserProvider,
	useAuthentication,
	useAuthenticatedUser
} from '@abraxa/authentication';

export type { User } from '@abraxa/authentication';

interface Auth0Payload {
	sub: string;
	email: string;
	name: string;
	picture?: string;
	[key: string]: string | undefined;
}

function parseAuth0Token(token: string): User {
	const decoded = jwtDecode<Auth0Payload>(token);

	if (!decoded) {
		throw new Error('Invalid token');
	}

	const user: User = {
		id: decoded.sub,
		email: decoded.email,
		type: UserType.Principal, // Default value, adjust as needed
		firstName: decoded.name.split(' ')[0],
		lastName: decoded.name.split(' ')[1],
		fullName: decoded.name,
		profileImage: decoded.picture,
		tenantId: 'Tenant', // Custom claim
		company: { name: 'Company' } // Custom claim
	};

	return user;
}

const AUTH0_DEFAULT_CONFIG = {
	domain: process.env.NEXT_PUBLIC_AUTH0_DOMAIN ?? '',
	clientId: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID ?? '',
	audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE ?? ''
};

interface WithAuthenticationProps {
	loadingElement?: ReactNode;
}

function WithAuthentication({ loadingElement, children }: PropsWithChildren<WithAuthenticationProps>) {
	const { isAuthenticating, isAuthenticated, user, error } = useAuthentication({
		onAuthentication: (token: string) => Promise.resolve(parseAuth0Token(token))
	});

	return (
		<UserProvider user={user}>
			{/* TODO: Proper 401 handling */}
			{isAuthenticating ? (
				loadingElement
			) : error ? (
				<h1>401 Unauthorized</h1>
			) : (
				isAuthenticated && user != null && children
			)}
		</UserProvider>
	);
}

// TODO: @abraxa/host-app could be used to automate the whole authentication flow
// but currently @abraxa/host-app comes with too much overhead for this project -
// GrowthBook integration, design system, router, react-query, etc.
// We should first expose a way to use the authentication flow without the rest of the host-app default setup
export function Authentication(props: ComponentProps<typeof WithAuthentication>) {
	return (
		<AuthenticationProvider {...AUTH0_DEFAULT_CONFIG}>
			<WithAuthentication {...props} />
		</AuthenticationProvider>
	);
}

export const WithUser = ({ children }: { children: ReactElement<{ user?: User }> }) => {
	const { user } = useAuthenticatedUser();

	return cloneElement(children, { user });
};
