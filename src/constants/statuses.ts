import { CircleStop, CheckCheck, SendHorizonal, Clock, PenLine } from 'lucide-react';

export const statuses = [
	{
		value: 'draft',
		label: 'Draft',
		color: 'text-gray-500',
		activityText: 'created Laytime',
		icon: PenLine
	},
	{
		value: 'pending_approval',
		label: 'Pending approval',
		color: 'text-amber-500',
		activityText: 'submitted Laytime for approval',
		icon: Clock
	},
	{
		value: 'submitted',
		label: 'Submitted',
		color: 'text-blue-500',
		activityText: '',
		icon: SendHorizonal
	},
	{
		value: 'approved',
		label: 'Approved',
		color: 'text-emerald-500',
		activityText: 'approved Laytime',
		icon: CheckCheck
	},
	{
		value: 'changes_requested',
		label: 'Changes requested',
		color: 'text-red-500',
		activityText: 'requested changes',
		icon: CircleStop
	}
];
