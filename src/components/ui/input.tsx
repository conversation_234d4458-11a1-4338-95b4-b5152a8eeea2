import * as React from 'react';

import { cn } from '@/lib/utils';

const Input = React.forwardRef<
	HTMLInputElement,
	React.ComponentProps<'input'> & { prefix?: string; suffix?: string; icon?: React.ElementType }
>(({ className, type, icon: Icon, ...props }, ref) => (
	<div className="relative">
		<span className="text-muted-foreground absolute top-1/2 left-2 -translate-y-1/2 text-xs">{props.prefix}</span>
		{Icon && <Icon className="text-muted-foreground absolute top-0 bottom-0 left-3 m-auto size-4" />}
		<input
			ref={ref}
			type={type}
			className={cn(
				`border-input file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-1 focus-visible:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 md:text-sm ${Icon ? 'pl-9' : ''}`,
				className
			)}
			{...props}
		/>
		<span className="text-muted-foreground absolute top-1/2 right-2 -translate-y-1/2 text-xs">{props.suffix}</span>
	</div>
));
Input.displayName = 'Input';

export { Input };
