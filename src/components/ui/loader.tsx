const outer = 16;
const diffPx = '8px';
const colorHex = '#3F5BD6';

export const Loader = () => (
	<div className="flex flex-col items-center">
		<div
			className="animate-spin rounded-full"
			style={{
				height: `${outer * 4}px`,
				width: `${outer * 4}px`,
				background: `radial-gradient(farthest-side, ${colorHex} 94%, #0000) top / ${diffPx} ${diffPx} no-repeat,
						conic-gradient(#0000 30%, ${colorHex})`,
				maskImage: `radial-gradient(farthest-side, #0000 calc(100% - ${diffPx}), #000 0)`,
				WebkitMaskImage: `radial-gradient(farthest-side, #0000 calc(100% - ${diffPx}), #000 0)`
			}}
		/>
	</div>
);
