import * as React from 'react';
import { Check } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider } from '@/components/ui/tooltip';

export interface ComboboxOption {
	value: string;
	label: string;
}

interface ComboboxProps {
	data: ComboboxOption[];
	placeholder: string;
	icon?: React.ElementType;
	onSelect: (value: string) => void;
}

export const ComboboxButton: React.FC<ComboboxProps> = ({ data, placeholder, icon: Icon, onSelect }) => {
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState('');

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger>
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								size="xs"
								variant="outline"
								role="combobox"
								aria-expanded={open}
								className="bg-transparent"
							>
								{Icon && <Icon className="text-muted-foreground size-3.5" strokeWidth={1.5} />}
								<div className="flex-1 overflow-hidden text-left text-xs font-normal text-ellipsis">
									{value ? (
										data.find(item => item.value === value)?.label
									) : (
										<span className="text-muted-foreground">{placeholder}</span>
									)}
								</div>
							</Button>
						</TooltipTrigger>
						<TooltipContent>{placeholder}</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</PopoverTrigger>
			<PopoverContent className="w-full p-0" align="start">
				<Command>
					<CommandInput placeholder="Search..." className="h-9" />
					<CommandList>
						<CommandEmpty>No {placeholder.toLowerCase()} found.</CommandEmpty>
						<CommandGroup>
							{data.map(item => (
								<CommandItem
									key={item.value}
									value={item.value}
									onSelect={currentValue => {
										setValue(currentValue === value ? '' : currentValue);
										onSelect(currentValue === value ? '' : currentValue);
										setOpen(false);
									}}
								>
									{item.label}
									<Check
										className={cn('ml-auto', value === item.value ? 'opacity-100' : 'opacity-0')}
									/>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
};
