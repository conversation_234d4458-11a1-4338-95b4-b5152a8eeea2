import * as React from 'react';
import { Check, ChevronsUpDown, Ship } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

const vessels = [
	{
		value: '1',
		label: 'mv Meridiaan Express'
	},
	{
		value: '2',
		label: 'mv Meridiaan Cinco'
	},
	{
		value: '3',
		label: 'mv Vertom Meridiaan'
	},
	{
		value: '4',
		label: 'mv Gulf Meridiaan'
	},
	{
		value: '5',
		label: 'mv Astra Meridiaan'
	}
];

export function ComboboxDemo() {
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState('');

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-start px-3">
					<Ship className="opacity-50" />
					<div className="flex-1 overflow-hidden text-left text-ellipsis">
						{value ? vessels.find(framework => framework.value === value)?.label : 'Vessel'}
					</div>
					<ChevronsUpDown className="opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="j-full w-full p-0">
				<Command>
					<CommandInput placeholder="Search vessel / IMO" className="h-9" />
					<CommandList>
						<CommandEmpty>No vessel found.</CommandEmpty>
						<CommandGroup>
							{vessels.map(framework => (
								<CommandItem
									key={framework.value}
									value={framework.value}
									onSelect={currentValue => {
										setValue(currentValue === value ? '' : currentValue);
										setOpen(false);
									}}
								>
									{framework.label}
									<Check
										className={cn(
											'ml-auto',
											value === framework.value ? 'opacity-100' : 'opacity-0'
										)}
									/>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
