import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import type { LucideIcon } from 'lucide-react';
import { Button } from './button';

interface AnimatedButtonProps {
	icon: LucideIcon;
	label: string;
	onClick?: () => void;
}

export function AnimatedButton({ icon: Icon, label }: AnimatedButtonProps) {
	const [isHovered, setIsHovered] = useState(false);
	const buttonRef = useRef<HTMLButtonElement>(null);
	const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

	useEffect(() => {
		const updateMousePosition = (e: MouseEvent) => {
			if (buttonRef.current) {
				const rect = buttonRef.current.getBoundingClientRect();
				setMousePosition({
					x: ((e.clientX - rect.left) / rect.width) * 100,
					y: ((e.clientY - rect.top) / rect.height) * 100
				});
			}
		};

		window.addEventListener('mousemove', updateMousePosition);
		return () => window.removeEventListener('mousemove', updateMousePosition);
	}, []);

	return (
		<motion.div className="relative" onHoverStart={() => setIsHovered(true)} onHoverEnd={() => setIsHovered(false)}>
			<motion.div
				className="pointer-events-none absolute inset-[-1px] rounded-full"
				style={{
					opacity: isHovered ? 1 : 0,
					background: 'transparent',
					transition: 'opacity 0.15s'
				}}
			>
				<div
					className="absolute inset-0 rounded-full border border-[rgba(61,89,214,0.6)] dark:border-[rgba(61,89,214,0.9)]"
					style={{
						background: 'transparent',
						clipPath: 'inset(0px round 9999px)',
						maskImage: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, black, transparent 100%)`
					}}
				/>
			</motion.div>
			<Button
				ref={buttonRef}
				aria-label={label}
				className="outline-border hover:bg-secondary text-muted-foreground h-8 shrink-0 cursor-pointer items-center justify-center gap-1.5 rounded-full bg-transparent font-normal outline-1 select-none dark:hover:bg-slate-900"
			>
				<Icon className="size-[16px]" />
				{label}
			</Button>
		</motion.div>
	);
}
