import { addDays, format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { DateRange } from 'react-day-picker';
import { Calendar } from './calendar';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

export function DatePicker({
	date,
	setDate,
	placeholder,
	className
}: {
	date: Date | undefined;
	setDate: (date: Date | undefined) => void;
	placeholder?: string;
	className?: string;
}) {
	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button
					variant={'outline'}
					className={cn(
						'h-8 w-full justify-between px-3 text-left font-normal',
						!date && 'text-muted-foreground',
						className
					)}
				>
					{date ? format(date, 'd MMM yy') : <span>{placeholder || 'Pick a date'}</span>}
					<CalendarIcon strokeWidth={1} />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-auto p-0" align="start">
				<Calendar
					mode="single"
					selected={date}
					initialFocus
					className="rounded-md border"
					onSelect={date => setDate(date)}
				/>
			</PopoverContent>
		</Popover>
	);
}

export function DatePickerWithRange({
	className,
	date = { from: new Date(2022, 0, 20), to: addDays(new Date(2022, 0, 20), 20) },
	setDate
}: React.HTMLAttributes<HTMLDivElement> & { date: DateRange; setDate: (date: DateRange) => void }) {
	return (
		<div className={cn('grid gap-2', className)}>
			<Popover>
				<PopoverTrigger asChild>
					<Button
						id="date"
						variant={'outline'}
						className={cn(
							'h-8 w-full justify-between px-3 text-left font-normal',
							!date && 'text-muted-foreground',
							className
						)}
					>
						{date?.from ? (
							date.to ? (
								<>
									{format(date.from, 'd MMM yy')} - {format(date.to, 'd MMM yy')}
								</>
							) : (
								format(date.from, 'd MMM yy')
							)
						) : (
							<span>Pick a date</span>
						)}
						<CalendarIcon strokeWidth={1} />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-auto p-0" align="start">
					<Calendar
						initialFocus
						mode="range"
						defaultMonth={date?.from}
						selected={date}
						numberOfMonths={2}
						className="rounded-md border"
						onSelect={date => setDate(date ?? { from: new Date(), to: new Date() })}
					/>
				</PopoverContent>
			</Popover>
		</div>
	);
}
