import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

export interface ComboboxOption {
	value: string;
	label: string;
}

interface ComboboxProps {
	data: ComboboxOption[];
	placeholder: string;
	className?: string;
	icon?: React.ElementType;
	initialValue?: string;
	onSelect: (value: string) => void;
}

export const ComboboxButtonInline: React.FC<ComboboxProps> = ({
	data,
	placeholder,
	className,
	icon: Icon,
	initialValue,
	onSelect
}) => {
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState(initialValue || '');

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					size="xs"
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className={cn(
						'aria-expanded:bg-accent group/combo aria-expanded:ring-primary border-none bg-transparent text-sm overflow-ellipsis shadow-none aria-expanded:ring-1',
						className
					)}
				>
					{Icon && <Icon className="text-muted-foreground size-3.5" strokeWidth={1.5} />}
					<div className="flex-1 overflow-hidden text-left text-ellipsis">
						{value ? (
							data.find(item => item.value === value)?.label
						) : (
							<span className="text-muted-foreground">{placeholder}</span>
						)}
					</div>
					<ChevronsUpDown className="text-muted-foreground opacity-0 group-hover/combo:opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-full p-0" align="start">
				<Command>
					<CommandInput placeholder="Search..." className="h-9" />
					<CommandList>
						<CommandEmpty>No {placeholder.toLowerCase()} found.</CommandEmpty>
						<CommandGroup>
							{data.map(item => (
								<CommandItem
									key={item.value}
									value={item.value}
									onSelect={currentValue => {
										setValue(currentValue === value ? '' : currentValue);
										onSelect(currentValue === value ? '' : currentValue);
										setOpen(false);
									}}
								>
									{item.label}
									<Check
										className={cn('ml-auto', value === item.value ? 'opacity-100' : 'opacity-0')}
									/>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
};
