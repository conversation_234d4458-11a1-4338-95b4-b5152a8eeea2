import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

export interface ComboboxOption {
	value: string;
	label: string;
}

interface ComboboxProps {
	initialValue?: string;
	data: ComboboxOption[];
	placeholder: string;
	id?: string;
	icon?: React.ElementType;
	onSelect: (value: string) => void;
	className?: string;
	search?: boolean;
}

export const Combobox: React.FC<ComboboxProps> = ({
	initialValue,
	data,
	placeholder,
	id,
	icon: Icon,
	onSelect,
	className,
	search = true
}) => {
	const [open, setOpen] = React.useState(false);
	const [value, setValue] = React.useState(initialValue || '');

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					id={id}
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className={cn('w-full justify-start px-3', className)}
				>
					{Icon && <Icon className="text-muted-foreground" />}
					<div className="flex-1 overflow-hidden text-left text-ellipsis">
						{value ? (
							data.find(item => item.value === value)?.label
						) : (
							<span className="text-muted-foreground">{placeholder}</span>
						)}
					</div>
					<ChevronsUpDown className="opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="j-full w-full p-0">
				<Command>
					{search && <CommandInput placeholder="Search..." className="h-9" />}
					<CommandList>
						<CommandEmpty>No {placeholder.toLowerCase()} found.</CommandEmpty>
						<CommandGroup>
							{data.map(item => (
								<CommandItem
									key={item.value}
									value={item.value}
									onSelect={currentValue => {
										setValue(currentValue === value ? '' : currentValue);
										onSelect(currentValue === value ? '' : currentValue);
										setOpen(false);
									}}
								>
									{item.label}
									<Check
										className={cn('ml-auto', value === item.value ? 'opacity-100' : 'opacity-0')}
									/>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
};
