import { Sparkles } from 'lucide-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useSidebar } from '../ui/sidebar';
import {
	CommandDialog,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
	CommandShortcut
} from '@/components/ui/command';
import { DialogTitle } from '@/components/ui/dialog';

export function CommandSearchDialog() {
	const navigate = useNavigate();
	const { searchOpen, setSearchOpen } = useSidebar();

	useEffect(() => {
		const down = (e: KeyboardEvent) => {
			if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
				e.preventDefault();
				setSearchOpen(prev => !prev);
			}
		};

		document.addEventListener('keydown', down);
		return () => document.removeEventListener('keydown', down);
	}, [setSearchOpen]);

	const handleSelect = async (path: string) => {
		await navigate(path);
		setSearchOpen(false);
	};

	return (
		<CommandDialog open={searchOpen} onOpenChange={setSearchOpen}>
			<div className="flex gap-2 p-3 pb-0">
				<div className="border-inset text-primary cursor-pointer rounded-[4px] border border-indigo-300 bg-indigo-100 p-1 text-xs leading-none font-medium dark:border-slate-800 dark:bg-slate-900">
					Search
				</div>
				<div className="border-inset bg-muted text-muted-foreground flex cursor-pointer gap-1 rounded-[4px] border p-1 text-xs leading-none font-medium dark:bg-slate-900">
					<Sparkles className="h-3 w-3" />
					Ask AI
				</div>
			</div>
			<DialogTitle>
				<CommandInput className="text-base" placeholder="Type a command or search..." />
			</DialogTitle>
			<CommandList>
				<CommandEmpty>No results found.</CommandEmpty>
				<CommandGroup heading="Suggestions">
					<CommandItem onSelect={() => handleSelect('/operations/port-calls')}>
						<span>Port Calls</span>
					</CommandItem>
					<CommandItem onSelect={() => handleSelect('/operations/appointments')}>
						<span>Appointments</span>
					</CommandItem>
					<CommandItem onSelect={() => handleSelect('/financial/disbursements')}>
						<span>Disbursements</span>
					</CommandItem>
					<CommandItem onSelect={() => handleSelect('/financial/payments')}>
						<span>Payments</span>
					</CommandItem>
					<CommandItem onSelect={() => handleSelect('/financial/cashflow')}>
						<span>Cashflow</span>
					</CommandItem>
					<CommandItem onSelect={() => handleSelect('/laytime')}>
						<span>Laytime</span>
					</CommandItem>
				</CommandGroup>
				<CommandSeparator />
				<CommandGroup heading="Settings">
					<CommandItem onSelect={() => handleSelect('/settings')}>
						<span>Settings</span>
						<CommandShortcut>⌘S</CommandShortcut>
					</CommandItem>
					<CommandItem>
						<span>Billing</span>
						<CommandShortcut>⌘B</CommandShortcut>
					</CommandItem>
					<CommandItem>
						<span>Notifications</span>
						<CommandShortcut>⌘N</CommandShortcut>
					</CommandItem>
				</CommandGroup>
			</CommandList>
		</CommandDialog>
	);
}
