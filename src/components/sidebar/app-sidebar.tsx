import * as React from 'react';
import {
	BookOpen,
	BriefcaseBusiness,
	DollarSign,
	FileSpreadsheet,
	Home,
	Inbox,
	Scale,
	Ship,
	Search,
	Sparkles
} from 'lucide-react';
import { Link } from 'react-router';
import { useAuthenticatedUser } from '@abraxa/authentication';

import { NavCommon } from '@/components/sidebar/nav-common';
import { NavMain } from '@/components/sidebar/nav-main';
import { NavSecondary } from '@/components/sidebar/nav-secondary';
import { NavUser } from '@/components/sidebar/nav-user';
import { Separator } from '@/components/ui/separator';
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem
} from '@/components/ui/sidebar';

const principalSidebar = {
	navCommon: [
		{
			title: 'Search',
			url: '',
			icon: Search,
			shortcut: 'K'
		},
		{
			title: 'Home',
			url: '/',
			icon: Home,
			shortcut: ''
		},
		{
			title: 'Inbox',
			url: '/inbox',
			icon: Inbox,
			shortcut: ''
		},
		{
			title: 'Operator One',
			url: '/ai',
			icon: Sparkles,
			badge: 'AI',
			shortcut: ''
		}
	],
	navMain: [
		{
			title: 'Operations',
			url: '#',
			icon: BriefcaseBusiness,
			isActive: true,
			items: [
				{
					title: 'Voyages',
					url: '/operations/voyages'
				},
				{
					title: 'Port Calls',
					url: '/operations/port-calls'
				},
				{
					title: 'Appointments',
					url: '/operations/appointments'
				},
				{
					title: 'Enquiries',
					url: '/operations/enquiries'
				}
			]
		},
		{
			title: 'Financial',
			url: '#',
			icon: DollarSign,
			isActive: true,
			items: [
				{
					title: 'Disbursements',
					badge: '4',
					url: '/financial/disbursements'
				},
				{
					title: 'Cashflow',
					url: '/financial/cashflow'
				}
			]
		},
		{
			title: 'Quick Quote',
			url: '/quick-quote',
			icon: FileSpreadsheet
		},
		{
			title: 'Directory',
			url: '#',
			icon: BookOpen,
			isActive: true,
			items: [
				{
					title: 'Agents',
					url: '/directory/agents'
				},
				{
					title: 'Ports',
					url: '/directory/ports'
				}
			]
		},
		{
			title: 'Laytime',
			url: '/laytime',
			icon: Scale
		},
		{
			title: 'My Fleet',
			url: '/my-fleet',
			icon: Ship
		}
	],
	navSecondary: [
		{
			title: 'Laytime Admin',
			url: '/admin/laytime',
			icon: Scale
		}
	]
};

export const AppSidebar = ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
	const { user, logout } = useAuthenticatedUser();

	return (
		<Sidebar variant="inset" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton size="lg" asChild>
							<Link to="/">
								<div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
									<svg
										width="32"
										height="32"
										viewBox="0 0 32 32"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
									>
										<path
											d="M0 8C0 3.58172 3.58172 0 8 0H24C28.4183 0 32 3.58172 32 8V24C32 28.4183 28.4183 32 24 32H8C3.58172 32 0 28.4183 0 24V8Z"
											fill="bg-sidebar-primary"
										/>
										<path
											d="M23.7805 21.3325L20.1305 19.296L15.992 12.6015L12.482 18.28L17.732 17.26L10.592 21.3325H8.19849L14.338 11.401C14.477 11.1761 14.6712 10.9906 14.9022 10.8619C15.1331 10.7333 15.3931 10.6658 15.6575 10.666H16.3215C16.5859 10.6659 16.8459 10.7334 17.0769 10.8621C17.3079 10.9909 17.502 11.1766 17.641 11.4015L23.7805 21.3325Z"
											fill="white"
										/>
									</svg>
								</div>
								<div className="grid flex-1 text-left text-sm leading-tight">
									<span className="truncate font-semibold">Houston Legal</span>
									<span className="text-muted-foreground truncate text-xs"><EMAIL></span>
								</div>
							</Link>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<>
					<NavCommon items={principalSidebar.navCommon} />
					<Separator />
					<NavMain items={principalSidebar.navMain} />
					{process.env.NODE_ENV === 'development' && (
						<NavSecondary items={principalSidebar.navSecondary} className="mt-auto" />
					)}
				</>
			</SidebarContent>
			{user && (
				<SidebarFooter>
					<NavUser
						user={{
							name: user.fullName,
							email: user.email,
							avatar: user.profileImage
						}}
						onLogout={logout}
					/>
				</SidebarFooter>
			)}
		</Sidebar>
	);
};
