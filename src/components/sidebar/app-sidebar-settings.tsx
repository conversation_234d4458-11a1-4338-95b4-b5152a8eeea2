import * as React from 'react';
import {
	Bell,
	BookOpen,
	Building,
	Building2,
	CreditCard,
	MoveLeft,
	Plug,
	Settings2,
	User,
	Users,
	WalletCards,
	Workflow
} from 'lucide-react';
import { Link } from 'react-router';

import { NavBillingSettings } from './nav-billing-settings';
import { NavPersonalSettings } from './nav-personal-settings';
import { NavAccountSettings } from '@/components/sidebar/nav-account-settings';
import { Separator } from '@/components/ui/separator';
import {
	Sidebar,
	SidebarContent,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem
} from '@/components/ui/sidebar';

const data = {
	navAccount: [
		{
			title: 'Account settings',
			url: '',
			icon: Building2,
			isActive: true,
			items: [
				{
					title: 'Company profile',
					url: ''
				},
				{
					title: 'System settings',
					url: ''
				},
				{
					title: 'Email settings',
					url: '/settings/email/templates'
				},
				{
					title: 'Security',
					url: ''
				}
			]
		},
		{
			title: 'Offices',
			url: '',
			icon: Building
		},
		{
			title: 'Users & Teams',
			url: '',
			icon: Users,
			isActive: true,
			items: [
				{
					title: 'Users',
					url: ''
				},
				{
					title: 'Roles',
					url: ''
				},
				{
					title: 'Teams',
					url: ''
				}
			]
		},
		{
			title: 'Library',
			url: '',
			icon: BookOpen,
			isActive: true,
			items: [
				{
					title: 'Services',
					url: ''
				},
				{
					title: 'Cost center',
					url: ''
				}
			]
		},
		{
			title: 'Integrations',
			url: '',
			icon: Plug
		},
		{
			title: 'Workflows',
			url: '',
			icon: Workflow
		}
	],
	navBilling: [
		{
			title: 'Subscription',
			url: '',
			icon: WalletCards
		},
		{
			title: 'Billing',
			url: '',
			icon: CreditCard
		}
	],
	navPersonal: [
		{
			title: 'My profile',
			url: '',
			icon: User
		},
		{
			title: 'Preferences',
			url: '',
			icon: Settings2
		},
		{
			title: 'Notifications',
			url: '',
			icon: Bell
		}
	]
};

export function AppSidebarSettings({ ...props }: React.ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar variant="inset" {...props}>
			<SidebarHeader>
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton size="lg" asChild>
							<Link to="/">
								<MoveLeft />
								<span className="truncate text-sm leading-tight">Back to workspace</span>
							</Link>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>
			<SidebarContent>
				<Separator />
				<NavAccountSettings items={data.navAccount} />
				<Separator />
				<NavBillingSettings items={data.navBilling} />
				<Separator />
				<NavPersonalSettings items={data.navPersonal} />
			</SidebarContent>
		</Sidebar>
	);
}
