import { ChevronRight, type LucideIcon } from 'lucide-react';
import { Link, useLocation } from 'react-router';
import { Badge } from '../ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
	SidebarGroup,
	SidebarMenu,
	SidebarMenuAction,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	useSidebar
} from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';

export function NavCommon({
	items
}: {
	items: {
		title: string;
		url: string;
		icon: LucideIcon;
		isActive?: boolean;
		shortcut: string;
		badge?: string;
		items?: {
			title: string;
			url: string;
		}[];
	}[];
}) {
	const location = useLocation();
	const { setSearchOpen } = useSidebar();
	return (
		<SidebarGroup>
			<SidebarMenu>
				{items.map(item => (
					<Collapsible key={item.title} asChild defaultOpen={item.isActive}>
						<SidebarMenuItem>
							<SidebarMenuButton
								asChild
								tooltip={item.title}
								className={cn(location.pathname === item.url && 'bg-active text-active-foreground')}
								onClick={item.title === 'Search' ? () => setSearchOpen(true) : undefined}
							>
								<Link to={item.url}>
									<item.icon />
									<span className="flex-1">{item.title}</span>
									{item.shortcut ? (
										<kbd className="bg-muted text-muted-foreground pointer-events-none inline-flex h-5 items-center gap-1 rounded border px-1 font-mono text-[10px] font-medium opacity-100 select-none">
											<span className="text-xs">⌘</span>
											{item.shortcut}
										</kbd>
									) : (
										''
									)}
									{item.badge ? (
										<Badge variant="default" className="h-4 min-w-4 rounded-sm px-1 py-0 text-xs">
											{item.badge}
										</Badge>
									) : (
										''
									)}
								</Link>
							</SidebarMenuButton>
							{item.items?.length ? (
								<>
									<CollapsibleTrigger asChild>
										<SidebarMenuAction className="data-[state=open]:rotate-90">
											<ChevronRight />
											<span className="sr-only">Toggle</span>
										</SidebarMenuAction>
									</CollapsibleTrigger>
									<CollapsibleContent>
										<SidebarMenuSub>
											{item.items?.map(subItem => (
												<SidebarMenuSubItem key={subItem.title}>
													<SidebarMenuSubButton
														asChild
														className={cn(
															location.pathname === subItem.url &&
																'bg-active text-active-foreground'
														)}
													>
														<Link to={subItem.url}>
															<span>{subItem.title}</span>
														</Link>
													</SidebarMenuSubButton>
												</SidebarMenuSubItem>
											))}
										</SidebarMenuSub>
									</CollapsibleContent>
								</>
							) : null}
						</SidebarMenuItem>
					</Collapsible>
				))}
			</SidebarMenu>
		</SidebarGroup>
	);
}
