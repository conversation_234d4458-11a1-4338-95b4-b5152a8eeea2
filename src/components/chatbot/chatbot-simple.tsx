import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Button } from '@/components/ui/button';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';

interface Message {
	text: string;
	sender: 'user' | 'bot';
	id: string;
}

// Define API response types
interface ChatResponse {
	reply: string;
}

interface ErrorResponse {
	error?: string;
	message?: string;
}

interface Props {
	onOpenChatPanel?: () => void;
}

const ChatbotSimple: React.FC<Props> = ({ onOpenChatPanel }) => {
	const [messages, setMessages] = useState<Message[]>([]);
	const [input, setInput] = useState<string>('');
	const [loading, setLoading] = useState<boolean>(false);

	const sendMessage = async () => {
		const newMessage: Message = {
			text: input,
			sender: 'user',
			id: `user-${Date.now()}` // need a unique id for react key.
		};
		setMessages([...messages, newMessage]);
		setInput('');
		setLoading(true);

		try {
			const response = await fetch('/static/api/chat', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ message: input })
			});

			if (!response.ok) {
				const errorData = (await response.json()) as ErrorResponse;
				console.error('Error response:', errorData);
				throw new Error('Network response was not ok');
			}

			const data = (await response.json()) as ChatResponse;
			setMessages([
				...messages,
				newMessage,
				{
					text: data.reply,
					sender: 'bot',
					id: `bot-${Date.now()}`
				}
			]);
		} catch (error) {
			console.error('Error:', error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="flex w-full flex-col items-center justify-end gap-4">
			{messages.length ? (
				<div className="flex w-full flex-1 flex-col px-6">
					<div className="flex w-full flex-1 flex-col items-center justify-end gap-4">
						{messages.map(msg => (
							<div key={msg.id} className="w-full max-w-3xl py-4">
								{msg.sender === 'user' ? (
									<div className="bg-muted justify-self-end rounded-lg px-4 py-2">
										<div className="ai-markdown gap-6 text-base/7">
											<ReactMarkdown>{msg.text}</ReactMarkdown>
										</div>
									</div>
								) : (
									<div className="flex w-full items-start gap-6 justify-self-start rounded-lg">
										<span className="bg-muted rounded-full p-3">
											<Sparkles className="size-4" />
										</span>
										<div className="ai-markdown gap-6 text-base/7">
											<ReactMarkdown>{msg.text}</ReactMarkdown>
										</div>
									</div>
								)}
							</div>
						))}
						{loading && (
							<div className="w-full max-w-3xl py-4">
								<div className="flex items-center gap-6 justify-self-start rounded-lg">
									<span className="bg-muted rounded-full p-3">
										<Sparkles className="size-4" />
									</span>
									<div className="text-base/7">
										<div className="dot-pulse"></div>
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			) : (
				''
			)}
			<div className="flex w-full flex-col items-center justify-end gap-6 px-6 py-4">
				<div className="w-full rounded-xl border focus-within:border-slate-300 dark:bg-gray-900 dark:focus-within:border-slate-700">
					<div className="flex items-end">
						<AutosizeTextarea
							// autoFocus
							value={input}
							className="h-12 min-h-12 resize-none overflow-auto rounded-xl border-none bg-transparent p-4 pt-3 pb-0 text-sm ring-0 outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
							placeholder="Ask Operator One anything..."
							onChange={e => setInput(e.target.value)}
							onClick={onOpenChatPanel}
							onKeyDown={e => {
								if (e.key === 'Enter' && !e.shiftKey) {
									e.preventDefault();
									void sendMessage();
								}
							}}
						/>
						<div className="p-3">
							<Button
								variant="default"
								size="icon"
								disabled={loading}
								className="h-7 w-7"
								onClick={() => void sendMessage()}
							>
								<ArrowUp />
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ChatbotSimple;
