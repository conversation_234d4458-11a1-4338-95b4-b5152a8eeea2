import { ChartColumnBig, DollarSign, MoreHorizontal, Route, Scale } from 'lucide-react';
import { AnimatedButton } from '@/components/ui/animated-button';

export default function Prompts({ className = '' }) {
	return (
		<div className={className}>
			<AnimatedButton icon={Route} label="Plan a Voyage" />
			<AnimatedButton icon={DollarSign} label="Check Prices" />
			<AnimatedButton icon={Scale} label="Get Laytime" />
			<AnimatedButton icon={ChartColumnBig} label="Insights" />
			<AnimatedButton icon={MoreHorizontal} label="More" />
		</div>
	);
}
