import { Circle, CircleCheck, CircleCheckBig, ChevronDown, FolderGit2, Paperclip, Settings2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

const data = {
	tasks: [
		{
			id: '1',
			status: 'todo',
			name: 'Check PDA is paid',
			type: 'task'
		},
		{
			id: '2',
			status: 'todo',
			name: 'Check original BsL',
			type: 'task'
		},
		{
			id: '3',
			status: 'todo',
			name: 'Arrange off-hire bunker',
			type: 'task'
		},
		{
			id: '4',
			status: 'todo',
			name: 'Arrange bunker survey',
			type: 'task'
		},
		{
			id: '5',
			status: 'todo',
			name: 'Confirm bunker quantity',
			type: 'task'
		},
		{
			id: '6',
			status: 'done',
			name: 'Daily update to the Charterers',
			type: 'task'
		}
	]
};

export default function ChatbotToolbar() {
	const { tasks } = data;

	return (
		<div className="flex gap-1">
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
							<Paperclip className="size-3.5" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Attach files, C/Ps, SOFs, etc.</TooltipContent>
				</Tooltip>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
							<FolderGit2 className="size-3.5" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Workflows</TooltipContent>
				</Tooltip>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button variant="ghost" size="icon" className="text-muted-foreground h-7 w-7">
							<Settings2 className="size-3.5" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Preferences</TooltipContent>
				</Tooltip>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="outline"
							size="xs"
							className="text-muted-foreground h-7 rounded-full bg-transparent"
						>
							<CircleCheckBig className="size-3.5" />
							Tasks
							<ChevronDown className="-ml-0.5 size-3" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="start">
						<DropdownMenuLabel>Next Tasks</DropdownMenuLabel>
						{tasks.map(task => (
							<DropdownMenuItem key={task.id}>
								{task.status === 'todo' ? (
									<>
										<Circle className="text-muted-foreground" />
										<div className="font-normal text-wrap">{task.name}</div>
									</>
								) : (
									<>
										<CircleCheck className="text-background fill-primary" />
										<div className="text-muted-foreground font-normal text-wrap line-through">
											{task.name}
										</div>
									</>
								)}
							</DropdownMenuItem>
						))}
					</DropdownMenuContent>
				</DropdownMenu>
			</TooltipProvider>
		</div>
	);
}
