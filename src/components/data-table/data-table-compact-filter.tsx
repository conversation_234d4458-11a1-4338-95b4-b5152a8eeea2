import * as React from 'react';
import { Column } from '@tanstack/react-table';
import { ListFilter, X } from 'lucide-react';

import { DataTableFilterCommand } from './data-table-filter-command';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface DataTableCompactFilterProps<TData, TValue> {
	column?: Column<TData, TValue>;
	title?: string;
	options: {
		label: string;
		value: string;
		icon?: React.ComponentType<{ className?: string }>;
	}[];
	icon?: React.ComponentType<{ className?: string }>;
}

export function DataTableCompactFilter<TData, TValue>({
	column,
	title,
	options,
	icon
}: DataTableCompactFilterProps<TData, TValue>) {
	const selectedValues = new Set(column?.getFilterValue() as string[]);

	return (
		<Popover>
			<PopoverTrigger asChild>
				{selectedValues?.size > 0 && (
					<div className="text-muted-foreground bg-accent border-input mr-2 inline-flex h-7 cursor-pointer items-center justify-center gap-0 overflow-hidden rounded-md border px-0 text-xs font-medium whitespace-nowrap shadow-xs">
						<div className="flex items-center gap-2 px-2">
							{icon ? (
								React.createElement(icon, { className: 'size-3' })
							) : (
								<ListFilter className="size-3" />
							)}
							{title}
						</div>

						<Separator orientation="vertical" className="mx-0 h-7" />
						<Badge
							variant="secondary"
							className="h-7 rounded-none bg-transparent px-2 font-normal lg:hidden"
						>
							{selectedValues.size}
						</Badge>
						<div className="hidden space-x-1 lg:flex">
							{selectedValues.size > 1 ? (
								<Badge variant="secondary" className="h-7 rounded-none bg-transparent px-2 font-normal">
									{selectedValues.size} selected
								</Badge>
							) : (
								options
									.filter(option => selectedValues.has(option.value))
									.map(option => (
										<Badge
											key={option.value}
											variant="secondary"
											className="h-7 rounded-none bg-transparent px-2 font-normal"
										>
											{option.label}
										</Badge>
									))
							)}
						</div>
						<Separator orientation="vertical" className="mx-0 h-7" />
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<Button
										size="icon"
										variant="ghost"
										className="h-7 w-7 rounded-none p-0"
										onClick={() => column?.setFilterValue(undefined)}
									>
										<X className="size-3" />
									</Button>
								</TooltipTrigger>
								<TooltipContent>Remove Filter</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					</div>
				)}
			</PopoverTrigger>
			<PopoverContent className="w-fit p-0" align="start">
				{selectedValues?.size > 0 && <DataTableFilterCommand column={column} title={title} options={options} />}
			</PopoverContent>
		</Popover>
	);
}
