import { Table } from '@tanstack/react-table';
import { Download, ListFilter } from 'lucide-react';

import { DataTableDisplayOptions } from './data-table-display-options';
import { Button } from '@/components/ui/button';
import { DataTableSearch } from '@/components/data-table/data-table-search';

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
}

export function DataTableToolbar<TData>({ table }: DataTableToolbarProps<TData>) {
	return (
		<div className="flex items-center justify-between border-t border-b py-2">
			<div className="flex flex-1 items-center space-x-2">
				<Button variant="ghost" size="xs">
					<ListFilter />
					Filter
				</Button>
			</div>
			<div className="flex items-center gap-2">
				<DataTableSearch table={table} />
				<Button variant="ghost" size="xs">
					<Download />
					Export
				</Button>
				<DataTableDisplayOptions table={table} />
			</div>
		</div>
	);
}
