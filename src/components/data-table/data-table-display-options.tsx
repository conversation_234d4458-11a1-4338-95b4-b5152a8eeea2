import { Table } from '@tanstack/react-table';
import { BetweenHorizontalStart, Settings2 } from 'lucide-react';

import { Label } from '../ui/label';
import { Separator } from '../ui/separator';
import { Button } from '@/components/ui/button';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface ColumnMeta {
	label?: string;
}

interface DataTableDisplayOptionsProps<TData> {
	table: Table<TData>;
}

export function DataTableDisplayOptions<TData>({ table }: DataTableDisplayOptionsProps<TData>) {
	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button variant="ghost" size="xs" className="ml-auto hidden h-7 lg:flex">
					<Settings2 />
					Display
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-82 p-0" align="end">
				<div className="p-4">
					<h5 className="text-sm leading-none font-medium">Display options</h5>
				</div>
				<Separator className="bg-border/50" />
				<div className="flex items-center gap-2 p-4">
					<Label className="text-muted-foreground flex flex-1 items-center gap-2 text-xs">
						<BetweenHorizontalStart className="size-3.5" /> Group by
					</Label>
					<Select>
						<SelectTrigger className="h-8 w-1/2 text-xs">
							<SelectValue placeholder="None" defaultValue="none" />
						</SelectTrigger>
						<SelectContent className="bg-background">
							<SelectGroup>
								<SelectItem value="none" defaultChecked className="text-xs">
									None
								</SelectItem>
								<SelectItem value="vessel" className="text-xs">
									Vessel
								</SelectItem>
								<SelectItem value="status" className="text-xs">
									Status
								</SelectItem>
								<SelectItem value="operator" className="text-xs">
									Operator
								</SelectItem>
							</SelectGroup>
						</SelectContent>
					</Select>
				</div>
				<Separator className="bg-border/50" />
				<div className="text-muted-foreground px-4 pt-3 text-xs">Display columns</div>
				<ToggleGroup
					type="multiple"
					variant="default"
					className="flex-wrap gap-1.5 p-4 text-xs"
					size="sm"
					defaultValue={table
						.getAllColumns()
						.filter(
							column =>
								typeof column.accessorFn !== 'undefined' && column.getCanHide() && column.getIsVisible()
						)
						.map(column => column.id)}
					onValueChange={(values: string[]) => {
						// Handle toggling visibility for all columns
						table
							.getAllColumns()
							.filter(column => typeof column.accessorFn !== 'undefined' && column.getCanHide())
							.forEach(column => {
								const isVisible = values.includes(column.id);
								column.toggleVisibility(isVisible);
							});
					}}
				>
					{table
						.getAllColumns()
						.filter(column => typeof column.accessorFn !== 'undefined' && column.getCanHide())
						.map(column => (
							<ToggleGroupItem
								key={column.id}
								size="sm"
								className="text-muted-foreground h-7 min-w-auto flex-none rounded-md text-xs capitalize first:rounded-md last:rounded-md"
								value={column.id}
							>
								{(column.columnDef.meta as ColumnMeta | undefined)?.label || column.id}
							</ToggleGroupItem>
						))}
				</ToggleGroup>
				<Separator className="bg-border/50" />
				<div className="flex items-center justify-end gap-2 px-4 py-3">
					<Button variant="ghost" size="xs" className="text-muted-foreground">
						Reset to default
					</Button>
				</div>
			</PopoverContent>
		</Popover>
	);
}
