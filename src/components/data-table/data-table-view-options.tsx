import { useState } from 'react';
import { Table } from '@tanstack/react-table';
import { BetweenHorizontalStart, Eye, Settings2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuRadioGroup,
	DropdownMenuRadioItem,
	DropdownMenuSeparator,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuPortal,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface ColumnMeta {
	label?: string;
}

interface DataTableViewOptionsProps<TData> {
	table: Table<TData>;
}

export function DataTableViewOptions<TData>({ table }: DataTableViewOptionsProps<TData>) {
	const [groupBy, setGroupBy] = useState<string>('none');

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" size="xs" className="ml-auto hidden h-7 lg:flex">
					<Settings2 />
					Customize
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-[180px]">
				<DropdownMenuLabel>Customize columns</DropdownMenuLabel>
				<DropdownMenuSeparator />
				<DropdownMenuSub>
					<DropdownMenuSubTrigger>
						<BetweenHorizontalStart />
						Group by
					</DropdownMenuSubTrigger>
					<DropdownMenuPortal>
						<DropdownMenuSubContent>
							<DropdownMenuRadioGroup value={groupBy} onValueChange={setGroupBy}>
								<DropdownMenuRadioItem value="none" onSelect={event => event.preventDefault()}>
									None
								</DropdownMenuRadioItem>
								<DropdownMenuRadioItem value="vessel" onSelect={event => event.preventDefault()}>
									Vessel
								</DropdownMenuRadioItem>
								<DropdownMenuRadioItem value="status" onSelect={event => event.preventDefault()}>
									Status
								</DropdownMenuRadioItem>
							</DropdownMenuRadioGroup>
						</DropdownMenuSubContent>
					</DropdownMenuPortal>
				</DropdownMenuSub>
				<DropdownMenuSeparator />
				<DropdownMenuSub>
					<DropdownMenuSubTrigger>
						<Eye />
						Display
					</DropdownMenuSubTrigger>
					<DropdownMenuPortal>
						<DropdownMenuSubContent>
							{table
								.getAllColumns()
								.filter(column => typeof column.accessorFn !== 'undefined' && column.getCanHide())
								.map(column => (
									<DropdownMenuCheckboxItem
										key={column.id}
										className="capitalize"
										checked={column.getIsVisible()}
										onCheckedChange={value => column.toggleVisibility(!!value)}
										onSelect={event => event.preventDefault()}
									>
										{(column.columnDef.meta as ColumnMeta | undefined)?.label || column.id}
									</DropdownMenuCheckboxItem>
								))}
						</DropdownMenuSubContent>
					</DropdownMenuPortal>
				</DropdownMenuSub>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
