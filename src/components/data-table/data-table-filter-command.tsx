import * as React from 'react';
import { Column } from '@tanstack/react-table';
import { Check } from 'lucide-react';

import { cn } from '@/lib/utils';

import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator
} from '@/components/ui/command';

interface DataTableFacetedFilterProps<TData, TValue> {
	column?: Column<TData, TValue>;
	title?: string;
	options: {
		label: string;
		value: string;
		icon?: React.ComponentType<{ className?: string }>;
	}[];
}

export function DataTableFilterCommand<TData, TValue>({
	column,
	title,
	options
}: DataTableFacetedFilterProps<TData, TValue>) {
	const facets = column?.getFacetedUniqueValues();
	const selectedValues = new Set(column?.getFilterValue() as string[]);

	return (
		<Command>
			<CommandInput placeholder={title} />
			<CommandList>
				<CommandEmpty>No results found.</CommandEmpty>
				<CommandGroup>
					{options.map(option => {
						const isSelected = selectedValues.has(option.value);
						return (
							<CommandItem
								key={option.value}
								onSelect={() => {
									if (isSelected) {
										selectedValues.delete(option.value);
									} else {
										selectedValues.add(option.value);
									}
									const filterValues = Array.from(selectedValues);
									column?.setFilterValue(filterValues.length ? filterValues : undefined);
								}}
							>
								<div
									className={cn(
										'border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border',
										isSelected
											? 'bg-primary text-primary-foreground'
											: 'opacity-50 [&_svg]:invisible'
									)}
								>
									<Check />
								</div>
								{option.icon && <option.icon className="text-muted-foreground mr-2 size-3.5" />}
								<span>{option.label}</span>
								{facets?.get(option.value) && (
									<span className="ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs">
										{facets.get(option.value)}
									</span>
								)}
							</CommandItem>
						);
					})}
				</CommandGroup>
				{selectedValues.size > 0 && (
					<>
						<CommandSeparator />
						<CommandGroup>
							<CommandItem
								className="justify-center text-center"
								onSelect={() => column?.setFilterValue(undefined)}
							>
								Clear filters
							</CommandItem>
						</CommandGroup>
					</>
				)}
			</CommandList>
		</Command>
	);
}
