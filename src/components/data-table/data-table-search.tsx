import { Table } from '@tanstack/react-table';
import { Search } from 'lucide-react';

import { Input } from '@/components/ui/input';

interface DataTableSearchProps<TData> {
	table: Table<TData>;
}

export function DataTableSearch<TData>({ table }: DataTableSearchProps<TData>) {
	return (
		<Input
			placeholder="Search"
			value={(table.getState().globalFilter as string) ?? ''}
			className="hover:bg-accent placeholder:text-foreground focus:placeholder:text-muted-foreground h-7 w-[87px] border-none font-medium transition-all duration-200 hover:cursor-pointer focus:w-[250px] focus:cursor-auto focus:bg-inherit md:text-xs"
			icon={Search}
			onChange={event => table.setGlobalFilter(event.target.value)}
		/>
	);
}
