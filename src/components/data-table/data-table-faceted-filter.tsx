import * as React from 'react';
import { Column } from '@tanstack/react-table';
import { Check, ListFilter, X } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';

interface DataTableFacetedFilterProps<TData, TValue> {
	column?: Column<TData, TValue>;
	title?: string;
	options: {
		label: string;
		value: string;
		icon?: React.ComponentType<{ className?: string }>;
	}[];
}

export function DataTableFacetedFilter<TData, TValue>({
	column,
	title,
	options
}: DataTableFacetedFilterProps<TData, TValue>) {
	const facets = column?.getFacetedUniqueValues();
	const selectedValues = new Set(column?.getFilterValue() as string[]);

	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button variant="outline" size="xs" className="text-muted-foreground bg-accent h-7 gap-0 px-0">
					<div className="flex items-center gap-2 px-2">
						<ListFilter className="size-3" />
						{title}
					</div>
					{selectedValues?.size > 0 && (
						<>
							<Separator orientation="vertical" className="mx-0 h-7" />
							<Badge variant="secondary" className="rounded-sm bg-transparent px-2 font-normal lg:hidden">
								{selectedValues.size}
							</Badge>
							<div className="hidden space-x-1 lg:flex">
								{selectedValues.size > 1 ? (
									<Badge variant="secondary" className="rounded-sm bg-transparent px-2 font-normal">
										{selectedValues.size} selected
									</Badge>
								) : (
									options
										.filter(option => selectedValues.has(option.value))
										.map(option => (
											<Badge
												key={option.value}
												variant="secondary"
												className="rounded-sm bg-transparent px-2 font-normal"
											>
												{option.label}
											</Badge>
										))
								)}
							</div>
							<Separator orientation="vertical" className="mx-0 h-7" />
							<Button
								size="icon"
								variant="ghost"
								className="h-7 w-7 p-0"
								onClick={() => column?.setFilterValue(undefined)}
							>
								<X className="size-3" />
							</Button>
						</>
					)}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-fit p-0" align="start">
				<Command>
					<CommandInput placeholder={title} />
					<CommandList>
						<CommandEmpty>No results found.</CommandEmpty>
						<CommandGroup>
							{options.map(option => {
								const isSelected = selectedValues.has(option.value);
								return (
									<CommandItem
										key={option.value}
										onSelect={() => {
											if (isSelected) {
												selectedValues.delete(option.value);
											} else {
												selectedValues.add(option.value);
											}
											const filterValues = Array.from(selectedValues);
											column?.setFilterValue(filterValues.length ? filterValues : undefined);
										}}
									>
										<div
											className={cn(
												'border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border',
												isSelected
													? 'bg-primary text-primary-foreground'
													: 'opacity-50 [&_svg]:invisible'
											)}
										>
											<Check />
										</div>
										{option.icon && <option.icon className="text-muted-foreground mr-2 h-4 w-4" />}
										<span>{option.label}</span>
										{facets?.get(option.value) && (
											<span className="ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs">
												{facets.get(option.value)}
											</span>
										)}
									</CommandItem>
								);
							})}
						</CommandGroup>
						{selectedValues.size > 0 && (
							<>
								<CommandSeparator />
								<CommandGroup>
									<CommandItem
										className="justify-center text-center"
										onSelect={() => column?.setFilterValue(undefined)}
									>
										Clear filters
									</CommandItem>
								</CommandGroup>
							</>
						)}
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
