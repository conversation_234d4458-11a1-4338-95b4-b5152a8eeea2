import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { SofResult } from './types';
import { SofUpdateInput } from '@/graphql';

export const UPDATE_SOF = gql`
	mutation UpdateSof($id: String!, $set: SofUpdateInput!) {
		updateSof(where: { id: { eq: $id } }, set: $set) {
			id
		}
	}
`;

export const useMutateSof = (options?: MutateOptions) => {
	const [updateSof, { loading, error }] = useMutation(UPDATE_SOF, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleSof']
	});

	const mutateSof = async (id: string, data: Partial<SofUpdateInput>) => {
		try {
			const result = (await updateSof({
				variables: {
					id,
					set: data
				}
			})) as SofResult;
			return result.data?.updateSof;
		} catch (err) {
			console.error('Error updating sof:', err);
			throw err;
		}
	};

	return {
		mutateSof,
		loading,
		error
	};
};
