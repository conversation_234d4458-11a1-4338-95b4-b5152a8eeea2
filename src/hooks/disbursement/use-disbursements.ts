import { useQuery } from '@tanstack/react-query';
import { useGraphqlApiProxy } from '@abraxa/graphql';
import { gql, GetDisbursementsQuery, GetDisbursementsQueryVariables } from '@/graphql';

export const GET_ALL_DISBURSEMENTS = gql(`query getDisbursements{
  disbursement {
    agentId
    agentName
    amount
    cta
    da
    date
    groupId
    id
    portId
    portName
    portCountryCode
    status
    vesselId
    vesselImo
    vesselName
  }
}`);

export const useDisbursements = () => {
	const proxy = useGraphqlApiProxy();
	const { data, isLoading, ...props } = useQuery({
		queryKey: ['disbursements'],
		queryFn: () => proxy<GetDisbursementsQuery, GetDisbursementsQueryVariables>(GET_ALL_DISBURSEMENTS)
	});

	const disbursements = data?.disbursement || [];

	return {
		disbursements,
		loading: isLoading,
		...props
	};
};
