import { useQuery } from '@tanstack/react-query';
import { useGraphqlApiProxy } from '@abraxa/graphql';
import { gql, GetPortCallsQuery, GetPortCallsQueryVariables } from '@/graphql';

export const GET_ALL_PORT_CALLS = gql(`query getPortCalls{
  portCall {
    agentName
    eta
    etd
    fileId
    operatorName
    portFunction
    portName
    portCountryCode
    status
    vesselImo
    vesselName
  }
}`);

export const usePortCalls = () => {
	const proxy = useGraphqlApiProxy();
	const { data, isLoading, ...props } = useQuery({
		queryKey: ['portCalls'],
		queryFn: () => proxy<GetPortCallsQuery, GetPortCallsQueryVariables>(GET_ALL_PORT_CALLS)
	});

	const portCalls = data?.portCall || [];

	return {
		portCalls,
		loading: isLoading,
		...props
	};
};
