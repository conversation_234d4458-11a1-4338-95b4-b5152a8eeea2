import { useQuery } from '@tanstack/react-query';
import { useGraphqlApiProxy } from '@abraxa/graphql';
import { gql, GetCharterersQuery, GetCharterersQueryVariables } from '@/graphql';

export const GET_ALL_CHARTERERS = gql(`query getCharterers{
  charterer {
    id
    name
  }
}`);

export const useCharterers = () => {
	const proxy = useGraphqlApiProxy();
	const { data, isLoading, ...props } = useQuery({
		queryKey: ['charterers'],
		queryFn: () => proxy<GetCharterersQuery, GetCharterersQueryVariables>(GET_ALL_CHARTERERS)
	});

	const charterers = data?.charterer;

	return {
		charterers,
		loading: isLoading,
		...props
	};
};
