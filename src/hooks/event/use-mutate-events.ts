import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { EventResult } from './types';
import { EventUpdateInput } from '@/graphql';

export const UPDATE_EVENT = gql`
	mutation UpdateEvent($id: String!, $set: EventUpdateInput!) {
		updateEvent(where: { id: { eq: $id } }, set: $set) {
			id
		}
	}
`;

export const useMutateEvents = (options?: MutateOptions) => {
	const [updateEvent, { loading, error }] = useMutation(UPDATE_EVENT, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleSof']
	});

	const mutateEvent = async (id: string, data: Partial<EventUpdateInput>) => {
		try {
			const result = (await updateEvent({
				variables: {
					id,
					set: data
				}
			})) as EventResult;
			return result.data?.updateEvent;
		} catch (err) {
			console.error('Error updating event:', err);
			throw err;
		}
	};

	return {
		mutateEvent,
		loading,
		error
	};
};
