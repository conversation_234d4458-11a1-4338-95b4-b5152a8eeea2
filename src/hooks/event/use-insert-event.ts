import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { EventInsertInput, EventItem } from '@/graphql';

export const INSERT_EVENT = gql(`mutation InsertIntoEvent($values: [EventInsertInput!]!) {
  insertIntoEvent(values: $values) {
    id
  }
}`);

interface EventResult {
	data: {
		insertIntoEvent: EventItem;
	};
}

export const useInsertEvent = (options?: MutateOptions) => {
	const [insert, { loading, error }] = useMutation(INSERT_EVENT, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleSof']
	});

	const insertEvent = async (data: EventInsertInput) => {
		try {
			const result = (await insert({
				variables: {
					values: data
				}
			})) as EventResult;
			return result.data?.insertIntoEvent;
		} catch (err) {
			console.error('Error updating event:', err);
			throw err;
		}
	};

	return {
		insertEvent,
		loading,
		error
	};
};
