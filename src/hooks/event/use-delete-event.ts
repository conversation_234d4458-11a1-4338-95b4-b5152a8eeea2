import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { DeleteEventResult } from './types';

export const DELETE_EVENT = gql`
	mutation DeleteEvent($id: String!) {
		deleteFromEvent(where: { id: { eq: $id } }) {
			id
		}
	}
`;

export const useDeleteEvent = (options?: MutateOptions) => {
	const [deleteEvent, { loading, error }] = useMutation(DELETE_EVENT, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleLaytime']
	});

	const removeEvent = async (id: string) => {
		try {
			const result = (await deleteEvent({
				variables: {
					id
				}
			})) as DeleteEventResult;
			return result.data?.deleteFromEvent;
		} catch (err) {
			console.error('Error deleting event:', err);
			throw err;
		}
	};

	return {
		removeEvent,
		loading,
		error
	};
};
