import { useQuery } from '@tanstack/react-query';
import { useGraphqlApiProxy } from '@abraxa/graphql';
import { gql, GetAppointmentsQuery, GetAppointmentsQueryVariables } from '@/graphql';

export const GET_ALL_APPOINTMENTS = gql(`query getAppointments{
  appointment {
    agentName
    eta
    fileId
    operatorName
    portFunction
    portName
    portCountryCode
    status
    vesselImo
    vesselName
    requestDate
  }
}`);

export const useAppointments = () => {
	const proxy = useGraphqlApiProxy();
	const { data, isLoading, ...props } = useQuery({
		queryKey: ['appointments'],
		queryFn: () => proxy<GetAppointmentsQuery, GetAppointmentsQueryVariables>(GET_ALL_APPOINTMENTS)
	});

	const appointments = data?.appointment || [];

	return {
		appointments,
		loading: isLoading,
		...props
	};
};
