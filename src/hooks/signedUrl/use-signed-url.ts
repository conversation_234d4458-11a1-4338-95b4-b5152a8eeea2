import { v4 as uuidv4 } from 'uuid';
import { gql, useMutation } from '@apollo/client';
import { toast } from 'sonner';

const GET_SIGNED_URL = gql`
	mutation Signed($input: SignedUrlInput!) {
		getSignedUrl(input: $input) {
			signedUrl
		}
	}
`;

interface SignedUrlInput {
	fileName: string;
	contentType: string;
}

interface SignedUrlResponse {
	getSignedUrl: {
		signedUrl: string;
	};
}

function getCorrectMimeType(file: File): string {
	const extension = file.name.split('.').pop()?.toLowerCase() || '';

	const mimeTypeMap: Record<string, string> = {
		pdf: 'application/pdf',
		docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		jpg: 'image/jpeg',
		jpeg: 'image/jpeg',
		png: 'image/png'
	};

	// if the browser-detected MIME type is generic or missing, use our mapping
	if (file.type === '' || file.type === 'application/octet-stream') {
		const mappedType = mimeTypeMap[extension];
		return mappedType || 'application/octet-stream';
	}

	return file.type;
}

export function useFileUpload() {
	const [getSignedUrl] = useMutation<SignedUrlResponse, { input: SignedUrlInput }>(GET_SIGNED_URL);

	const uploadFile = async (
		file: File
	): Promise<{ url: string; originalName: string; uuid: string; gcpName: string }> => {
		try {
			const contentType = getCorrectMimeType(file);

			const fileUuid: string = uuidv4();

			const fileExtension = file.name.split('.').pop() || '';

			const gcpName = `${fileUuid}.${fileExtension}`;

			const { data } = await getSignedUrl({
				variables: {
					input: {
						fileName: gcpName,
						contentType
					}
				}
			});

			if (!data?.getSignedUrl.signedUrl) {
				throw new Error('Failed to get signed URL');
			}

			const signedUrl = data.getSignedUrl.signedUrl;

			const response = await fetch(signedUrl, {
				method: 'PUT',
				body: file,
				headers: {
					'Content-Type': contentType
				}
			});

			if (!response.ok) {
				throw new Error(`Upload failed with status: ${response.status} ${response.statusText}`);
			}

			const fileUrl = `https://storage.cloud.google.com/laytime_uploads/${gcpName}`;

			console.log(signedUrl.split('/')[4].split('?')[0]);

			return {
				url: fileUrl,
				originalName: file.name,
				uuid: fileUuid,
				gcpName: signedUrl.split('/')[4].split('.')[0]
			};
		} catch (error) {
			console.error('Error uploading file:', error);
			toast('Upload Failed', {
				description: `Failed to upload ${file.name}. ${error instanceof Error ? error.message : 'Please try again.'}`,
				duration: 5000
			});
			throw error;
		}
	};

	return { uploadFile };
}
