import { gql, useMutation } from '@apollo/client';

const INSERT_CHARTER_PARTY_TERMS = gql`
	mutation InsertCharterPartyTerms($values: CharterPartyTermsInsertInput!) {
		insertIntoCharterPartyTermsSingle(values: $values) {
			id
			loadingRate
			demurrageRate
			dischargingRate
			laytimeAllowed
			laytimeCalculationModifiers
		}
	}
`;

interface CharterPartyTermsItem {
	id: string;
	loadingRate?: number;
	demurrageRate?: number;
	dischargingRate?: number;
	laytimeAllowed: number;
	laytimeCalculationModifiers?: string;
}

interface CharterPartyTermsResult {
	insertIntoCharterPartyTermsSingle: CharterPartyTermsItem | null;
}

interface CharterPartyTermsInsertInput {
	id?: string;
	loadingRate?: number;
	demurrageRate?: number;
	dischargingRate?: number;
	laytimeAllowed: number;
	laytimeCalculationModifiers?: string;
}

export function useInsertCharterPartyTerms() {
	const [insertCharterPartyTermsMutation] = useMutation<CharterPartyTermsResult>(INSERT_CHARTER_PARTY_TERMS);

	const insertCharterPartyTerms = async (
		input: CharterPartyTermsInsertInput
	): Promise<CharterPartyTermsItem | null> => {
		const { data, errors } = await insertCharterPartyTermsMutation({
			variables: { values: input }
		});

		if (errors) {
			throw new Error(errors[0].message);
		}

		if (!data?.insertIntoCharterPartyTermsSingle) {
			throw new Error('Failed to create charter party terms');
		}

		return data.insertIntoCharterPartyTermsSingle;
	};

	return { insertCharterPartyTerms };
}
