import { useInsertComment } from '../comment';
import { useMutateLaytime } from './use-mutate-laytime';
import { CommentStatusEnum, LaytimeStatusEnum } from '@/graphql';

export const useUpdateLaytimeStatus = () => {
	const { mutateLaytime } = useMutateLaytime();
	const { insertComment } = useInsertComment();

	const updateLaytimeStatus = async (
		laytimeId: string,
		status: LaytimeStatusEnum,
		message: string,
		userId: string
	) => {
		await mutateLaytime(laytimeId, { status });
		await insertComment({
			laytimeId,
			status: status as unknown as CommentStatusEnum,
			message,
			userId
		});
	};
	return { updateLaytimeStatus };
};
