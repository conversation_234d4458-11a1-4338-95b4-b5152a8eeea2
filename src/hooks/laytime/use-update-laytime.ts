import { toast } from 'sonner';
import { useMutateCargo } from '../cargo';
import { useMutateSof } from '../sof/use-mutate-sof';
import { useMutateCharterPartyTerms } from '../charterPartyTerms';
import { useMutateEvents, useInsertEvent } from '../event';
import { useInsertExclusion, useMutateExclusion } from '../exclusion';
import { useDeleteEvent } from '../event/use-delete-event';
import { useDeleteExclusion } from '../exclusion/use-delete-exclusion';
import { LaytimeData } from './types';
import { useMutateLaytime } from './use-mutate-laytime';
import { LaytimeOperationEnum, LaytimeStatusEnum } from '@/graphql';

export const useUpdateLaytime = () => {
	const { mutateLaytime, loading } = useMutateLaytime();
	const { mutateSof } = useMutateSof();
	const { mutateCargo } = useMutateCargo();
	const { mutateCharterPartyTerms } = useMutateCharterPartyTerms();
	const { mutateEvent } = useMutateEvents();
	const { insertEvent } = useInsertEvent();
	const { insertExclusion } = useInsertExclusion();
	const { mutateExclusion } = useMutateExclusion();
	const { removeEvent } = useDeleteEvent();
	const { removeExclusion } = useDeleteExclusion();
	const updateLaytime = async (data: LaytimeData) => {
		try {
			await mutateLaytime(data.id, {
				status: data.general.status as LaytimeStatusEnum,
				operation: data.general.operation as LaytimeOperationEnum,
				amount: JSON.stringify({
					value: data.summary.amountDue,
					type: data.summary.laytimeType,
					currency: data.summary.rateCurrency
				}),
				createdAt: data.general.cpDate,
				laycanFrom: data.general.laycanFrom,
				laycanTo: data.general.laycanTo,
				laytimeAllowed: data.summary.timeAllowed ? parseInt(data.summary.timeAllowed) : undefined,
				laytimeUsed: data.summary.timeUsed ? parseInt(data.summary.timeUsed) : undefined
			});
			await mutateSof(data.sofId, {
				chartererId: data.general.party.id,
				portId: data.general.port.id
			});
			await mutateCargo(data.cargoId, {
				quantity: parseFloat(data.general.cargo.quantity),
				unit: data.general.cargo.unit,
				type: data.general.cargo.type
			});
			await mutateCharterPartyTerms(data.charterPartyTermsId, {
				loadingRate: Number(data.summary.rateQty.replace(/,/g, '')),
				demurrageRate: Number(data.summary.rateAmount.replace(/,/g, ''))
			});
			await Promise.all(
				data.calculation.map(event =>
					event.id.length === 0
						? insertEvent({
								sofId: data.sofId,
								timestamp: event.date,
								description: event.remarks,
								type: event.event
							})
						: mutateEvent(event.id, {
								timestamp: event.date,
								description: event.remarks,
								type: event.event
							})
				)
			);

			await Promise.all(
				data.exclusions.map(exclusion =>
					exclusion.id.length === 0
						? insertExclusion({
								dateFrom: exclusion.dateFrom,
								dateTo: exclusion.dateTo,
								duration: Number(exclusion.duration),
								event: exclusion.event,
								percentage: exclusion.percentage,
								sofId: data.sofId,
								timeUsed: Number(exclusion.timeUsed)
							})
						: mutateExclusion(exclusion.id, {
								dateFrom: exclusion.dateFrom,
								dateTo: exclusion.dateTo,
								duration: Number(exclusion.duration),
								event: exclusion.event,
								percentage: exclusion.percentage,
								sofId: data.sofId,
								timeUsed: Number(exclusion.timeUsed)
							})
				)
			);
		} catch (error) {
			toast('Error saving changes', {
				description: 'Please try again later',
				duration: 5000
			});
			console.error('Error saving laytime as draft:', error);
		} finally {
			toast('Changes saved as draft', {
				duration: 3000
			});
		}
	};

	const deleteCalculations = async (ids: string[]) => {
		await Promise.all(ids.map(id => removeEvent(id)));
	};

	const deleteExclusions = async (ids: string[]) => {
		await Promise.all(ids.map(id => removeExclusion(id)));
	};

	return { updateLaytime, loading, deleteCalculations, deleteExclusions };
};
