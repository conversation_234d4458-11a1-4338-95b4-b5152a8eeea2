import { Charterer } from '@/hooks/charterer';
import { Port } from '@/hooks/port';
export interface Cargo {
	quantity: string;
	unit: string;
	type: string;
}

export interface General {
	id: string;
	status: string;
	port: Port;
	operation: string;
	cargo: Cargo;
	party: Charterer;
	cpDate: string;
	laycanFrom: string;
	laycanTo: string;
	vesselName: string;
}

export interface Summary {
	laytimeType: string;
	rateQty: string;
	rateSuf: string;
	rateAmount: string;
	rateCurrency: string;
	timeAllowed: string;
	timeUsed: string;
	varianceTime: string;
	amountDue: string;
}

export interface Calculation {
	id: string;
	event: string;
	date: string;
	remarks: string;
	laytimeStarts: boolean;
	demurrageStarts: boolean;
}

export interface Exclusion {
	id: string;
	dateFrom: string;
	dateTo: string;
	event: string;
	duration: number;
	percentage: string;
	timeUsed: number;
}

export interface Document {
	name: string;
	type: string;
}

export interface Activity {
	operator: Operator;
	status: string;
	comment: string;
	date: string;
}

export interface Operator {
	id: string;
	firstName: string;
	lastName: string;
	avatar: string;
}

export interface LaytimeData {
	id: string;
	sofId: string;
	cargoId: string;
	chartererId: string;
	portId: string;
	charterPartyTermsId: string;
	general: General;
	summary: Summary;
	calculation: Calculation[];
	exclusions: Exclusion[];
	documents: Document[];
	activity: Activity[];
}

export type Amount = {
	value: number;
	type: string;
	currency: string;
};

export type Laytime = {
	id: string;
	uid: string;
	vessel: string;
	port: Port;
	charterer: Charterer;
	amount: Amount;
	status: string;
	comments: number;
	date: string;
	operator: Operator;
};
