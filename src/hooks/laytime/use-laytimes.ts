import { gql, useQuery } from '@apollo/client';
import { format } from 'date-fns';
import { Laytime } from './types';
import { LaytimeFilters, LaytimeSelectItem } from '@/graphql';

export const GET_ALL_LAYTIMES = gql(`
	query getLaytimes($statusFilter: LaytimeFilters!) {
		laytime(where: $statusFilter) {
			comments {
				id
			}
			operator {
				firstName
				lastName
				avatar
			}
			sof {
				vessel {
					name
				}
				port {
					id
					name
					countryCode
				}
				charterer {
					id
					name
				}
			}
			id
			amount
			status
			createdAt
		}
	}
`);

interface GetAllLaytimesData {
	laytime: LaytimeSelectItem[];
}

const decodeLaytimeSelectItem = (laytimeSelectItem: LaytimeSelectItem): Laytime => ({
	id: `#VOY-${laytimeSelectItem.id.slice(-4).toUpperCase()}`,
	uid: laytimeSelectItem.id,
	comments: laytimeSelectItem.comments.length,
	operator: {
		id: laytimeSelectItem.operator?.id || '',
		avatar: laytimeSelectItem.operator?.avatar || '',
		firstName: laytimeSelectItem.operator?.firstName || '',
		lastName: laytimeSelectItem.operator?.lastName || ''
	},
	amount: {
		value: Math.random() * 10000,
		type: Math.random() > 0.5 ? 'demurrage' : 'despatch',
		currency: Math.random() > 0.5 ? 'USD' : 'EUR'
	},
	status: laytimeSelectItem.status,
	date: format(new Date(laytimeSelectItem.createdAt), 'LLL dd, y'),
	vessel: `mv ${laytimeSelectItem.sof?.vessel?.name}` || '',
	port: {
		id: laytimeSelectItem.sof?.port?.id || '',
		name: laytimeSelectItem.sof?.port?.name || '',
		countryCode: laytimeSelectItem.sof?.port?.countryCode || ''
	},
	charterer: {
		id: laytimeSelectItem.sof?.charterer?.id || '',
		name: laytimeSelectItem.sof?.charterer?.name || ''
	}
});

export const useLaytimes = (filter?: LaytimeFilters) => {
	const { data, ...props } = useQuery<GetAllLaytimesData>(GET_ALL_LAYTIMES, {
		fetchPolicy: 'cache-and-network',
		variables: { statusFilter: filter ? filter : {} }
	});

	const laytime = data?.laytime.map(decodeLaytimeSelectItem) || [];

	return {
		laytime,
		...props
	};
};
