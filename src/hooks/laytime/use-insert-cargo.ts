import { gql, useMutation } from '@apollo/client';

const INSERT_CARGO = gql`
	mutation InsertCargo($values: CargoInsertInput!) {
		insertIntoCargoSingle(values: $values) {
			id
			type
			quantity
			unit
		}
	}
`;

interface CargoItem {
	id: string;
	type: string;
	quantity: number;
	unit?: string;
}

interface CargoResult {
	insertIntoCargoSingle: CargoItem | null;
}

interface CargoInsertInput {
	id?: string;
	type: string;
	quantity: number;
	unit?: string;
}

export function useInsertCargo() {
	const [insertCargoMutation] = useMutation<CargoResult>(INSERT_CARGO);

	const insertCargo = async (input: CargoInsertInput): Promise<CargoItem | null> => {
		const { data, errors } = await insertCargoMutation({
			variables: { values: input }
		});

		if (errors) {
			throw new Error(errors[0].message);
		}

		if (!data?.insertIntoCargoSingle) {
			throw new Error('Failed to create cargo');
		}
		return data.insertIntoCargoSingle;
	};

	return { insertCargo };
}
