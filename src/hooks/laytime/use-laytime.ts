import { gql, useQuery } from '@apollo/client';
import { format } from 'date-fns';
import { Charterer } from '../charterer';
import { Port } from '../port';
import { Activity, Calculation, Exclusion } from './types';
import { LaytimeSelectItem } from '@/graphql';
import { formatAmount, formatTimeAsDHM } from '@/common/utils/formatUtils';

export const GET_SINGLE_LAYTIME = gql(`query getSingleLaytime($slug: String!) {
  laytimeSingle(where: {id: {eq: $slug}}) {
    id
    sof {
      id
      cargo {
        id
        quantity
        type
        unit
      }
      vessel {
        id
        name
      }
      charterPartyTerms {
        id
        loadingRate
        dischargingRate
        laytimeCalculationModifiers
        demurrageRate
      }
      charterer {
        id
        email
        name
      }
      exclusions {
        id
        dateFrom
        dateTo
        event
        duration
        percentage
        timeUsed
      }
      events {
        id
        timestamp
        description
        type
      }
      port {
        id
        name
        countryCode
      }
    }
    comments {
      id
      message
      status
      createdAt
      user {
        id
        firstName
        lastName
        avatar
      }
    }
    amount
    createdAt
    operation
    laytimeAllowed
    laytimeStarted
    laytimeUsed
    norAccepted
    norTendered
    status
    updatedAt
    laycanFrom
    laycanTo
  }
}`);

interface GetAllLaytimesData {
	laytimeSingle: LaytimeSelectItem;
}

export type Cargo = {
	quantity: string;
	unit: string;
	type: string;
};

export type General = {
	id: string;
	vesselName: string;
	status: string;
	port: Port;
	operation: string;
	cargo: Cargo;
	party: Charterer;
	cpDate: string;
	laycanFrom: string;
	laycanTo: string;
};

export type Summary = {
	laytimeType: string;
	rateQty: string;
	rateSuf: string;
	rateAmount: string;
	rateCurrency: string;
	timeAllowed: string;
	timeUsed: string;
	varianceTime: string;
	amountDue: string;
};

const emptyGeneral: General = {
	id: '',
	status: '',
	port: {
		id: '',
		name: '',
		countryCode: ''
	},
	operation: '',
	cargo: {
		quantity: '',
		unit: '',
		type: ''
	},
	party: {
		id: '',
		name: ''
	},
	cpDate: '',
	laycanFrom: '',
	laycanTo: '',
	vesselName: ''
};

const emptySummary: Summary = {
	laytimeType: '',
	rateQty: '',
	rateSuf: '',
	rateAmount: '',
	rateCurrency: '',
	timeAllowed: '',
	timeUsed: '',
	varianceTime: '',
	amountDue: ''
};

const getLaytimeGeneral = (laytimeSelectItem: LaytimeSelectItem): General => ({
	id: `#VOY-${laytimeSelectItem.id.slice(-4).toUpperCase()}`,
	vesselName: laytimeSelectItem.sof?.vessel?.name || '',
	status: laytimeSelectItem.status,
	cpDate: format(new Date(laytimeSelectItem.createdAt), 'd MMM yy'),
	port: {
		id: laytimeSelectItem.sof?.port?.id || '',
		name: laytimeSelectItem.sof?.port?.name || '',
		countryCode: laytimeSelectItem.sof?.port?.countryCode || ''
	},
	cargo: {
		quantity: Math.floor(Number(laytimeSelectItem.sof?.cargo?.quantity)).toLocaleString(),
		unit: laytimeSelectItem.sof?.cargo?.unit || '',
		type: laytimeSelectItem.sof?.cargo?.type || ''
	},
	party: {
		id: laytimeSelectItem.sof?.charterer?.id || '',
		name: laytimeSelectItem.sof?.charterer?.name || ''
	},
	operation: laytimeSelectItem.operation || '',
	laycanFrom: laytimeSelectItem.laycanFrom ? format(new Date(laytimeSelectItem.laycanFrom), 'd MMM yy') : '',
	laycanTo: laytimeSelectItem.laycanTo ? format(new Date(laytimeSelectItem.laycanTo), 'd MMM yy') : ''
});

const getLaytimeSummary = (laytimeSelectItem: LaytimeSelectItem): Summary => {
	const demurrageRate = laytimeSelectItem.sof?.charterPartyTerms?.demurrageRate ?? 0;
	const varianceTime = Math.abs((laytimeSelectItem.laytimeUsed ?? 0) - laytimeSelectItem.laytimeAllowed);
	const varianceHours = Math.floor(varianceTime / 60);

	const laytimeType = (JSON.parse(laytimeSelectItem?.amount ?? '{}') as { type: string }).type;

	return {
		laytimeType,
		rateQty: formatAmount(Math.floor(Number(laytimeSelectItem.sof?.charterPartyTerms?.loadingRate?.toString()))),
		rateSuf: 'mts/pdpr',
		rateAmount: formatAmount(Number(demurrageRate)),
		rateCurrency: 'USD',
		timeAllowed: formatTimeAsDHM(laytimeSelectItem.laytimeAllowed),
		timeUsed: formatTimeAsDHM(laytimeSelectItem.laytimeUsed ?? 0),
		varianceTime: formatTimeAsDHM(varianceTime),
		amountDue: formatAmount(Number((demurrageRate * varianceHours) / 10))
	};
};

const getLaytimeCalculation = (laytimeSelectItem: LaytimeSelectItem): Calculation[] => {
	const laytimeEvents = Array.from(laytimeSelectItem.sof?.events ?? []);
	laytimeEvents.sort((a, b) => new Date(a.timestamp ?? 0).getTime() - new Date(b.timestamp ?? 0).getTime());
	const calculation = laytimeEvents?.map(event => ({
		id: event.id ?? '',
		event: event.type ?? '',
		date: new Date(event.timestamp ?? 0).toISOString(),
		remarks: event.description ?? '',
		laytimeStarts: false,
		demurrageStarts: false
	}));
	return calculation || [];
};

const getLaytimeExclusions = (laytimeSelectItem: LaytimeSelectItem): Exclusion[] => {
	const laytimeExclusions = Array.from(laytimeSelectItem.sof?.exclusions ?? []);
	laytimeExclusions.sort((a, b) => new Date(a.dateFrom ?? 0).getTime() - new Date(b.dateFrom ?? 0).getTime());
	const exclusions = laytimeExclusions.map(exclusion => ({
		id: exclusion.id ?? '',
		dateFrom: exclusion.dateFrom ?? '',
		dateTo: exclusion.dateTo ?? '',
		event: exclusion.event ?? '',
		duration: exclusion.duration ?? '',
		percentage: exclusion.percentage ?? '',
		timeUsed: exclusion.timeUsed ?? ''
	}));
	return exclusions || [];
};

const getLaytimeActivity = (laytimeSelectItem: LaytimeSelectItem): Activity[] => {
	const comments = Array.from(laytimeSelectItem.comments ?? []);
	comments.sort((a, b) => new Date(a.createdAt ?? 0).getTime() - new Date(b.createdAt ?? 0).getTime());
	return comments.map(comment => ({
		operator: {
			id: comment.user?.id ?? '',
			firstName: comment.user?.firstName ?? '',
			lastName: comment.user?.lastName ?? '',
			avatar: comment.user?.avatar ?? ''
		},
		comment: comment.message ?? '',
		date: comment.createdAt,
		status: comment.status ?? ''
	}));
};
export const useLaytime = (slug: string) => {
	const { data, ...props } = useQuery<GetAllLaytimesData>(GET_SINGLE_LAYTIME, {
		fetchPolicy: 'network-only',
		variables: {
			slug
		}
	});

	const general = data?.laytimeSingle ? getLaytimeGeneral(data.laytimeSingle) : emptyGeneral;
	const summary = data?.laytimeSingle ? getLaytimeSummary(data.laytimeSingle) : emptySummary;
	const calculation = data?.laytimeSingle ? getLaytimeCalculation(data.laytimeSingle) : [];
	const exclusions = data?.laytimeSingle ? getLaytimeExclusions(data.laytimeSingle) : [];
	const activity = data?.laytimeSingle ? getLaytimeActivity(data.laytimeSingle) : [];

	return {
		laytime: {
			id: data?.laytimeSingle?.id || '',
			sofId: data?.laytimeSingle?.sof?.id || '',
			cargoId: data?.laytimeSingle?.sof?.cargo?.id || '',
			chartererId: data?.laytimeSingle?.sof?.charterer?.id || '',
			charterPartyTermsId: data?.laytimeSingle?.sof?.charterPartyTerms?.id || '',
			portId: data?.laytimeSingle?.sof?.port?.id || '',
			general,
			summary,
			calculation,
			exclusions,
			activity
		},
		...props
	};
};
