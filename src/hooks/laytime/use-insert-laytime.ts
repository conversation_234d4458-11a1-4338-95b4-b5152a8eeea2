import { gql, useMutation } from '@apollo/client';
import { LaytimeOperationEnum, LaytimeStatusEnum } from '../../app/types/laytime';

const INSERT_LAYTIME = gql`
	mutation InsertLaytime($values: LaytimeInsertInput!) {
		insertIntoLaytimeSingle(values: $values) {
			id
			operation
			status
			laytimeAllowed
			laytimeUsed
			amount
		}
	}
`;

interface LaytimeItem {
	id: string;
	operation: LaytimeOperationEnum;
	status: LaytimeStatusEnum;
	laytimeAllowed: number;
	laytimeUsed: number;
	amount: string;
}

interface LaytimeResult {
	insertIntoLaytimeSingle: LaytimeItem | null;
}

interface LaytimeInsertInput {
	operation: LaytimeOperationEnum;
	operatorId: string;
	sofId: string;
	status: LaytimeStatusEnum;
	laytimeAllowed: number;
	norTendered?: string;
	norAccepted?: string;
	laytimeStarted?: string;
	laytimeUsed?: number;
	amount?: string;
}

export function useInsertLaytime() {
	const [insertLaytimeMutation] = useMutation<LaytimeResult>(INSERT_LAYTIME);

	const insertLaytime = async (input: LaytimeInsertInput): Promise<LaytimeItem | null> => {
		try {
			const { data, errors } = await insertLaytimeMutation({
				variables: { values: input }
			});

			if (errors) {
				throw new Error(errors[0].message);
			}

			if (!data?.insertIntoLaytimeSingle) {
				throw new Error('Failed to create laytime');
			}

			return data.insertIntoLaytimeSingle;
		} catch (error) {
			console.error('Error in insertLaytime:', error);
			throw error;
		}
	};

	return { insertLaytime };
}
