import { gql, useMutation } from '@apollo/client';

const INSERT_PORT = gql`
	mutation InsertPort($values: PortInsertInput!) {
		insertIntoPortSingle(values: $values) {
			id
			name
			countryCode
		}
	}
`;

interface PortItem {
	id: string;
	name: string;
	countryCode: string;
}

interface PortResult {
	insertIntoPortSingle: PortItem | null;
}

interface PortInsertInput {
	id?: string;
	name: string;
	countryCode: string;
}

export function useInsertPort() {
	const [insertPortMutation] = useMutation<PortResult>(INSERT_PORT);

	const insertPort = async (input: PortInsertInput): Promise<PortItem | null> => {
		const { data, errors } = await insertPortMutation({
			variables: { values: input }
		});

		if (errors) {
			throw new Error(errors[0].message);
		}

		if (!data?.insertIntoPortSingle) {
			throw new Error('Failed to create port');
		}

		return data.insertIntoPortSingle;
	};

	return { insertPort };
}
