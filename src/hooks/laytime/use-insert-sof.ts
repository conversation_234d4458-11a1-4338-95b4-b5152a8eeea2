import { gql, useMutation } from '@apollo/client';

const INSERT_SOF = gql`
	mutation InsertSof($values: SofInsertInput!) {
		insertIntoSofSingle(values: $values) {
			id
			cargoId
			portId
			charterPartyTermsId
			vesselId
			chartererId
		}
	}
`;

interface SofItem {
	id: string;
	cargoId: string;
	portId: string;
	charterPartyTermsId: string;
	vesselId: string;
	chartererId: string;
}

interface SofResult {
	insertIntoSofSingle: SofItem | null;
}

interface SofInsertInput {
	id?: string;
	cargoId: string;
	portId: string;
	charterPartyTermsId: string;
	vesselId: string;
	chartererId: string;
}

export function useInsertSof() {
	const [insertSofMutation] = useMutation<SofResult>(INSERT_SOF);

	const insertSof = async (input: SofInsertInput): Promise<SofItem | null> => {
		const { data, errors } = await insertSofMutation({
			variables: { values: input }
		});

		if (errors) {
			throw new Error(errors[0].message);
		}

		if (!data?.insertIntoSofSingle) {
			throw new Error('Failed to create SOF');
		}

		return data.insertIntoSofSingle;
	};

	return { insertSof };
}
