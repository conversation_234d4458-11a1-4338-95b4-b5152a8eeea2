import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { LaytimeUpdateInput } from '@/graphql';

export const UPDATE_LAYTIME = gql`
	mutation UpdateLaytime($id: String!, $set: LaytimeUpdateInput!) {
		updateLaytime(where: { id: { eq: $id } }, set: $set) {
			id
		}
	}
`;

export interface LaytimeResult {
	data: {
		updateLaytime: LaytimeUpdateInput;
	};
}

export const useMutateLaytime = (options?: MutateOptions) => {
	const [updateLaytime, { loading, error }] = useMutation(UPDATE_LAYTIME, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleLaytime']
	});

	const mutateLaytime = async (id: string, data: Partial<LaytimeUpdateInput>) => {
		try {
			const result = (await updateLaytime({
				variables: {
					id,
					set: data
				}
			})) as LaytimeResult;
			return result.data?.updateLaytime;
		} catch (err) {
			console.error('Error updating laytime:', err);
			throw err;
		}
	};

	return {
		mutateLaytime,
		loading,
		error
	};
};
