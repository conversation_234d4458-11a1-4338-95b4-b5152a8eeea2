import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { InsertExclusionResult } from './types';
import { ExclusionInsertInput } from '@/graphql';

export const INSERT_EXCLUSION = gql(`mutation InsertIntoExclusion($values: [ExclusionInsertInput!]!) {
  insertIntoExclusion(values: $values) {
    id
  }
}`);

export const useInsertExclusion = (options?: MutateOptions) => {
	const [insert, { loading, error }] = useMutation(INSERT_EXCLUSION, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleSof']
	});

	const insertExclusion = async (data: ExclusionInsertInput) => {
		try {
			const result = (await insert({
				variables: {
					values: data
				}
			})) as InsertExclusionResult;
			return result.data?.insertIntoExclusion;
		} catch (err) {
			console.error('Error updating event:', err);
			throw err;
		}
	};

	return {
		insertExclusion,
		loading,
		error
	};
};
