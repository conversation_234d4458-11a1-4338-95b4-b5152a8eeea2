import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { DeleteExclusionResult } from './types';

export const DELETE_EXCLUSION = gql`
	mutation DeleteExclusion($id: String!) {
		deleteFromExclusion(where: { id: { eq: $id } }) {
			id
		}
	}
`;

export const useDeleteExclusion = (options?: MutateOptions) => {
	const [deleteExclusion, { loading, error }] = useMutation(DELETE_EXCLUSION, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleLaytime']
	});

	const removeExclusion = async (id: string) => {
		try {
			const result = (await deleteExclusion({
				variables: {
					id
				}
			})) as DeleteExclusionResult;
			return result.data?.deleteFromExclusion;
		} catch (err) {
			console.error('Error deleting exclusion:', err);
			throw err;
		}
	};

	return {
		removeExclusion,
		loading,
		error
	};
};
