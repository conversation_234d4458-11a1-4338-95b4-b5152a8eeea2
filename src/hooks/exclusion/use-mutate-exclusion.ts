import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { UpdateExclusionResult } from './types';
import { ExclusionUpdateInput } from '@/graphql';

export const UPDATE_EXCLUSION = gql`
	mutation UpdateExclusion($id: String!, $set: ExclusionUpdateInput!) {
		updateExclusion(where: { id: { eq: $id } }, set: $set) {
			id
		}
	}
`;

export const useMutateExclusion = (options?: MutateOptions) => {
	const [updateExclusion, { loading, error }] = useMutation(UPDATE_EXCLUSION, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleSof']
	});

	const mutateExclusion = async (id: string, data: Partial<ExclusionUpdateInput>) => {
		try {
			const result = (await updateExclusion({
				variables: {
					id,
					set: data
				}
			})) as UpdateExclusionResult;
			return result.data?.updateExclusion;
		} catch (err) {
			console.error('Error updating exclusion:', err);
			throw err;
		}
	};

	return {
		mutateExclusion,
		loading,
		error
	};
};
