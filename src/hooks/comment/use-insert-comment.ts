import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { CommentInsertInput, CommentItem } from '@/graphql';

export const INSERT_COMMENT = gql(`mutation InsertIntoComment($values: [CommentInsertInput!]!) {
  insertIntoComment(values: $values) {
    id
  }
}`);

interface CommentResult {
	data: {
		insertIntoComment: CommentItem;
	};
}

export const useInsertComment = (options?: MutateOptions) => {
	const [insert, { loading, error }] = useMutation(INSERT_COMMENT, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleSof']
	});

	const insertComment = async (data: Partial<CommentInsertInput>) => {
		try {
			const result = (await insert({
				variables: {
					values: data
				}
			})) as CommentResult;
			return result.data?.insertIntoComment;
		} catch (err) {
			console.error('Error updating event:', err);
			throw err;
		}
	};

	return {
		insertComment,
		loading,
		error
	};
};
