import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { CharterPartyTermsResult } from './types';
import { CharterPartyTermsUpdateInput } from '@/graphql';

export const UPDATE_CHARTER_PARTY_TERMS = gql`
	mutation UpdateCharterPartyTerms($id: String!, $set: CharterPartyTermsUpdateInput!) {
		updateCharterPartyTerms(where: { id: { eq: $id } }, set: $set) {
			id
		}
	}
`;

export const useMutateCharterPartyTerms = (options?: MutateOptions) => {
	const [updateCharterPartyTerms, { loading, error }] = useMutation(UPDATE_CHARTER_PARTY_TERMS, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleCharterPartyTerms']
	});

	const mutateCharterPartyTerms = async (id: string, data: Partial<CharterPartyTermsUpdateInput>) => {
		try {
			const result = (await updateCharterPartyTerms({
				variables: {
					id,
					set: data
				}
			})) as CharterPartyTermsResult;
			return result.data?.updateCharterPartyTerms;
		} catch (err) {
			console.error('Error updating charter party terms:', err);
			throw err;
		}
	};

	return {
		mutateCharterPartyTerms,
		loading,
		error
	};
};
