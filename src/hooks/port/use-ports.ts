import { useQuery } from '@tanstack/react-query';
import { useGraphqlApiProxy } from '@abraxa/graphql';
import { gql, GetPortsQuery, GetPortsQueryVariables } from '@/graphql';

export const GET_ALL_PORTS = gql(`query getPorts{
  port {
    id
    name
	countryCode
  }
}`);

export const usePorts = () => {
	const proxy = useGraphqlApiProxy();
	const { data, isLoading, ...props } = useQuery({
		queryKey: ['ports'],
		queryFn: () => proxy<GetPortsQuery, GetPortsQueryVariables>(GET_ALL_PORTS)
	});

	const ports = data?.port || [];

	return {
		ports,
		loading: isLoading,
		...props
	};
};
