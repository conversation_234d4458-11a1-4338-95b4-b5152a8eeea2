import { gql, useMutation } from '@apollo/client';
import { MutateOptions } from '../types';
import { CargoResult } from './types';
import { CargoUpdateInput } from '@/graphql';

export const UPDATE_CARGO = gql`
	mutation UpdateCargo($id: String!, $set: CargoUpdateInput!) {
		updateCargo(where: { id: { eq: $id } }, set: $set) {
			id
		}
	}
`;

export const useMutateCargo = (options?: MutateOptions) => {
	const [updateCargo, { loading, error }] = useMutation(UPDATE_CARGO, {
		onCompleted: () => {
			options?.onSuccess?.();
		},
		onError: error => {
			options?.onError?.(error);
		},
		refetchQueries: ['getSingleCargo']
	});

	const mutateCargo = async (id: string, data: Partial<CargoUpdateInput>) => {
		try {
			const result = (await updateCargo({
				variables: {
					id,
					set: data
				}
			})) as CargoResult;
			return result.data?.updateCargo;
		} catch (err) {
			console.error('Error updating cargo:', err);
			throw err;
		}
	};

	return {
		mutateCargo,
		loading,
		error
	};
};
