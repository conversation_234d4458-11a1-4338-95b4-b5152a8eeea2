export interface LaytimeListData {
	documentId: string;
	voyage_id: string;
	port_function: string;
	laytime_status: string;
	updatedAt: string;
	vessel: {
		name: string;
	};
	port: {
		name: string;
		country_code: string;
	};
	company: {
		name: string;
	};
	info: {
		amount: number;
		currency: string;
		laytime_type: string;
	};
	users_permissions_user: {
		firstName: string;
		lastName: string;
	};
}

export interface LaytimeDisplayData {
	documentId: string;
	voyage_id: string;
	laytime_status: string;
	port_function: string;
	vessel: {
		name: string;
	};
	port: {
		name: string;
		country_code: string;
	};
	company: {
		name: string;
	};
	info: {
		cargo: {
			documentId: string;
			name: string;
		};
		amount: number;
		cargo_unit: string;
		cargo_qty: number;
		cp_date: string;
		currency: string;
		laycan_end: string;
		laycan_start: string;
		laytime_type: string;
		rate_amount: number;
		rate_qty: number;
		time_allowed_hrs: number;
		time_used_hrs: number;
		total_hrs: number;
	};
	calculation: {
		event: string;
		date_time: string;
		demurrage_starts: boolean;
		laytime_starts: boolean;
		remarks: string;
	}[];
	exclusions: {
		date_time_from: string;
		date_time_to: string;
		duration_hrs: number;
		event: string;
		percent: number;
		time_used_hrs: number;
	}[];
	documents: {
		documentId: string;
		name: string;
		url: string;
		ext: string;
	}[];
	logs: {
		documentId: string;
		laytime_status: string;
		date: string;
		comment: string;
		users_permissions_user: {
			documentId: string;
			firstName: string;
			lastName: string;
		};
	}[];
}
