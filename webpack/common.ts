import path from 'path';

import { EnvironmentPlugin } from 'webpack';
import { ModuleFederationPlugin, Configuration } from '@abraxa/tools-webpack';

import packageMeta from '../package.json';

export const WEBPACK_COMMON_CONFIG: Configuration = {
	entry: 'index.ts',
	html: {
		template: path.resolve('public', 'index.html')
	},
	resolve: {
		alias: {
			'@/graphql': path.resolve(__dirname, '../generated'),
			'@/admin/*': path.resolve(__dirname, '../src/app/admin/*'),
			'@/static/*': path.resolve(__dirname, '../public/static/*'),
			'@/*': path.resolve(__dirname, '../src/*')
		}
	},
	module: {
		rules: [
			{
				test: /\.css$/i,
				use: ['style-loader', 'css-loader', 'postcss-loader']
			},
			{
				test: /\.(png|jpg|jpeg|gif|webp)$/i,
				type: 'asset/resource',
				generator: {
					filename: path.join('static', 'media', '[name].[contenthash:8][ext]')
				}
			}
		]
	},
	plugins: [
		new EnvironmentPlugin({
			BASE_PATH: process.env.BASE_PATH ?? '/',
			//TODO: leaks api key inside the browser
			OPENAI_API_KEY: process.env.OPENAI_API_KEY,
			NEXT_PUBLIC_AUTH0_DOMAIN: process.env.NEXT_PUBLIC_AUTH0_DOMAIN,
			NEXT_PUBLIC_AUTH0_CLIENT_ID: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID,
			NEXT_PUBLIC_AUTH0_AUDIENCE: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,
			NEXT_PUBLIC_CORE_API_BASE_PATH: process.env.NEXT_PUBLIC_CORE_API_BASE_PATH,
			NEXT_PUBLIC_MAPBOX_TOKEN: process.env.NEXT_PUBLIC_MAPBOX_TOKEN,
			NEXT_PUBLIC_GRAPHQL_ADDRESS: process.env.NEXT_PUBLIC_GRAPHQL_ADDRESS
		}),
		new ModuleFederationPlugin({
			name: packageMeta.name.replace(/^@abraxa\//, '').replace('-', '_'),
			filename: path.join('static', 'js', '[name].[contenthash:8].js'),
			dts: process.env.NODE_ENV === 'development',
			exposes: {
				'./home': path.join(__dirname, '..', 'remotes', 'home.tsx'),
				'./inbox': path.join(__dirname, '..', 'remotes', 'inbox.tsx'),
				'./ports': path.join(__dirname, '..', 'remotes', 'ports.tsx'),
				'./agents': path.join(__dirname, '..', 'remotes', 'agents.tsx'),
				'./voyages': path.join(__dirname, '..', 'remotes', 'voyages.tsx'),
				'./port-calls': path.join(__dirname, '..', 'remotes', 'port-calls.tsx'),
				'./laytime': path.join(__dirname, '..', 'remotes', 'laytime.tsx'),
				'./ai': path.join(__dirname, '..', 'remotes', 'ai.tsx'),
				'./enquiries': path.join(__dirname, '..', 'remotes', 'enquiries.tsx'),
				'./disbursements': path.join(__dirname, '..', 'remotes', 'disbursements.tsx'),
				'./cashflow': path.join(__dirname, '..', 'remotes', 'cashflow.tsx'),
				'./appointments': path.join(__dirname, '..', 'remotes', 'appointments.tsx')
			},
			shared: {
				react: {
					singleton: true,
					requiredVersion: packageMeta.dependencies.react
				},
				'react-dom': {
					singleton: true,
					requiredVersion: packageMeta.dependencies['react-dom']
				},
				'react-router': {
					singleton: true,
					requiredVersion: packageMeta.dependencies['react-router']
				},
				'@tanstack/react-query': {
					singleton: true,
					requiredVersion: packageMeta.dependencies['@tanstack/react-query']
				},
				'@abraxa/graphql': {
					singleton: true,
					requiredVersion: packageMeta.dependencies['@abraxa/graphql']
				},
				'@abraxa/authentication': {
					singleton: true,
					requiredVersion: packageMeta.dependencies['@abraxa/authentication']
				}
			}
		})
	]
};
