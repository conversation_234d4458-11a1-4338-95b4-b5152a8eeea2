import path from 'path';

import { getWebpackProdConfig, merge } from '@abraxa/tools-webpack';

import { WEBPACK_COMMON_CONFIG } from './common';

export const WEBPACK_PROD_CONFIG = getWebpackProdConfig(
	merge(WEBPACK_COMMON_CONFIG, {
		output: {
			path: path.resolve('build', 'public'),
			filename: path.join('static', 'js', '[name].[contenthash:8].js'),
			publicPath: process.env.PUBLIC_PATH ?? '/'
		}
	})
);
