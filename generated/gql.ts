/* eslint-disable */
import * as types from './graphql';



/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n\tquery getAgents {\n\t\tagent {\n\t\t\tid\n\t\t\tverified\n\t\t\tcompany\n\t\t\tcountry\n\t\t\tphone\n\t\t\temail\n\t\t}\n\t}\n": typeof types.GetAgentsDocument,
    "query getAppointments{\n  appointment {\n    agentName\n    eta\n    fileId\n    operatorName\n    portFunction\n    portName\n    portCountryCode\n    status\n    vesselImo\n    vesselName\n    requestDate\n  }\n}": typeof types.GetAppointmentsDocument,
    "\n\tmutation UpdateCargo($id: String!, $set: CargoUpdateInput!) {\n\t\tupdateCargo(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.UpdateCargoDocument,
    "\n\tmutation UpdateCharterPartyTerms($id: String!, $set: CharterPartyTermsUpdateInput!) {\n\t\tupdateCharterPartyTerms(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.UpdateCharterPartyTermsDocument,
    "query getCharterers{\n  charterer {\n    id\n    name\n  }\n}": typeof types.GetCharterersDocument,
    "mutation InsertIntoComment($values: [CommentInsertInput!]!) {\n  insertIntoComment(values: $values) {\n    id\n  }\n}": typeof types.InsertIntoCommentDocument,
    "query getDisbursements{\n  disbursement {\n    agentId\n    agentName\n    amount\n    cta\n    da\n    date\n    groupId\n    id\n    portId\n    portName\n    portCountryCode\n    status\n    vesselId\n    vesselImo\n    vesselName\n  }\n}": typeof types.GetDisbursementsDocument,
    "\n\tmutation DeleteEvent($id: String!) {\n\t\tdeleteFromEvent(where: { id: { eq: $id } }) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.DeleteEventDocument,
    "mutation InsertIntoEvent($values: [EventInsertInput!]!) {\n  insertIntoEvent(values: $values) {\n    id\n  }\n}": typeof types.InsertIntoEventDocument,
    "\n\tmutation UpdateEvent($id: String!, $set: EventUpdateInput!) {\n\t\tupdateEvent(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.UpdateEventDocument,
    "\n\tmutation DeleteExclusion($id: String!) {\n\t\tdeleteFromExclusion(where: { id: { eq: $id } }) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.DeleteExclusionDocument,
    "mutation InsertIntoExclusion($values: [ExclusionInsertInput!]!) {\n  insertIntoExclusion(values: $values) {\n    id\n  }\n}": typeof types.InsertIntoExclusionDocument,
    "\n\tmutation UpdateExclusion($id: String!, $set: ExclusionUpdateInput!) {\n\t\tupdateExclusion(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.UpdateExclusionDocument,
    "\n\tmutation InsertCargo($values: CargoInsertInput!) {\n\t\tinsertIntoCargoSingle(values: $values) {\n\t\t\tid\n\t\t\ttype\n\t\t\tquantity\n\t\t\tunit\n\t\t}\n\t}\n": typeof types.InsertCargoDocument,
    "\n\tmutation InsertCharterPartyTerms($values: CharterPartyTermsInsertInput!) {\n\t\tinsertIntoCharterPartyTermsSingle(values: $values) {\n\t\t\tid\n\t\t\tloadingRate\n\t\t\tdemurrageRate\n\t\t\tdischargingRate\n\t\t\tlaytimeAllowed\n\t\t\tlaytimeCalculationModifiers\n\t\t}\n\t}\n": typeof types.InsertCharterPartyTermsDocument,
    "\n\tmutation InsertLaytime($values: LaytimeInsertInput!) {\n\t\tinsertIntoLaytimeSingle(values: $values) {\n\t\t\tid\n\t\t\toperation\n\t\t\tstatus\n\t\t\tlaytimeAllowed\n\t\t\tlaytimeUsed\n\t\t\tamount\n\t\t}\n\t}\n": typeof types.InsertLaytimeDocument,
    "\n\tmutation InsertPort($values: PortInsertInput!) {\n\t\tinsertIntoPortSingle(values: $values) {\n\t\t\tid\n\t\t\tname\n\t\t\tcountryCode\n\t\t}\n\t}\n": typeof types.InsertPortDocument,
    "\n\tmutation InsertSof($values: SofInsertInput!) {\n\t\tinsertIntoSofSingle(values: $values) {\n\t\t\tid\n\t\t\tcargoId\n\t\t\tportId\n\t\t\tcharterPartyTermsId\n\t\t\tvesselId\n\t\t\tchartererId\n\t\t}\n\t}\n": typeof types.InsertSofDocument,
    "query getSingleLaytime($slug: String!) {\n  laytimeSingle(where: {id: {eq: $slug}}) {\n    id\n    sof {\n      id\n      cargo {\n        id\n        quantity\n        type\n        unit\n      }\n      vessel {\n        id\n        name\n      }\n      charterPartyTerms {\n        id\n        loadingRate\n        dischargingRate\n        laytimeCalculationModifiers\n        demurrageRate\n      }\n      charterer {\n        id\n        email\n        name\n      }\n      exclusions {\n        id\n        dateFrom\n        dateTo\n        event\n        duration\n        percentage\n        timeUsed\n      }\n      events {\n        id\n        timestamp\n        description\n        type\n      }\n      port {\n        id\n        name\n        countryCode\n      }\n    }\n    comments {\n      id\n      message\n      status\n      createdAt\n      user {\n        id\n        firstName\n        lastName\n        avatar\n      }\n    }\n    amount\n    createdAt\n    operation\n    laytimeAllowed\n    laytimeStarted\n    laytimeUsed\n    norAccepted\n    norTendered\n    status\n    updatedAt\n    laycanFrom\n    laycanTo\n  }\n}": typeof types.GetSingleLaytimeDocument,
    "\n\tquery getLaytimes($statusFilter: LaytimeFilters!) {\n\t\tlaytime(where: $statusFilter) {\n\t\t\tcomments {\n\t\t\t\tid\n\t\t\t}\n\t\t\toperator {\n\t\t\t\tfirstName\n\t\t\t\tlastName\n\t\t\t\tavatar\n\t\t\t}\n\t\t\tsof {\n\t\t\t\tvessel {\n\t\t\t\t\tname\n\t\t\t\t}\n\t\t\t\tport {\n\t\t\t\t\tid\n\t\t\t\t\tname\n\t\t\t\t\tcountryCode\n\t\t\t\t}\n\t\t\t\tcharterer {\n\t\t\t\t\tid\n\t\t\t\t\tname\n\t\t\t\t}\n\t\t\t}\n\t\t\tid\n\t\t\tamount\n\t\t\tstatus\n\t\t\tcreatedAt\n\t\t}\n\t}\n": typeof types.GetLaytimesDocument,
    "\n\tmutation UpdateLaytime($id: String!, $set: LaytimeUpdateInput!) {\n\t\tupdateLaytime(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.UpdateLaytimeDocument,
    "query getPorts{\n  port {\n    id\n    name\n\tcountryCode\n  }\n}": typeof types.GetPortsDocument,
    "query getPortCalls{\n  portCall {\n    agentName\n    eta\n    etd\n    fileId\n    operatorName\n    portFunction\n    portName\n    portCountryCode\n    status\n    vesselImo\n    vesselName\n  }\n}": typeof types.GetPortCallsDocument,
    "\n\tmutation Signed($input: SignedUrlInput!) {\n\t\tgetSignedUrl(input: $input) {\n\t\t\tsignedUrl\n\t\t}\n\t}\n": typeof types.SignedDocument,
    "\n\tmutation UpdateSof($id: String!, $set: SofUpdateInput!) {\n\t\tupdateSof(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": typeof types.UpdateSofDocument,
};
const documents: Documents = {
    "\n\tquery getAgents {\n\t\tagent {\n\t\t\tid\n\t\t\tverified\n\t\t\tcompany\n\t\t\tcountry\n\t\t\tphone\n\t\t\temail\n\t\t}\n\t}\n": types.GetAgentsDocument,
    "query getAppointments{\n  appointment {\n    agentName\n    eta\n    fileId\n    operatorName\n    portFunction\n    portName\n    portCountryCode\n    status\n    vesselImo\n    vesselName\n    requestDate\n  }\n}": types.GetAppointmentsDocument,
    "\n\tmutation UpdateCargo($id: String!, $set: CargoUpdateInput!) {\n\t\tupdateCargo(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": types.UpdateCargoDocument,
    "\n\tmutation UpdateCharterPartyTerms($id: String!, $set: CharterPartyTermsUpdateInput!) {\n\t\tupdateCharterPartyTerms(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": types.UpdateCharterPartyTermsDocument,
    "query getCharterers{\n  charterer {\n    id\n    name\n  }\n}": types.GetCharterersDocument,
    "mutation InsertIntoComment($values: [CommentInsertInput!]!) {\n  insertIntoComment(values: $values) {\n    id\n  }\n}": types.InsertIntoCommentDocument,
    "query getDisbursements{\n  disbursement {\n    agentId\n    agentName\n    amount\n    cta\n    da\n    date\n    groupId\n    id\n    portId\n    portName\n    portCountryCode\n    status\n    vesselId\n    vesselImo\n    vesselName\n  }\n}": types.GetDisbursementsDocument,
    "\n\tmutation DeleteEvent($id: String!) {\n\t\tdeleteFromEvent(where: { id: { eq: $id } }) {\n\t\t\tid\n\t\t}\n\t}\n": types.DeleteEventDocument,
    "mutation InsertIntoEvent($values: [EventInsertInput!]!) {\n  insertIntoEvent(values: $values) {\n    id\n  }\n}": types.InsertIntoEventDocument,
    "\n\tmutation UpdateEvent($id: String!, $set: EventUpdateInput!) {\n\t\tupdateEvent(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": types.UpdateEventDocument,
    "\n\tmutation DeleteExclusion($id: String!) {\n\t\tdeleteFromExclusion(where: { id: { eq: $id } }) {\n\t\t\tid\n\t\t}\n\t}\n": types.DeleteExclusionDocument,
    "mutation InsertIntoExclusion($values: [ExclusionInsertInput!]!) {\n  insertIntoExclusion(values: $values) {\n    id\n  }\n}": types.InsertIntoExclusionDocument,
    "\n\tmutation UpdateExclusion($id: String!, $set: ExclusionUpdateInput!) {\n\t\tupdateExclusion(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": types.UpdateExclusionDocument,
    "\n\tmutation InsertCargo($values: CargoInsertInput!) {\n\t\tinsertIntoCargoSingle(values: $values) {\n\t\t\tid\n\t\t\ttype\n\t\t\tquantity\n\t\t\tunit\n\t\t}\n\t}\n": types.InsertCargoDocument,
    "\n\tmutation InsertCharterPartyTerms($values: CharterPartyTermsInsertInput!) {\n\t\tinsertIntoCharterPartyTermsSingle(values: $values) {\n\t\t\tid\n\t\t\tloadingRate\n\t\t\tdemurrageRate\n\t\t\tdischargingRate\n\t\t\tlaytimeAllowed\n\t\t\tlaytimeCalculationModifiers\n\t\t}\n\t}\n": types.InsertCharterPartyTermsDocument,
    "\n\tmutation InsertLaytime($values: LaytimeInsertInput!) {\n\t\tinsertIntoLaytimeSingle(values: $values) {\n\t\t\tid\n\t\t\toperation\n\t\t\tstatus\n\t\t\tlaytimeAllowed\n\t\t\tlaytimeUsed\n\t\t\tamount\n\t\t}\n\t}\n": types.InsertLaytimeDocument,
    "\n\tmutation InsertPort($values: PortInsertInput!) {\n\t\tinsertIntoPortSingle(values: $values) {\n\t\t\tid\n\t\t\tname\n\t\t\tcountryCode\n\t\t}\n\t}\n": types.InsertPortDocument,
    "\n\tmutation InsertSof($values: SofInsertInput!) {\n\t\tinsertIntoSofSingle(values: $values) {\n\t\t\tid\n\t\t\tcargoId\n\t\t\tportId\n\t\t\tcharterPartyTermsId\n\t\t\tvesselId\n\t\t\tchartererId\n\t\t}\n\t}\n": types.InsertSofDocument,
    "query getSingleLaytime($slug: String!) {\n  laytimeSingle(where: {id: {eq: $slug}}) {\n    id\n    sof {\n      id\n      cargo {\n        id\n        quantity\n        type\n        unit\n      }\n      vessel {\n        id\n        name\n      }\n      charterPartyTerms {\n        id\n        loadingRate\n        dischargingRate\n        laytimeCalculationModifiers\n        demurrageRate\n      }\n      charterer {\n        id\n        email\n        name\n      }\n      exclusions {\n        id\n        dateFrom\n        dateTo\n        event\n        duration\n        percentage\n        timeUsed\n      }\n      events {\n        id\n        timestamp\n        description\n        type\n      }\n      port {\n        id\n        name\n        countryCode\n      }\n    }\n    comments {\n      id\n      message\n      status\n      createdAt\n      user {\n        id\n        firstName\n        lastName\n        avatar\n      }\n    }\n    amount\n    createdAt\n    operation\n    laytimeAllowed\n    laytimeStarted\n    laytimeUsed\n    norAccepted\n    norTendered\n    status\n    updatedAt\n    laycanFrom\n    laycanTo\n  }\n}": types.GetSingleLaytimeDocument,
    "\n\tquery getLaytimes($statusFilter: LaytimeFilters!) {\n\t\tlaytime(where: $statusFilter) {\n\t\t\tcomments {\n\t\t\t\tid\n\t\t\t}\n\t\t\toperator {\n\t\t\t\tfirstName\n\t\t\t\tlastName\n\t\t\t\tavatar\n\t\t\t}\n\t\t\tsof {\n\t\t\t\tvessel {\n\t\t\t\t\tname\n\t\t\t\t}\n\t\t\t\tport {\n\t\t\t\t\tid\n\t\t\t\t\tname\n\t\t\t\t\tcountryCode\n\t\t\t\t}\n\t\t\t\tcharterer {\n\t\t\t\t\tid\n\t\t\t\t\tname\n\t\t\t\t}\n\t\t\t}\n\t\t\tid\n\t\t\tamount\n\t\t\tstatus\n\t\t\tcreatedAt\n\t\t}\n\t}\n": types.GetLaytimesDocument,
    "\n\tmutation UpdateLaytime($id: String!, $set: LaytimeUpdateInput!) {\n\t\tupdateLaytime(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": types.UpdateLaytimeDocument,
    "query getPorts{\n  port {\n    id\n    name\n\tcountryCode\n  }\n}": types.GetPortsDocument,
    "query getPortCalls{\n  portCall {\n    agentName\n    eta\n    etd\n    fileId\n    operatorName\n    portFunction\n    portName\n    portCountryCode\n    status\n    vesselImo\n    vesselName\n  }\n}": types.GetPortCallsDocument,
    "\n\tmutation Signed($input: SignedUrlInput!) {\n\t\tgetSignedUrl(input: $input) {\n\t\t\tsignedUrl\n\t\t}\n\t}\n": types.SignedDocument,
    "\n\tmutation UpdateSof($id: String!, $set: SofUpdateInput!) {\n\t\tupdateSof(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n": types.UpdateSofDocument,
};

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tquery getAgents {\n\t\tagent {\n\t\t\tid\n\t\t\tverified\n\t\t\tcompany\n\t\t\tcountry\n\t\t\tphone\n\t\t\temail\n\t\t}\n\t}\n"): typeof import('./graphql').GetAgentsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "query getAppointments{\n  appointment {\n    agentName\n    eta\n    fileId\n    operatorName\n    portFunction\n    portName\n    portCountryCode\n    status\n    vesselImo\n    vesselName\n    requestDate\n  }\n}"): typeof import('./graphql').GetAppointmentsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation UpdateCargo($id: String!, $set: CargoUpdateInput!) {\n\t\tupdateCargo(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').UpdateCargoDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation UpdateCharterPartyTerms($id: String!, $set: CharterPartyTermsUpdateInput!) {\n\t\tupdateCharterPartyTerms(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').UpdateCharterPartyTermsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "query getCharterers{\n  charterer {\n    id\n    name\n  }\n}"): typeof import('./graphql').GetCharterersDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "mutation InsertIntoComment($values: [CommentInsertInput!]!) {\n  insertIntoComment(values: $values) {\n    id\n  }\n}"): typeof import('./graphql').InsertIntoCommentDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "query getDisbursements{\n  disbursement {\n    agentId\n    agentName\n    amount\n    cta\n    da\n    date\n    groupId\n    id\n    portId\n    portName\n    portCountryCode\n    status\n    vesselId\n    vesselImo\n    vesselName\n  }\n}"): typeof import('./graphql').GetDisbursementsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation DeleteEvent($id: String!) {\n\t\tdeleteFromEvent(where: { id: { eq: $id } }) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').DeleteEventDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "mutation InsertIntoEvent($values: [EventInsertInput!]!) {\n  insertIntoEvent(values: $values) {\n    id\n  }\n}"): typeof import('./graphql').InsertIntoEventDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation UpdateEvent($id: String!, $set: EventUpdateInput!) {\n\t\tupdateEvent(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').UpdateEventDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation DeleteExclusion($id: String!) {\n\t\tdeleteFromExclusion(where: { id: { eq: $id } }) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').DeleteExclusionDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "mutation InsertIntoExclusion($values: [ExclusionInsertInput!]!) {\n  insertIntoExclusion(values: $values) {\n    id\n  }\n}"): typeof import('./graphql').InsertIntoExclusionDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation UpdateExclusion($id: String!, $set: ExclusionUpdateInput!) {\n\t\tupdateExclusion(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').UpdateExclusionDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation InsertCargo($values: CargoInsertInput!) {\n\t\tinsertIntoCargoSingle(values: $values) {\n\t\t\tid\n\t\t\ttype\n\t\t\tquantity\n\t\t\tunit\n\t\t}\n\t}\n"): typeof import('./graphql').InsertCargoDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation InsertCharterPartyTerms($values: CharterPartyTermsInsertInput!) {\n\t\tinsertIntoCharterPartyTermsSingle(values: $values) {\n\t\t\tid\n\t\t\tloadingRate\n\t\t\tdemurrageRate\n\t\t\tdischargingRate\n\t\t\tlaytimeAllowed\n\t\t\tlaytimeCalculationModifiers\n\t\t}\n\t}\n"): typeof import('./graphql').InsertCharterPartyTermsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation InsertLaytime($values: LaytimeInsertInput!) {\n\t\tinsertIntoLaytimeSingle(values: $values) {\n\t\t\tid\n\t\t\toperation\n\t\t\tstatus\n\t\t\tlaytimeAllowed\n\t\t\tlaytimeUsed\n\t\t\tamount\n\t\t}\n\t}\n"): typeof import('./graphql').InsertLaytimeDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation InsertPort($values: PortInsertInput!) {\n\t\tinsertIntoPortSingle(values: $values) {\n\t\t\tid\n\t\t\tname\n\t\t\tcountryCode\n\t\t}\n\t}\n"): typeof import('./graphql').InsertPortDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation InsertSof($values: SofInsertInput!) {\n\t\tinsertIntoSofSingle(values: $values) {\n\t\t\tid\n\t\t\tcargoId\n\t\t\tportId\n\t\t\tcharterPartyTermsId\n\t\t\tvesselId\n\t\t\tchartererId\n\t\t}\n\t}\n"): typeof import('./graphql').InsertSofDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "query getSingleLaytime($slug: String!) {\n  laytimeSingle(where: {id: {eq: $slug}}) {\n    id\n    sof {\n      id\n      cargo {\n        id\n        quantity\n        type\n        unit\n      }\n      vessel {\n        id\n        name\n      }\n      charterPartyTerms {\n        id\n        loadingRate\n        dischargingRate\n        laytimeCalculationModifiers\n        demurrageRate\n      }\n      charterer {\n        id\n        email\n        name\n      }\n      exclusions {\n        id\n        dateFrom\n        dateTo\n        event\n        duration\n        percentage\n        timeUsed\n      }\n      events {\n        id\n        timestamp\n        description\n        type\n      }\n      port {\n        id\n        name\n        countryCode\n      }\n    }\n    comments {\n      id\n      message\n      status\n      createdAt\n      user {\n        id\n        firstName\n        lastName\n        avatar\n      }\n    }\n    amount\n    createdAt\n    operation\n    laytimeAllowed\n    laytimeStarted\n    laytimeUsed\n    norAccepted\n    norTendered\n    status\n    updatedAt\n    laycanFrom\n    laycanTo\n  }\n}"): typeof import('./graphql').GetSingleLaytimeDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tquery getLaytimes($statusFilter: LaytimeFilters!) {\n\t\tlaytime(where: $statusFilter) {\n\t\t\tcomments {\n\t\t\t\tid\n\t\t\t}\n\t\t\toperator {\n\t\t\t\tfirstName\n\t\t\t\tlastName\n\t\t\t\tavatar\n\t\t\t}\n\t\t\tsof {\n\t\t\t\tvessel {\n\t\t\t\t\tname\n\t\t\t\t}\n\t\t\t\tport {\n\t\t\t\t\tid\n\t\t\t\t\tname\n\t\t\t\t\tcountryCode\n\t\t\t\t}\n\t\t\t\tcharterer {\n\t\t\t\t\tid\n\t\t\t\t\tname\n\t\t\t\t}\n\t\t\t}\n\t\t\tid\n\t\t\tamount\n\t\t\tstatus\n\t\t\tcreatedAt\n\t\t}\n\t}\n"): typeof import('./graphql').GetLaytimesDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation UpdateLaytime($id: String!, $set: LaytimeUpdateInput!) {\n\t\tupdateLaytime(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').UpdateLaytimeDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "query getPorts{\n  port {\n    id\n    name\n\tcountryCode\n  }\n}"): typeof import('./graphql').GetPortsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "query getPortCalls{\n  portCall {\n    agentName\n    eta\n    etd\n    fileId\n    operatorName\n    portFunction\n    portName\n    portCountryCode\n    status\n    vesselImo\n    vesselName\n  }\n}"): typeof import('./graphql').GetPortCallsDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation Signed($input: SignedUrlInput!) {\n\t\tgetSignedUrl(input: $input) {\n\t\t\tsignedUrl\n\t\t}\n\t}\n"): typeof import('./graphql').SignedDocument;
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(source: "\n\tmutation UpdateSof($id: String!, $set: SofUpdateInput!) {\n\t\tupdateSof(where: { id: { eq: $id } }, set: $set) {\n\t\t\tid\n\t\t}\n\t}\n"): typeof import('./graphql').UpdateSofDocument;


export function gql(source: string) {
  return (documents as any)[source] ?? {};
}
