schema {
  query: Query
  mutation: Mutation
}

input AgentCompanyFilters {
  OR: [AgentCompanyfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentCompanyfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentCountryFilters {
  OR: [AgentCountryfiltersOr!]
  """JSON"""
  eq: String
  """JSON"""
  gt: String
  """JSON"""
  gte: String
  ilike: String
  """Array<JSON>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """JSON"""
  lt: String
  """JSON"""
  lte: String
  """JSON"""
  ne: String
  notIlike: String
  """Array<JSON>"""
  notInArray: [String!]
  notLike: String
}

input AgentCountryfiltersOr {
  """JSON"""
  eq: String
  """JSON"""
  gt: String
  """JSON"""
  gte: String
  ilike: String
  """Array<JSON>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """JSON"""
  lt: String
  """JSON"""
  lte: String
  """JSON"""
  ne: String
  notIlike: String
  """Array<JSON>"""
  notInArray: [String!]
  notLike: String
}

input AgentEmailFilters {
  OR: [AgentEmailfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentEmailfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentFilters {
  OR: [AgentFiltersOr!]
  company: AgentCompanyFilters
  country: AgentCountryFilters
  email: AgentEmailFilters
  id: AgentIdFilters
  phone: AgentPhoneFilters
  verified: AgentVerifiedFilters
}

input AgentFiltersOr {
  company: AgentCompanyFilters
  country: AgentCountryFilters
  email: AgentEmailFilters
  id: AgentIdFilters
  phone: AgentPhoneFilters
  verified: AgentVerifiedFilters
}

input AgentIdFilters {
  OR: [AgentIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentInsertInput {
  company: String!
  """JSON"""
  country: String!
  email: String!
  id: String
  phone: String!
  verified: Boolean
}

type AgentItem {
  company: String!
  """JSON"""
  country: String!
  email: String!
  id: String!
  phone: String!
  verified: Boolean!
}

input AgentOrderBy {
  company: InnerOrder
  country: InnerOrder
  email: InnerOrder
  id: InnerOrder
  phone: InnerOrder
  verified: InnerOrder
}

input AgentPhoneFilters {
  OR: [AgentPhonefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AgentPhonefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type AgentSelectItem {
  company: String!
  """JSON"""
  country: String!
  email: String!
  id: String!
  phone: String!
  verified: Boolean!
}

input AgentUpdateInput {
  company: String
  """JSON"""
  country: String
  email: String
  id: String
  phone: String
  verified: Boolean
}

input AgentVerifiedFilters {
  OR: [AgentVerifiedfiltersOr!]
  eq: Boolean
  gt: Boolean
  gte: Boolean
  ilike: String
  """Array<undefined>"""
  inArray: [Boolean!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Boolean
  lte: Boolean
  ne: Boolean
  notIlike: String
  """Array<undefined>"""
  notInArray: [Boolean!]
  notLike: String
}

input AgentVerifiedfiltersOr {
  eq: Boolean
  gt: Boolean
  gte: Boolean
  ilike: String
  """Array<undefined>"""
  inArray: [Boolean!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Boolean
  lte: Boolean
  ne: Boolean
  notIlike: String
  """Array<undefined>"""
  notInArray: [Boolean!]
  notLike: String
}

input AppointmentAgentIdFilters {
  OR: [AppointmentAgentIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentAgentIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentAgentNameFilters {
  OR: [AppointmentAgentNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentAgentNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentEtaFilters {
  OR: [AppointmentEtafiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentEtafiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentFileIdFilters {
  OR: [AppointmentFileIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentFileIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentFilters {
  OR: [AppointmentFiltersOr!]
  agentId: AppointmentAgentIdFilters
  agentName: AppointmentAgentNameFilters
  eta: AppointmentEtaFilters
  fileId: AppointmentFileIdFilters
  id: AppointmentIdFilters
  operatorId: AppointmentOperatorIdFilters
  operatorName: AppointmentOperatorNameFilters
  portCountryCode: AppointmentPortCountryCodeFilters
  portFunction: AppointmentPortFunctionFilters
  portId: AppointmentPortIdFilters
  portName: AppointmentPortNameFilters
  requestDate: AppointmentRequestDateFilters
  status: AppointmentStatusFilters
  vesselId: AppointmentVesselIdFilters
  vesselImo: AppointmentVesselImoFilters
  vesselName: AppointmentVesselNameFilters
}

input AppointmentFiltersOr {
  agentId: AppointmentAgentIdFilters
  agentName: AppointmentAgentNameFilters
  eta: AppointmentEtaFilters
  fileId: AppointmentFileIdFilters
  id: AppointmentIdFilters
  operatorId: AppointmentOperatorIdFilters
  operatorName: AppointmentOperatorNameFilters
  portCountryCode: AppointmentPortCountryCodeFilters
  portFunction: AppointmentPortFunctionFilters
  portId: AppointmentPortIdFilters
  portName: AppointmentPortNameFilters
  requestDate: AppointmentRequestDateFilters
  status: AppointmentStatusFilters
  vesselId: AppointmentVesselIdFilters
  vesselImo: AppointmentVesselImoFilters
  vesselName: AppointmentVesselNameFilters
}

input AppointmentIdFilters {
  OR: [AppointmentIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentInsertInput {
  agentId: String!
  agentName: String!
  eta: String!
  fileId: String!
  id: String
  operatorId: String!
  operatorName: String!
  portCountryCode: String!
  portFunction: AppointmentPortFunctionEnum!
  portId: String!
  portName: String!
  requestDate: String!
  status: String!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

type AppointmentItem {
  agentId: String!
  agentName: String!
  eta: String!
  fileId: String!
  id: String!
  operatorId: String!
  operatorName: String!
  portCountryCode: String!
  portFunction: AppointmentPortFunctionEnum!
  portId: String!
  portName: String!
  requestDate: String!
  status: String!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

input AppointmentOperatorIdFilters {
  OR: [AppointmentOperatorIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentOperatorIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentOperatorNameFilters {
  OR: [AppointmentOperatorNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentOperatorNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentOrderBy {
  agentId: InnerOrder
  agentName: InnerOrder
  eta: InnerOrder
  fileId: InnerOrder
  id: InnerOrder
  operatorId: InnerOrder
  operatorName: InnerOrder
  portCountryCode: InnerOrder
  portFunction: InnerOrder
  portId: InnerOrder
  portName: InnerOrder
  requestDate: InnerOrder
  status: InnerOrder
  vesselId: InnerOrder
  vesselImo: InnerOrder
  vesselName: InnerOrder
}

input AppointmentPortCountryCodeFilters {
  OR: [AppointmentPortCountryCodefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentPortCountryCodefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

enum AppointmentPortFunctionEnum {
  """Value: ace"""
  ace
  """Value: aes"""
  aes
  """Value: bunkering"""
  bunkering
  """Value: canal_transit"""
  canal_transit
  """Value: cargo_operations"""
  cargo_operations
  """Value: cleaning"""
  cleaning
  """Value: clearance"""
  clearance
  """Value: crew_change"""
  crew_change
  """Value: cruise_call"""
  cruise_call
  """Value: delivery_redelivery"""
  delivery_redelivery
  """Value: documentation"""
  documentation
  """Value: dry_docking"""
  dry_docking
  """Value: gassing_up"""
  gassing_up
  """Value: husbandry"""
  husbandry
  """Value: lay_up"""
  lay_up
  """Value: other"""
  other
  """Value: purging"""
  purging
  """Value: repairs"""
  repairs
  """Value: shelter_layby"""
  shelter_layby
  """Value: towage_on_floating_lines"""
  towage_on_floating_lines
  """Value: waiting_layby"""
  waiting_layby
}

input AppointmentPortFunctionFilters {
  OR: [AppointmentPortFunctionfiltersOr!]
  eq: AppointmentPortFunctionEnum
  gt: AppointmentPortFunctionEnum
  gte: AppointmentPortFunctionEnum
  ilike: String
  """Array<undefined>"""
  inArray: [AppointmentPortFunctionEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: AppointmentPortFunctionEnum
  lte: AppointmentPortFunctionEnum
  ne: AppointmentPortFunctionEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [AppointmentPortFunctionEnum!]
  notLike: String
}

input AppointmentPortFunctionfiltersOr {
  eq: AppointmentPortFunctionEnum
  gt: AppointmentPortFunctionEnum
  gte: AppointmentPortFunctionEnum
  ilike: String
  """Array<undefined>"""
  inArray: [AppointmentPortFunctionEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: AppointmentPortFunctionEnum
  lte: AppointmentPortFunctionEnum
  ne: AppointmentPortFunctionEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [AppointmentPortFunctionEnum!]
  notLike: String
}

input AppointmentPortIdFilters {
  OR: [AppointmentPortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentPortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentPortNameFilters {
  OR: [AppointmentPortNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentPortNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentRequestDateFilters {
  OR: [AppointmentRequestDatefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentRequestDatefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type AppointmentSelectItem {
  agentId: String!
  agentName: String!
  eta: String!
  fileId: String!
  id: String!
  operatorId: String!
  operatorName: String!
  portCountryCode: String!
  portFunction: AppointmentPortFunctionEnum!
  portId: String!
  portName: String!
  requestDate: String!
  status: String!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

input AppointmentStatusFilters {
  OR: [AppointmentStatusfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentStatusfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentUpdateInput {
  agentId: String
  agentName: String
  eta: String
  fileId: String
  id: String
  operatorId: String
  operatorName: String
  portCountryCode: String
  portFunction: AppointmentPortFunctionEnum
  portId: String
  portName: String
  requestDate: String
  status: String
  vesselId: String
  vesselImo: String
  vesselName: String
}

input AppointmentVesselIdFilters {
  OR: [AppointmentVesselIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentVesselIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentVesselImoFilters {
  OR: [AppointmentVesselImofiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentVesselImofiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentVesselNameFilters {
  OR: [AppointmentVesselNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input AppointmentVesselNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoFilters {
  OR: [CargoFiltersOr!]
  id: CargoIdFilters
  quantity: CargoQuantityFilters
  type: CargoTypeFilters
  unit: CargoUnitFilters
}

input CargoFiltersOr {
  id: CargoIdFilters
  quantity: CargoQuantityFilters
  type: CargoTypeFilters
  unit: CargoUnitFilters
}

input CargoIdFilters {
  OR: [CargoIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoInsertInput {
  id: String
  quantity: Float!
  type: String!
  unit: String
}

type CargoItem {
  id: String!
  quantity: Float!
  type: String!
  unit: String
}

input CargoOrderBy {
  id: InnerOrder
  quantity: InnerOrder
  type: InnerOrder
  unit: InnerOrder
}

input CargoQuantityFilters {
  OR: [CargoQuantityfiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CargoQuantityfiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

type CargoSelectItem {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CargoSofsRelation!]!
  type: String!
  unit: String
}

type CargoSofsRelation {
  cargo(where: CargoFilters): CargoSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): CargoSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): CargoSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [CargoSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [CargoSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): CargoSofsRelationLaytimeRelation
  port(where: PortFilters): CargoSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): CargoSofsRelationVesselRelation
  vesselId: String!
}

type CargoSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  type: String!
  unit: String
}

type CargoSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CargoSofsRelationCharterPartyTermsRelationSofsRelation!]!
}

type CargoSofsRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CargoSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CargoSofsRelationChartererRelationSofsRelation!]!
}

type CargoSofsRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CargoSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): CargoSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type CargoSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CargoSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): CargoSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type CargoSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CargoSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CargoSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): CargoSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): CargoSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [CargoSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type CargoSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CargoSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CargoSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type CargoSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CargoSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CargoSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CargoSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CargoSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CargoSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CargoSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CargoSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CargoSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CargoSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type CargoSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type CargoSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): CargoSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type CargoSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CargoSofsRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [CargoSofsRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [CargoSofsRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CargoSofsRelationPortRelationSofsRelation!]!
}

type CargoSofsRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): CargoSofsRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type CargoSofsRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CargoSofsRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): CargoSofsRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type CargoSofsRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CargoSofsRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CargoSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CargoSofsRelationVesselRelationSofsRelation!]!
  type: String!
}

type CargoSofsRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input CargoTypeFilters {
  OR: [CargoTypefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoTypefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoUnitFilters {
  OR: [CargoUnitfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoUnitfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CargoUpdateInput {
  id: String
  quantity: Float
  type: String
  unit: String
}

input CharterPartyTermsDemurrageRateFilters {
  OR: [CharterPartyTermsDemurrageRatefiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsDemurrageRatefiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsDischargingRateFilters {
  OR: [CharterPartyTermsDischargingRatefiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsDischargingRatefiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsFilters {
  OR: [CharterPartyTermsFiltersOr!]
  demurrageRate: CharterPartyTermsDemurrageRateFilters
  dischargingRate: CharterPartyTermsDischargingRateFilters
  id: CharterPartyTermsIdFilters
  laytimeAllowed: CharterPartyTermsLaytimeAllowedFilters
  laytimeCalculationModifiers: CharterPartyTermsLaytimeCalculationModifiersFilters
  loadingRate: CharterPartyTermsLoadingRateFilters
}

input CharterPartyTermsFiltersOr {
  demurrageRate: CharterPartyTermsDemurrageRateFilters
  dischargingRate: CharterPartyTermsDischargingRateFilters
  id: CharterPartyTermsIdFilters
  laytimeAllowed: CharterPartyTermsLaytimeAllowedFilters
  laytimeCalculationModifiers: CharterPartyTermsLaytimeCalculationModifiersFilters
  loadingRate: CharterPartyTermsLoadingRateFilters
}

input CharterPartyTermsIdFilters {
  OR: [CharterPartyTermsIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CharterPartyTermsIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CharterPartyTermsInsertInput {
  demurrageRate: Float
  dischargingRate: Float
  id: String
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
}

type CharterPartyTermsItem {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
}

input CharterPartyTermsLaytimeAllowedFilters {
  OR: [CharterPartyTermsLaytimeAllowedfiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsLaytimeAllowedfiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsLaytimeCalculationModifiersFilters {
  OR: [CharterPartyTermsLaytimeCalculationModifiersfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CharterPartyTermsLaytimeCalculationModifiersfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CharterPartyTermsLoadingRateFilters {
  OR: [CharterPartyTermsLoadingRatefiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsLoadingRatefiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input CharterPartyTermsOrderBy {
  demurrageRate: InnerOrder
  dischargingRate: InnerOrder
  id: InnerOrder
  laytimeAllowed: InnerOrder
  laytimeCalculationModifiers: InnerOrder
  loadingRate: InnerOrder
}

type CharterPartyTermsSelectItem {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CharterPartyTermsSofsRelation!]!
}

type CharterPartyTermsSofsRelation {
  cargo(where: CargoFilters): CharterPartyTermsSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): CharterPartyTermsSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): CharterPartyTermsSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [CharterPartyTermsSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [CharterPartyTermsSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): CharterPartyTermsSofsRelationLaytimeRelation
  port(where: PortFilters): CharterPartyTermsSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): CharterPartyTermsSofsRelationVesselRelation
  vesselId: String!
}

type CharterPartyTermsSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CharterPartyTermsSofsRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type CharterPartyTermsSofsRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CharterPartyTermsSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
}

type CharterPartyTermsSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CharterPartyTermsSofsRelationChartererRelationSofsRelation!]!
}

type CharterPartyTermsSofsRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CharterPartyTermsSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): CharterPartyTermsSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type CharterPartyTermsSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CharterPartyTermsSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): CharterPartyTermsSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type CharterPartyTermsSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CharterPartyTermsSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CharterPartyTermsSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): CharterPartyTermsSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): CharterPartyTermsSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type CharterPartyTermsSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type CharterPartyTermsSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type CharterPartyTermsSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CharterPartyTermsSofsRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [CharterPartyTermsSofsRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [CharterPartyTermsSofsRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CharterPartyTermsSofsRelationPortRelationSofsRelation!]!
}

type CharterPartyTermsSofsRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): CharterPartyTermsSofsRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type CharterPartyTermsSofsRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CharterPartyTermsSofsRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): CharterPartyTermsSofsRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type CharterPartyTermsSofsRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CharterPartyTermsSofsRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CharterPartyTermsSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CharterPartyTermsSofsRelationVesselRelationSofsRelation!]!
  type: String!
}

type CharterPartyTermsSofsRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input CharterPartyTermsUpdateInput {
  demurrageRate: Float
  dischargingRate: Float
  id: String
  laytimeAllowed: Float
  laytimeCalculationModifiers: String
  loadingRate: Float
}

input ChartererEmailFilters {
  OR: [ChartererEmailfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererEmailfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererFilters {
  OR: [ChartererFiltersOr!]
  email: ChartererEmailFilters
  id: ChartererIdFilters
  name: ChartererNameFilters
  phone: ChartererPhoneFilters
}

input ChartererFiltersOr {
  email: ChartererEmailFilters
  id: ChartererIdFilters
  name: ChartererNameFilters
  phone: ChartererPhoneFilters
}

input ChartererIdFilters {
  OR: [ChartererIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererInsertInput {
  email: String
  id: String
  name: String!
  phone: String
}

type ChartererItem {
  email: String
  id: String!
  name: String!
  phone: String
}

input ChartererNameFilters {
  OR: [ChartererNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererOrderBy {
  email: InnerOrder
  id: InnerOrder
  name: InnerOrder
  phone: InnerOrder
}

input ChartererPhoneFilters {
  OR: [ChartererPhonefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ChartererPhonefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type ChartererSelectItem {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ChartererSofsRelation!]!
}

type ChartererSofsRelation {
  cargo(where: CargoFilters): ChartererSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): ChartererSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): ChartererSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [ChartererSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [ChartererSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): ChartererSofsRelationLaytimeRelation
  port(where: PortFilters): ChartererSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): ChartererSofsRelationVesselRelation
  vesselId: String!
}

type ChartererSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ChartererSofsRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type ChartererSofsRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ChartererSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ChartererSofsRelationCharterPartyTermsRelationSofsRelation!]!
}

type ChartererSofsRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ChartererSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
}

type ChartererSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): ChartererSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type ChartererSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ChartererSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): ChartererSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type ChartererSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ChartererSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ChartererSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): ChartererSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): ChartererSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [ChartererSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type ChartererSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): ChartererSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): ChartererSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type ChartererSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ChartererSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [ChartererSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type ChartererSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type ChartererSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ChartererSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [ChartererSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type ChartererSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): ChartererSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): ChartererSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type ChartererSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type ChartererSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): ChartererSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type ChartererSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ChartererSofsRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [ChartererSofsRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [ChartererSofsRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ChartererSofsRelationPortRelationSofsRelation!]!
}

type ChartererSofsRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): ChartererSofsRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type ChartererSofsRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type ChartererSofsRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): ChartererSofsRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type ChartererSofsRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type ChartererSofsRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ChartererSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ChartererSofsRelationVesselRelationSofsRelation!]!
  type: String!
}

type ChartererSofsRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input ChartererUpdateInput {
  email: String
  id: String
  name: String
  phone: String
}

input CommentCreatedAtFilters {
  OR: [CommentCreatedAtfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input CommentCreatedAtfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input CommentFilters {
  OR: [CommentFiltersOr!]
  createdAt: CommentCreatedAtFilters
  id: CommentIdFilters
  laytimeId: CommentLaytimeIdFilters
  message: CommentMessageFilters
  status: CommentStatusFilters
  userId: CommentUserIdFilters
}

input CommentFiltersOr {
  createdAt: CommentCreatedAtFilters
  id: CommentIdFilters
  laytimeId: CommentLaytimeIdFilters
  message: CommentMessageFilters
  status: CommentStatusFilters
  userId: CommentUserIdFilters
}

input CommentIdFilters {
  OR: [CommentIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CommentIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CommentInsertInput {
  """Date"""
  createdAt: String
  id: String
  laytimeId: String!
  message: String!
  status: CommentStatusEnum
  userId: String!
}

type CommentItem {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

input CommentLaytimeIdFilters {
  OR: [CommentLaytimeIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CommentLaytimeIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type CommentLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CommentLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): CommentLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): CommentLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [CommentLaytimeRelationUploadedFilesRelation!]!
}

type CommentLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CommentLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CommentLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CommentLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CommentLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CommentLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CommentLaytimeRelationSofRelation {
  cargo(where: CargoFilters): CommentLaytimeRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): CommentLaytimeRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): CommentLaytimeRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [CommentLaytimeRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [CommentLaytimeRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): CommentLaytimeRelationSofRelationLaytimeRelation
  port(where: PortFilters): CommentLaytimeRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): CommentLaytimeRelationSofRelationVesselRelation
  vesselId: String!
}

type CommentLaytimeRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentLaytimeRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type CommentLaytimeRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type CommentLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentLaytimeRelationSofRelationChartererRelationSofsRelation!]!
}

type CommentLaytimeRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): CommentLaytimeRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type CommentLaytimeRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): CommentLaytimeRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type CommentLaytimeRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CommentLaytimeRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [CommentLaytimeRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [CommentLaytimeRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentLaytimeRelationSofRelationPortRelationSofsRelation!]!
}

type CommentLaytimeRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): CommentLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type CommentLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CommentLaytimeRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): CommentLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type CommentLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CommentLaytimeRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentLaytimeRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type CommentLaytimeRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): CommentLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): CommentLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type CommentLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CommentLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CommentLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CommentLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CommentLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CommentLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

input CommentMessageFilters {
  OR: [CommentMessagefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CommentMessagefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CommentOrderBy {
  createdAt: InnerOrder
  id: InnerOrder
  laytimeId: InnerOrder
  message: InnerOrder
  status: InnerOrder
  userId: InnerOrder
}

type CommentSelectItem {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): CommentLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): CommentUserRelation
  userId: String!
}

enum CommentStatusEnum {
  """Value: approved"""
  approved
  """Value: changes_requested"""
  changes_requested
  """Value: pending_approval"""
  pending_approval
  """Value: submitted"""
  submitted
}

input CommentStatusFilters {
  OR: [CommentStatusfiltersOr!]
  eq: CommentStatusEnum
  gt: CommentStatusEnum
  gte: CommentStatusEnum
  ilike: String
  """Array<undefined>"""
  inArray: [CommentStatusEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: CommentStatusEnum
  lte: CommentStatusEnum
  ne: CommentStatusEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [CommentStatusEnum!]
  notLike: String
}

input CommentStatusfiltersOr {
  eq: CommentStatusEnum
  gt: CommentStatusEnum
  gte: CommentStatusEnum
  ilike: String
  """Array<undefined>"""
  inArray: [CommentStatusEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: CommentStatusEnum
  lte: CommentStatusEnum
  ne: CommentStatusEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [CommentStatusEnum!]
  notLike: String
}

input CommentUpdateInput {
  """Date"""
  createdAt: String
  id: String
  laytimeId: String
  message: String
  status: CommentStatusEnum
  userId: String
}

input CommentUserIdFilters {
  OR: [CommentUserIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input CommentUserIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type CommentUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CommentUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [CommentUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type CommentUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CommentUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CommentUserRelationLaytimesRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): CommentUserRelationLaytimesRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): CommentUserRelationLaytimesRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [CommentUserRelationLaytimesRelationUploadedFilesRelation!]!
}

type CommentUserRelationLaytimesRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type CommentUserRelationLaytimesRelationOperatorRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type CommentUserRelationLaytimesRelationSofRelation {
  cargo(where: CargoFilters): CommentUserRelationLaytimesRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): CommentUserRelationLaytimesRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): CommentUserRelationLaytimesRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [CommentUserRelationLaytimesRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [CommentUserRelationLaytimesRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): CommentUserRelationLaytimesRelationSofRelationLaytimeRelation
  port(where: PortFilters): CommentUserRelationLaytimesRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): CommentUserRelationLaytimesRelationSofRelationVesselRelation
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentUserRelationLaytimesRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type CommentUserRelationLaytimesRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentUserRelationLaytimesRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type CommentUserRelationLaytimesRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentUserRelationLaytimesRelationSofRelationChartererRelationSofsRelation!]!
}

type CommentUserRelationLaytimesRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): CommentUserRelationLaytimesRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type CommentUserRelationLaytimesRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): CommentUserRelationLaytimesRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type CommentUserRelationLaytimesRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CommentUserRelationLaytimesRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [CommentUserRelationLaytimesRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [CommentUserRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentUserRelationLaytimesRelationSofRelationPortRelationSofsRelation!]!
}

type CommentUserRelationLaytimesRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): CommentUserRelationLaytimesRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type CommentUserRelationLaytimesRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CommentUserRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): CommentUserRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type CommentUserRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type CommentUserRelationLaytimesRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [CommentUserRelationLaytimesRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type CommentUserRelationLaytimesRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type CommentUserRelationLaytimesRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): CommentUserRelationLaytimesRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): CommentUserRelationLaytimesRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type CommentUserRelationLaytimesRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type CommentUserRelationLaytimesRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

input DisbursementAgentIdFilters {
  OR: [DisbursementAgentIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementAgentIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementAgentNameFilters {
  OR: [DisbursementAgentNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementAgentNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementAmountFilters {
  OR: [DisbursementAmountfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementAmountfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementCtaFilters {
  OR: [DisbursementCtafiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementCtafiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

enum DisbursementDaEnum {
  """Value: dda"""
  dda
  """Value: fda"""
  fda
  """Value: pda"""
  pda
  """Value: sda"""
  sda
}

input DisbursementDaFilters {
  OR: [DisbursementDafiltersOr!]
  eq: DisbursementDaEnum
  gt: DisbursementDaEnum
  gte: DisbursementDaEnum
  ilike: String
  """Array<undefined>"""
  inArray: [DisbursementDaEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: DisbursementDaEnum
  lte: DisbursementDaEnum
  ne: DisbursementDaEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [DisbursementDaEnum!]
  notLike: String
}

input DisbursementDafiltersOr {
  eq: DisbursementDaEnum
  gt: DisbursementDaEnum
  gte: DisbursementDaEnum
  ilike: String
  """Array<undefined>"""
  inArray: [DisbursementDaEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: DisbursementDaEnum
  lte: DisbursementDaEnum
  ne: DisbursementDaEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [DisbursementDaEnum!]
  notLike: String
}

input DisbursementDateFilters {
  OR: [DisbursementDatefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementDatefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementFilters {
  OR: [DisbursementFiltersOr!]
  agentId: DisbursementAgentIdFilters
  agentName: DisbursementAgentNameFilters
  amount: DisbursementAmountFilters
  cta: DisbursementCtaFilters
  da: DisbursementDaFilters
  date: DisbursementDateFilters
  groupId: DisbursementGroupIdFilters
  id: DisbursementIdFilters
  portCountryCode: DisbursementPortCountryCodeFilters
  portId: DisbursementPortIdFilters
  portName: DisbursementPortNameFilters
  status: DisbursementStatusFilters
  vesselId: DisbursementVesselIdFilters
  vesselImo: DisbursementVesselImoFilters
  vesselName: DisbursementVesselNameFilters
}

input DisbursementFiltersOr {
  agentId: DisbursementAgentIdFilters
  agentName: DisbursementAgentNameFilters
  amount: DisbursementAmountFilters
  cta: DisbursementCtaFilters
  da: DisbursementDaFilters
  date: DisbursementDateFilters
  groupId: DisbursementGroupIdFilters
  id: DisbursementIdFilters
  portCountryCode: DisbursementPortCountryCodeFilters
  portId: DisbursementPortIdFilters
  portName: DisbursementPortNameFilters
  status: DisbursementStatusFilters
  vesselId: DisbursementVesselIdFilters
  vesselImo: DisbursementVesselImoFilters
  vesselName: DisbursementVesselNameFilters
}

input DisbursementGroupIdFilters {
  OR: [DisbursementGroupIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementGroupIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementIdFilters {
  OR: [DisbursementIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementInsertInput {
  agentId: String!
  agentName: String!
  amount: String!
  cta: String!
  da: DisbursementDaEnum!
  date: String!
  groupId: String!
  id: String
  portCountryCode: String!
  portId: String!
  portName: String!
  status: DisbursementStatusEnum!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

type DisbursementItem {
  agentId: String!
  agentName: String!
  amount: String!
  cta: String!
  da: DisbursementDaEnum!
  date: String!
  groupId: String!
  id: String!
  portCountryCode: String!
  portId: String!
  portName: String!
  status: DisbursementStatusEnum!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

input DisbursementOrderBy {
  agentId: InnerOrder
  agentName: InnerOrder
  amount: InnerOrder
  cta: InnerOrder
  da: InnerOrder
  date: InnerOrder
  groupId: InnerOrder
  id: InnerOrder
  portCountryCode: InnerOrder
  portId: InnerOrder
  portName: InnerOrder
  status: InnerOrder
  vesselId: InnerOrder
  vesselImo: InnerOrder
  vesselName: InnerOrder
}

input DisbursementPortCountryCodeFilters {
  OR: [DisbursementPortCountryCodefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementPortCountryCodefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementPortIdFilters {
  OR: [DisbursementPortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementPortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementPortNameFilters {
  OR: [DisbursementPortNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementPortNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type DisbursementSelectItem {
  agentId: String!
  agentName: String!
  amount: String!
  cta: String!
  da: DisbursementDaEnum!
  date: String!
  groupId: String!
  id: String!
  portCountryCode: String!
  portId: String!
  portName: String!
  status: DisbursementStatusEnum!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

enum DisbursementStatusEnum {
  """Value: pending approval"""
  Option2
  """Value: approved"""
  approved
  """Value: completed"""
  completed
  """Value: draft"""
  draft
  """Value: rejected"""
  rejected
  """Value: settled"""
  settled
  """Value: submitted"""
  submitted
}

input DisbursementStatusFilters {
  OR: [DisbursementStatusfiltersOr!]
  eq: DisbursementStatusEnum
  gt: DisbursementStatusEnum
  gte: DisbursementStatusEnum
  ilike: String
  """Array<undefined>"""
  inArray: [DisbursementStatusEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: DisbursementStatusEnum
  lte: DisbursementStatusEnum
  ne: DisbursementStatusEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [DisbursementStatusEnum!]
  notLike: String
}

input DisbursementStatusfiltersOr {
  eq: DisbursementStatusEnum
  gt: DisbursementStatusEnum
  gte: DisbursementStatusEnum
  ilike: String
  """Array<undefined>"""
  inArray: [DisbursementStatusEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: DisbursementStatusEnum
  lte: DisbursementStatusEnum
  ne: DisbursementStatusEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [DisbursementStatusEnum!]
  notLike: String
}

input DisbursementUpdateInput {
  agentId: String
  agentName: String
  amount: String
  cta: String
  da: DisbursementDaEnum
  date: String
  groupId: String
  id: String
  portCountryCode: String
  portId: String
  portName: String
  status: DisbursementStatusEnum
  vesselId: String
  vesselImo: String
  vesselName: String
}

input DisbursementVesselIdFilters {
  OR: [DisbursementVesselIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementVesselIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementVesselImoFilters {
  OR: [DisbursementVesselImofiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementVesselImofiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementVesselNameFilters {
  OR: [DisbursementVesselNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input DisbursementVesselNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventDescriptionFilters {
  OR: [EventDescriptionfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventDescriptionfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventFilters {
  OR: [EventFiltersOr!]
  description: EventDescriptionFilters
  id: EventIdFilters
  sofId: EventSofIdFilters
  timestamp: EventTimestampFilters
  type: EventTypeFilters
}

input EventFiltersOr {
  description: EventDescriptionFilters
  id: EventIdFilters
  sofId: EventSofIdFilters
  timestamp: EventTimestampFilters
  type: EventTypeFilters
}

input EventIdFilters {
  OR: [EventIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventInsertInput {
  description: String
  id: String
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type EventItem {
  description: String
  id: String!
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

input EventOrderBy {
  description: InnerOrder
  id: InnerOrder
  sofId: InnerOrder
  timestamp: InnerOrder
  type: InnerOrder
}

type EventSelectItem {
  description: String
  id: String!
  sof(where: SofFilters): EventSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

input EventSofIdFilters {
  OR: [EventSofIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventSofIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type EventSofRelation {
  cargo(where: CargoFilters): EventSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): EventSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): EventSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [EventSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [EventSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): EventSofRelationLaytimeRelation
  port(where: PortFilters): EventSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): EventSofRelationVesselRelation
  vesselId: String!
}

type EventSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [EventSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type EventSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type EventSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [EventSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type EventSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type EventSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [EventSofRelationChartererRelationSofsRelation!]!
}

type EventSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type EventSofRelationEventsRelation {
  description: String
  id: String!
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type EventSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): EventSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type EventSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type EventSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [EventSofRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): EventSofRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): EventSofRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [EventSofRelationLaytimeRelationUploadedFilesRelation!]!
}

type EventSofRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): EventSofRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): EventSofRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type EventSofRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [EventSofRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [EventSofRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type EventSofRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type EventSofRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [EventSofRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [EventSofRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type EventSofRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): EventSofRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): EventSofRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type EventSofRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type EventSofRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type EventSofRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): EventSofRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type EventSofRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type EventSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type EventSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [EventSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [EventSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [EventSofRelationPortRelationSofsRelation!]!
}

type EventSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): EventSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type EventSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type EventSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): EventSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type EventSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type EventSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type EventSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [EventSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type EventSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input EventTimestampFilters {
  OR: [EventTimestampfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input EventTimestampfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input EventTypeFilters {
  OR: [EventTypefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventTypefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input EventUpdateInput {
  description: String
  id: String
  sofId: String
  """Date"""
  timestamp: String
  type: String
}

input ExclusionDateFromFilters {
  OR: [ExclusionDateFromfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionDateFromfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionDateToFilters {
  OR: [ExclusionDateTofiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionDateTofiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionDurationFilters {
  OR: [ExclusionDurationfiltersOr!]
  eq: Int
  gt: Int
  gte: Int
  ilike: String
  """Array<undefined>"""
  inArray: [Int!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Int
  lte: Int
  ne: Int
  notIlike: String
  """Array<undefined>"""
  notInArray: [Int!]
  notLike: String
}

input ExclusionDurationfiltersOr {
  eq: Int
  gt: Int
  gte: Int
  ilike: String
  """Array<undefined>"""
  inArray: [Int!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Int
  lte: Int
  ne: Int
  notIlike: String
  """Array<undefined>"""
  notInArray: [Int!]
  notLike: String
}

input ExclusionEventFilters {
  OR: [ExclusionEventfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionEventfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionFilters {
  OR: [ExclusionFiltersOr!]
  dateFrom: ExclusionDateFromFilters
  dateTo: ExclusionDateToFilters
  duration: ExclusionDurationFilters
  event: ExclusionEventFilters
  id: ExclusionIdFilters
  percentage: ExclusionPercentageFilters
  sofId: ExclusionSofIdFilters
  timeUsed: ExclusionTimeUsedFilters
}

input ExclusionFiltersOr {
  dateFrom: ExclusionDateFromFilters
  dateTo: ExclusionDateToFilters
  duration: ExclusionDurationFilters
  event: ExclusionEventFilters
  id: ExclusionIdFilters
  percentage: ExclusionPercentageFilters
  sofId: ExclusionSofIdFilters
  timeUsed: ExclusionTimeUsedFilters
}

input ExclusionIdFilters {
  OR: [ExclusionIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionInsertInput {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String
  percentage: String!
  sofId: String!
  timeUsed: Int!
}

type ExclusionItem {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sofId: String!
  timeUsed: Int!
}

input ExclusionOrderBy {
  dateFrom: InnerOrder
  dateTo: InnerOrder
  duration: InnerOrder
  event: InnerOrder
  id: InnerOrder
  percentage: InnerOrder
  sofId: InnerOrder
  timeUsed: InnerOrder
}

input ExclusionPercentageFilters {
  OR: [ExclusionPercentagefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionPercentagefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type ExclusionSelectItem {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): ExclusionSofRelation
  sofId: String!
  timeUsed: Int!
}

input ExclusionSofIdFilters {
  OR: [ExclusionSofIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input ExclusionSofIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type ExclusionSofRelation {
  cargo(where: CargoFilters): ExclusionSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): ExclusionSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): ExclusionSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [ExclusionSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [ExclusionSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): ExclusionSofRelationLaytimeRelation
  port(where: PortFilters): ExclusionSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): ExclusionSofRelationVesselRelation
  vesselId: String!
}

type ExclusionSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ExclusionSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type ExclusionSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ExclusionSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ExclusionSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type ExclusionSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ExclusionSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ExclusionSofRelationChartererRelationSofsRelation!]!
}

type ExclusionSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ExclusionSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): ExclusionSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type ExclusionSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ExclusionSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sofId: String!
  timeUsed: Int!
}

type ExclusionSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ExclusionSofRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): ExclusionSofRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): ExclusionSofRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [ExclusionSofRelationLaytimeRelationUploadedFilesRelation!]!
}

type ExclusionSofRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): ExclusionSofRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): ExclusionSofRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type ExclusionSofRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ExclusionSofRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [ExclusionSofRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type ExclusionSofRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type ExclusionSofRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ExclusionSofRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [ExclusionSofRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type ExclusionSofRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): ExclusionSofRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): ExclusionSofRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type ExclusionSofRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type ExclusionSofRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): ExclusionSofRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type ExclusionSofRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type ExclusionSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [ExclusionSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [ExclusionSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ExclusionSofRelationPortRelationSofsRelation!]!
}

type ExclusionSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): ExclusionSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type ExclusionSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type ExclusionSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): ExclusionSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type ExclusionSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type ExclusionSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type ExclusionSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [ExclusionSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type ExclusionSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input ExclusionTimeUsedFilters {
  OR: [ExclusionTimeUsedfiltersOr!]
  eq: Int
  gt: Int
  gte: Int
  ilike: String
  """Array<undefined>"""
  inArray: [Int!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Int
  lte: Int
  ne: Int
  notIlike: String
  """Array<undefined>"""
  notInArray: [Int!]
  notLike: String
}

input ExclusionTimeUsedfiltersOr {
  eq: Int
  gt: Int
  gte: Int
  ilike: String
  """Array<undefined>"""
  inArray: [Int!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Int
  lte: Int
  ne: Int
  notIlike: String
  """Array<undefined>"""
  notInArray: [Int!]
  notLike: String
}

input ExclusionUpdateInput {
  dateFrom: String
  dateTo: String
  duration: Int
  event: String
  id: String
  percentage: String
  sofId: String
  timeUsed: Int
}

input HolidayFilters {
  OR: [HolidayFiltersOr!]
  holidayDate: HolidayHolidayDateFilters
  id: HolidayIdFilters
  portId: HolidayPortIdFilters
}

input HolidayFiltersOr {
  holidayDate: HolidayHolidayDateFilters
  id: HolidayIdFilters
  portId: HolidayPortIdFilters
}

input HolidayHolidayDateFilters {
  OR: [HolidayHolidayDatefiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input HolidayHolidayDatefiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input HolidayIdFilters {
  OR: [HolidayIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input HolidayIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input HolidayInsertInput {
  """Date"""
  holidayDate: String!
  id: String
  portId: String!
}

type HolidayItem {
  """Date"""
  holidayDate: String!
  id: String!
  portId: String!
}

input HolidayOrderBy {
  holidayDate: InnerOrder
  id: InnerOrder
  portId: InnerOrder
}

input HolidayPortIdFilters {
  OR: [HolidayPortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input HolidayPortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type HolidayPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [HolidayPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [HolidayPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [HolidayPortRelationSofsRelation!]!
}

type HolidayPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  portId: String!
}

type HolidayPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): HolidayPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type HolidayPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type HolidayPortRelationSofsRelation {
  cargo(where: CargoFilters): HolidayPortRelationSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): HolidayPortRelationSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): HolidayPortRelationSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [HolidayPortRelationSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [HolidayPortRelationSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): HolidayPortRelationSofsRelationLaytimeRelation
  port(where: PortFilters): HolidayPortRelationSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): HolidayPortRelationSofsRelationVesselRelation
  vesselId: String!
}

type HolidayPortRelationSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [HolidayPortRelationSofsRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type HolidayPortRelationSofsRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidayPortRelationSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [HolidayPortRelationSofsRelationCharterPartyTermsRelationSofsRelation!]!
}

type HolidayPortRelationSofsRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidayPortRelationSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [HolidayPortRelationSofsRelationChartererRelationSofsRelation!]!
}

type HolidayPortRelationSofsRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidayPortRelationSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): HolidayPortRelationSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type HolidayPortRelationSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidayPortRelationSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): HolidayPortRelationSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type HolidayPortRelationSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidayPortRelationSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [HolidayPortRelationSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): HolidayPortRelationSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): HolidayPortRelationSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type HolidayPortRelationSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type HolidayPortRelationSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type HolidayPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type HolidayPortRelationSofsRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type HolidayPortRelationSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [HolidayPortRelationSofsRelationVesselRelationSofsRelation!]!
  type: String!
}

type HolidayPortRelationSofsRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type HolidaySelectItem {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): HolidayPortRelation
  portId: String!
}

input HolidayUpdateInput {
  """Date"""
  holidayDate: String
  id: String
  portId: String
}

input InnerOrder {
  direction: OrderDirection!
  """Priority of current field"""
  priority: Int!
}

input LaytimeAmountFilters {
  OR: [LaytimeAmountfiltersOr!]
  """JSON"""
  eq: String
  """JSON"""
  gt: String
  """JSON"""
  gte: String
  ilike: String
  """Array<JSON>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """JSON"""
  lt: String
  """JSON"""
  lte: String
  """JSON"""
  ne: String
  notIlike: String
  """Array<JSON>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeAmountfiltersOr {
  """JSON"""
  eq: String
  """JSON"""
  gt: String
  """JSON"""
  gte: String
  ilike: String
  """Array<JSON>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """JSON"""
  lt: String
  """JSON"""
  lte: String
  """JSON"""
  ne: String
  notIlike: String
  """Array<JSON>"""
  notInArray: [String!]
  notLike: String
}

type LaytimeCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): LaytimeCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): LaytimeCommentsRelationUserRelation
  userId: String!
}

type LaytimeCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type LaytimeCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [LaytimeCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [LaytimeCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type LaytimeCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type LaytimeCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

input LaytimeCreatedAtFilters {
  OR: [LaytimeCreatedAtfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeCreatedAtfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeFilters {
  OR: [LaytimeFiltersOr!]
  amount: LaytimeAmountFilters
  createdAt: LaytimeCreatedAtFilters
  id: LaytimeIdFilters
  laycanFrom: LaytimeLaycanFromFilters
  laycanTo: LaytimeLaycanToFilters
  laytimeAllowed: LaytimeLaytimeAllowedFilters
  laytimeStarted: LaytimeLaytimeStartedFilters
  laytimeUsed: LaytimeLaytimeUsedFilters
  norAccepted: LaytimeNorAcceptedFilters
  norTendered: LaytimeNorTenderedFilters
  operation: LaytimeOperationFilters
  operatorId: LaytimeOperatorIdFilters
  sofId: LaytimeSofIdFilters
  status: LaytimeStatusFilters
  updatedAt: LaytimeUpdatedAtFilters
}

input LaytimeFiltersOr {
  amount: LaytimeAmountFilters
  createdAt: LaytimeCreatedAtFilters
  id: LaytimeIdFilters
  laycanFrom: LaytimeLaycanFromFilters
  laycanTo: LaytimeLaycanToFilters
  laytimeAllowed: LaytimeLaytimeAllowedFilters
  laytimeStarted: LaytimeLaytimeStartedFilters
  laytimeUsed: LaytimeLaytimeUsedFilters
  norAccepted: LaytimeNorAcceptedFilters
  norTendered: LaytimeNorTenderedFilters
  operation: LaytimeOperationFilters
  operatorId: LaytimeOperatorIdFilters
  sofId: LaytimeSofIdFilters
  status: LaytimeStatusFilters
  updatedAt: LaytimeUpdatedAtFilters
}

input LaytimeIdFilters {
  OR: [LaytimeIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeInsertInput {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String
  id: String
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum
  """Date"""
  updatedAt: String
}

type LaytimeItem {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

input LaytimeLaycanFromFilters {
  OR: [LaytimeLaycanFromfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeLaycanFromfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeLaycanToFilters {
  OR: [LaytimeLaycanTofiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeLaycanTofiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeLaytimeAllowedFilters {
  OR: [LaytimeLaytimeAllowedfiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input LaytimeLaytimeAllowedfiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input LaytimeLaytimeStartedFilters {
  OR: [LaytimeLaytimeStartedfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeLaytimeStartedfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeLaytimeUsedFilters {
  OR: [LaytimeLaytimeUsedfiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input LaytimeLaytimeUsedfiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input LaytimeNorAcceptedFilters {
  OR: [LaytimeNorAcceptedfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeNorAcceptedfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeNorTenderedFilters {
  OR: [LaytimeNorTenderedfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeNorTenderedfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

enum LaytimeOperationEnum {
  """Value: discharging"""
  discharging
  """Value: lightering"""
  lightering
  """Value: loading"""
  loading
  """Value: transshipment"""
  transshipment
}

input LaytimeOperationFilters {
  OR: [LaytimeOperationfiltersOr!]
  eq: LaytimeOperationEnum
  gt: LaytimeOperationEnum
  gte: LaytimeOperationEnum
  ilike: String
  """Array<undefined>"""
  inArray: [LaytimeOperationEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: LaytimeOperationEnum
  lte: LaytimeOperationEnum
  ne: LaytimeOperationEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [LaytimeOperationEnum!]
  notLike: String
}

input LaytimeOperationfiltersOr {
  eq: LaytimeOperationEnum
  gt: LaytimeOperationEnum
  gte: LaytimeOperationEnum
  ilike: String
  """Array<undefined>"""
  inArray: [LaytimeOperationEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: LaytimeOperationEnum
  lte: LaytimeOperationEnum
  ne: LaytimeOperationEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [LaytimeOperationEnum!]
  notLike: String
}

input LaytimeOperatorIdFilters {
  OR: [LaytimeOperatorIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeOperatorIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type LaytimeOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [LaytimeOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [LaytimeOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type LaytimeOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): LaytimeOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): LaytimeOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type LaytimeOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type LaytimeOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type LaytimeOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

input LaytimeOrderBy {
  amount: InnerOrder
  createdAt: InnerOrder
  id: InnerOrder
  laycanFrom: InnerOrder
  laycanTo: InnerOrder
  laytimeAllowed: InnerOrder
  laytimeStarted: InnerOrder
  laytimeUsed: InnerOrder
  norAccepted: InnerOrder
  norTendered: InnerOrder
  operation: InnerOrder
  operatorId: InnerOrder
  sofId: InnerOrder
  status: InnerOrder
  updatedAt: InnerOrder
}

type LaytimeSelectItem {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [LaytimeCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): LaytimeOperatorRelation
  operatorId: String!
  sof(where: SofFilters): LaytimeSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [LaytimeUploadedFilesRelation!]!
}

input LaytimeSofIdFilters {
  OR: [LaytimeSofIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeSofIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type LaytimeSofRelation {
  cargo(where: CargoFilters): LaytimeSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): LaytimeSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): LaytimeSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [LaytimeSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [LaytimeSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): LaytimeSofRelationLaytimeRelation
  port(where: PortFilters): LaytimeSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): LaytimeSofRelationVesselRelation
  vesselId: String!
}

type LaytimeSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [LaytimeSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type LaytimeSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type LaytimeSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [LaytimeSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type LaytimeSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type LaytimeSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [LaytimeSofRelationChartererRelationSofsRelation!]!
}

type LaytimeSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type LaytimeSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): LaytimeSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type LaytimeSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type LaytimeSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): LaytimeSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type LaytimeSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type LaytimeSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type LaytimeSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [LaytimeSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [LaytimeSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [LaytimeSofRelationPortRelationSofsRelation!]!
}

type LaytimeSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): LaytimeSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type LaytimeSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type LaytimeSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): LaytimeSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type LaytimeSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type LaytimeSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type LaytimeSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [LaytimeSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type LaytimeSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

enum LaytimeStatusEnum {
  """Value: approved"""
  approved
  """Value: changes_requested"""
  changes_requested
  """Value: draft"""
  draft
  """Value: pending_approval"""
  pending_approval
  """Value: submitted"""
  submitted
}

input LaytimeStatusFilters {
  OR: [LaytimeStatusfiltersOr!]
  eq: LaytimeStatusEnum
  gt: LaytimeStatusEnum
  gte: LaytimeStatusEnum
  ilike: String
  """Array<undefined>"""
  inArray: [LaytimeStatusEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: LaytimeStatusEnum
  lte: LaytimeStatusEnum
  ne: LaytimeStatusEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [LaytimeStatusEnum!]
  notLike: String
}

input LaytimeStatusfiltersOr {
  eq: LaytimeStatusEnum
  gt: LaytimeStatusEnum
  gte: LaytimeStatusEnum
  ilike: String
  """Array<undefined>"""
  inArray: [LaytimeStatusEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: LaytimeStatusEnum
  lte: LaytimeStatusEnum
  ne: LaytimeStatusEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [LaytimeStatusEnum!]
  notLike: String
}

input LaytimeUpdateInput {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String
  id: String
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum
  operatorId: String
  sofId: String
  status: LaytimeStatusEnum
  """Date"""
  updatedAt: String
}

input LaytimeUpdatedAtFilters {
  OR: [LaytimeUpdatedAtfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input LaytimeUpdatedAtfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

type LaytimeUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): LaytimeUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): LaytimeUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type LaytimeUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type LaytimeUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [LaytimeUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [LaytimeUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type LaytimeUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): LaytimeUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): LaytimeUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type LaytimeUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type LaytimeUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type LaytimeUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type Mutation {
  deleteFromAgent(where: AgentFilters): [AgentItem!]!
  deleteFromAppointment(where: AppointmentFilters): [AppointmentItem!]!
  deleteFromCargo(where: CargoFilters): [CargoItem!]!
  deleteFromCharterPartyTerms(where: CharterPartyTermsFilters): [CharterPartyTermsItem!]!
  deleteFromCharterer(where: ChartererFilters): [ChartererItem!]!
  deleteFromComment(where: CommentFilters): [CommentItem!]!
  deleteFromDisbursement(where: DisbursementFilters): [DisbursementItem!]!
  deleteFromEvent(where: EventFilters): [EventItem!]!
  deleteFromExclusion(where: ExclusionFilters): [ExclusionItem!]!
  deleteFromHoliday(where: HolidayFilters): [HolidayItem!]!
  deleteFromLaytime(where: LaytimeFilters): [LaytimeItem!]!
  deleteFromOfficeHours(where: OfficeHoursFilters): [OfficeHoursItem!]!
  deleteFromOperator(where: OperatorFilters): [OperatorItem!]!
  deleteFromPort(where: PortFilters): [PortItem!]!
  deleteFromPortCall(where: PortCallFilters): [PortCallItem!]!
  deleteFromSof(where: SofFilters): [SofItem!]!
  deleteFromUploadedFile(where: UploadedFileFilters): [UploadedFileItem!]!
  deleteFromVessel(where: VesselFilters): [VesselItem!]!
  getSignedUrl(input: SignedUrlInput!): SignedUrlResult
  insertIntoAgent(values: [AgentInsertInput!]!): [AgentItem!]!
  insertIntoAgentSingle(values: AgentInsertInput!): AgentItem
  insertIntoAppointment(values: [AppointmentInsertInput!]!): [AppointmentItem!]!
  insertIntoAppointmentSingle(values: AppointmentInsertInput!): AppointmentItem
  insertIntoCargo(values: [CargoInsertInput!]!): [CargoItem!]!
  insertIntoCargoSingle(values: CargoInsertInput!): CargoItem
  insertIntoCharterPartyTerms(values: [CharterPartyTermsInsertInput!]!): [CharterPartyTermsItem!]!
  insertIntoCharterPartyTermsSingle(values: CharterPartyTermsInsertInput!): CharterPartyTermsItem
  insertIntoCharterer(values: [ChartererInsertInput!]!): [ChartererItem!]!
  insertIntoChartererSingle(values: ChartererInsertInput!): ChartererItem
  insertIntoComment(values: [CommentInsertInput!]!): [CommentItem!]!
  insertIntoCommentSingle(values: CommentInsertInput!): CommentItem
  insertIntoDisbursement(values: [DisbursementInsertInput!]!): [DisbursementItem!]!
  insertIntoDisbursementSingle(values: DisbursementInsertInput!): DisbursementItem
  insertIntoEvent(values: [EventInsertInput!]!): [EventItem!]!
  insertIntoEventSingle(values: EventInsertInput!): EventItem
  insertIntoExclusion(values: [ExclusionInsertInput!]!): [ExclusionItem!]!
  insertIntoExclusionSingle(values: ExclusionInsertInput!): ExclusionItem
  insertIntoHoliday(values: [HolidayInsertInput!]!): [HolidayItem!]!
  insertIntoHolidaySingle(values: HolidayInsertInput!): HolidayItem
  insertIntoLaytime(values: [LaytimeInsertInput!]!): [LaytimeItem!]!
  insertIntoLaytimeSingle(values: LaytimeInsertInput!): LaytimeItem
  insertIntoOfficeHours(values: [OfficeHoursInsertInput!]!): [OfficeHoursItem!]!
  insertIntoOfficeHoursSingle(values: OfficeHoursInsertInput!): OfficeHoursItem
  insertIntoOperator(values: [OperatorInsertInput!]!): [OperatorItem!]!
  insertIntoOperatorSingle(values: OperatorInsertInput!): OperatorItem
  insertIntoPort(values: [PortInsertInput!]!): [PortItem!]!
  insertIntoPortCall(values: [PortCallInsertInput!]!): [PortCallItem!]!
  insertIntoPortCallSingle(values: PortCallInsertInput!): PortCallItem
  insertIntoPortSingle(values: PortInsertInput!): PortItem
  insertIntoSof(values: [SofInsertInput!]!): [SofItem!]!
  insertIntoSofSingle(values: SofInsertInput!): SofItem
  insertIntoUploadedFile(values: [UploadedFileInsertInput!]!): [UploadedFileItem!]!
  insertIntoUploadedFileSingle(values: UploadedFileInsertInput!): UploadedFileItem
  insertIntoVessel(values: [VesselInsertInput!]!): [VesselItem!]!
  insertIntoVesselSingle(values: VesselInsertInput!): VesselItem
  updateAgent(set: AgentUpdateInput!, where: AgentFilters): [AgentItem!]!
  updateAppointment(set: AppointmentUpdateInput!, where: AppointmentFilters): [AppointmentItem!]!
  updateCargo(set: CargoUpdateInput!, where: CargoFilters): [CargoItem!]!
  updateCharterPartyTerms(set: CharterPartyTermsUpdateInput!, where: CharterPartyTermsFilters): [CharterPartyTermsItem!]!
  updateCharterer(set: ChartererUpdateInput!, where: ChartererFilters): [ChartererItem!]!
  updateComment(set: CommentUpdateInput!, where: CommentFilters): [CommentItem!]!
  updateDisbursement(set: DisbursementUpdateInput!, where: DisbursementFilters): [DisbursementItem!]!
  updateEvent(set: EventUpdateInput!, where: EventFilters): [EventItem!]!
  updateExclusion(set: ExclusionUpdateInput!, where: ExclusionFilters): [ExclusionItem!]!
  updateHoliday(set: HolidayUpdateInput!, where: HolidayFilters): [HolidayItem!]!
  updateLaytime(set: LaytimeUpdateInput!, where: LaytimeFilters): [LaytimeItem!]!
  updateOfficeHours(set: OfficeHoursUpdateInput!, where: OfficeHoursFilters): [OfficeHoursItem!]!
  updateOperator(set: OperatorUpdateInput!, where: OperatorFilters): [OperatorItem!]!
  updatePort(set: PortUpdateInput!, where: PortFilters): [PortItem!]!
  updatePortCall(set: PortCallUpdateInput!, where: PortCallFilters): [PortCallItem!]!
  updateSof(set: SofUpdateInput!, where: SofFilters): [SofItem!]!
  updateUploadedFile(set: UploadedFileUpdateInput!, where: UploadedFileFilters): [UploadedFileItem!]!
  updateVessel(set: VesselUpdateInput!, where: VesselFilters): [VesselItem!]!
}

input OfficeHoursEndTimeFilters {
  OR: [OfficeHoursEndTimefiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursEndTimefiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursFilters {
  OR: [OfficeHoursFiltersOr!]
  endTime: OfficeHoursEndTimeFilters
  id: OfficeHoursIdFilters
  portId: OfficeHoursPortIdFilters
  startTime: OfficeHoursStartTimeFilters
  weekday: OfficeHoursWeekdayFilters
}

input OfficeHoursFiltersOr {
  endTime: OfficeHoursEndTimeFilters
  id: OfficeHoursIdFilters
  portId: OfficeHoursPortIdFilters
  startTime: OfficeHoursStartTimeFilters
  weekday: OfficeHoursWeekdayFilters
}

input OfficeHoursIdFilters {
  OR: [OfficeHoursIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursInsertInput {
  """Date"""
  endTime: String!
  id: String
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type OfficeHoursItem {
  """Date"""
  endTime: String!
  id: String!
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

input OfficeHoursOrderBy {
  endTime: InnerOrder
  id: InnerOrder
  portId: InnerOrder
  startTime: InnerOrder
  weekday: InnerOrder
}

input OfficeHoursPortIdFilters {
  OR: [OfficeHoursPortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursPortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type OfficeHoursPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [OfficeHoursPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [OfficeHoursPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OfficeHoursPortRelationSofsRelation!]!
}

type OfficeHoursPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): OfficeHoursPortRelationHolidaysRelationPortRelation
  portId: String!
}

type OfficeHoursPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type OfficeHoursPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type OfficeHoursPortRelationSofsRelation {
  cargo(where: CargoFilters): OfficeHoursPortRelationSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): OfficeHoursPortRelationSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): OfficeHoursPortRelationSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [OfficeHoursPortRelationSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [OfficeHoursPortRelationSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): OfficeHoursPortRelationSofsRelationLaytimeRelation
  port(where: PortFilters): OfficeHoursPortRelationSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): OfficeHoursPortRelationSofsRelationVesselRelation
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OfficeHoursPortRelationSofsRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type OfficeHoursPortRelationSofsRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OfficeHoursPortRelationSofsRelationCharterPartyTermsRelationSofsRelation!]!
}

type OfficeHoursPortRelationSofsRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OfficeHoursPortRelationSofsRelationChartererRelationSofsRelation!]!
}

type OfficeHoursPortRelationSofsRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): OfficeHoursPortRelationSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type OfficeHoursPortRelationSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): OfficeHoursPortRelationSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type OfficeHoursPortRelationSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type OfficeHoursPortRelationSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OfficeHoursPortRelationSofsRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type OfficeHoursPortRelationSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OfficeHoursPortRelationSofsRelationVesselRelationSofsRelation!]!
  type: String!
}

type OfficeHoursPortRelationSofsRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OfficeHoursSelectItem {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): OfficeHoursPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

input OfficeHoursStartTimeFilters {
  OR: [OfficeHoursStartTimefiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursStartTimefiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursUpdateInput {
  """Date"""
  endTime: String
  id: String
  portId: String
  """Date"""
  startTime: String
  weekday: String
}

input OfficeHoursWeekdayFilters {
  OR: [OfficeHoursWeekdayfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OfficeHoursWeekdayfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorAvatarFilters {
  OR: [OperatorAvatarfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorAvatarfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type OperatorCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): OperatorCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): OperatorCommentsRelationUserRelation
  userId: String!
}

type OperatorCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OperatorCommentsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): OperatorCommentsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): OperatorCommentsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [OperatorCommentsRelationLaytimeRelationUploadedFilesRelation!]!
}

type OperatorCommentsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type OperatorCommentsRelationLaytimeRelationOperatorRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type OperatorCommentsRelationLaytimeRelationSofRelation {
  cargo(where: CargoFilters): OperatorCommentsRelationLaytimeRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): OperatorCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): OperatorCommentsRelationLaytimeRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [OperatorCommentsRelationLaytimeRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [OperatorCommentsRelationLaytimeRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): OperatorCommentsRelationLaytimeRelationSofRelationLaytimeRelation
  port(where: PortFilters): OperatorCommentsRelationLaytimeRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): OperatorCommentsRelationLaytimeRelationSofRelationVesselRelation
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorCommentsRelationLaytimeRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type OperatorCommentsRelationLaytimeRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type OperatorCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorCommentsRelationLaytimeRelationSofRelationChartererRelationSofsRelation!]!
}

type OperatorCommentsRelationLaytimeRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): OperatorCommentsRelationLaytimeRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): OperatorCommentsRelationLaytimeRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type OperatorCommentsRelationLaytimeRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [OperatorCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [OperatorCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorCommentsRelationLaytimeRelationSofRelationPortRelationSofsRelation!]!
}

type OperatorCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): OperatorCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): OperatorCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorCommentsRelationLaytimeRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type OperatorCommentsRelationLaytimeRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorCommentsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): OperatorCommentsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): OperatorCommentsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type OperatorCommentsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OperatorCommentsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type OperatorCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

input OperatorEmailFilters {
  OR: [OperatorEmailfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorEmailfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorFilters {
  OR: [OperatorFiltersOr!]
  avatar: OperatorAvatarFilters
  email: OperatorEmailFilters
  firstName: OperatorFirstNameFilters
  id: OperatorIdFilters
  lastName: OperatorLastNameFilters
  role: OperatorRoleFilters
}

input OperatorFiltersOr {
  avatar: OperatorAvatarFilters
  email: OperatorEmailFilters
  firstName: OperatorFirstNameFilters
  id: OperatorIdFilters
  lastName: OperatorLastNameFilters
  role: OperatorRoleFilters
}

input OperatorFirstNameFilters {
  OR: [OperatorFirstNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorFirstNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorIdFilters {
  OR: [OperatorIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorInsertInput {
  avatar: String
  email: String!
  firstName: String!
  id: String
  lastName: String!
  role: OperatorRoleEnum
}

type OperatorItem {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

input OperatorLastNameFilters {
  OR: [OperatorLastNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input OperatorLastNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type OperatorLaytimesRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OperatorLaytimesRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): OperatorLaytimesRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): OperatorLaytimesRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [OperatorLaytimesRelationUploadedFilesRelation!]!
}

type OperatorLaytimesRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): OperatorLaytimesRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): OperatorLaytimesRelationCommentsRelationUserRelation
  userId: String!
}

type OperatorLaytimesRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OperatorLaytimesRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type OperatorLaytimesRelationOperatorRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type OperatorLaytimesRelationSofRelation {
  cargo(where: CargoFilters): OperatorLaytimesRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): OperatorLaytimesRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): OperatorLaytimesRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [OperatorLaytimesRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [OperatorLaytimesRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): OperatorLaytimesRelationSofRelationLaytimeRelation
  port(where: PortFilters): OperatorLaytimesRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): OperatorLaytimesRelationSofRelationVesselRelation
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorLaytimesRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type OperatorLaytimesRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorLaytimesRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type OperatorLaytimesRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorLaytimesRelationSofRelationChartererRelationSofsRelation!]!
}

type OperatorLaytimesRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): OperatorLaytimesRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type OperatorLaytimesRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): OperatorLaytimesRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type OperatorLaytimesRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OperatorLaytimesRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [OperatorLaytimesRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [OperatorLaytimesRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorLaytimesRelationSofRelationPortRelationSofsRelation!]!
}

type OperatorLaytimesRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): OperatorLaytimesRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type OperatorLaytimesRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type OperatorLaytimesRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): OperatorLaytimesRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type OperatorLaytimesRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type OperatorLaytimesRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [OperatorLaytimesRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type OperatorLaytimesRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type OperatorLaytimesRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): OperatorLaytimesRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): OperatorLaytimesRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type OperatorLaytimesRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type OperatorLaytimesRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

input OperatorOrderBy {
  avatar: InnerOrder
  email: InnerOrder
  firstName: InnerOrder
  id: InnerOrder
  lastName: InnerOrder
  role: InnerOrder
}

enum OperatorRoleEnum {
  """Value: admin"""
  admin
  """Value: operator"""
  operator
  """Value: viewer"""
  viewer
}

input OperatorRoleFilters {
  OR: [OperatorRolefiltersOr!]
  eq: OperatorRoleEnum
  gt: OperatorRoleEnum
  gte: OperatorRoleEnum
  ilike: String
  """Array<undefined>"""
  inArray: [OperatorRoleEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: OperatorRoleEnum
  lte: OperatorRoleEnum
  ne: OperatorRoleEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [OperatorRoleEnum!]
  notLike: String
}

input OperatorRolefiltersOr {
  eq: OperatorRoleEnum
  gt: OperatorRoleEnum
  gte: OperatorRoleEnum
  ilike: String
  """Array<undefined>"""
  inArray: [OperatorRoleEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: OperatorRoleEnum
  lte: OperatorRoleEnum
  ne: OperatorRoleEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [OperatorRoleEnum!]
  notLike: String
}

type OperatorSelectItem {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [OperatorCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [OperatorLaytimesRelation!]!
  role: OperatorRoleEnum!
}

input OperatorUpdateInput {
  avatar: String
  email: String
  firstName: String
  id: String
  lastName: String
  role: OperatorRoleEnum
}

"""Order by direction"""
enum OrderDirection {
  """Ascending order"""
  asc
  """Descending order"""
  desc
}

input PortCallAgentIdFilters {
  OR: [PortCallAgentIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallAgentIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallAgentNameFilters {
  OR: [PortCallAgentNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallAgentNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallEtaFilters {
  OR: [PortCallEtafiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallEtafiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallEtdFilters {
  OR: [PortCallEtdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallEtdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallFileIdFilters {
  OR: [PortCallFileIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallFileIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallFilters {
  OR: [PortCallFiltersOr!]
  agentId: PortCallAgentIdFilters
  agentName: PortCallAgentNameFilters
  eta: PortCallEtaFilters
  etd: PortCallEtdFilters
  fileId: PortCallFileIdFilters
  id: PortCallIdFilters
  operatorId: PortCallOperatorIdFilters
  operatorName: PortCallOperatorNameFilters
  portCountryCode: PortCallPortCountryCodeFilters
  portFunction: PortCallPortFunctionFilters
  portId: PortCallPortIdFilters
  portName: PortCallPortNameFilters
  status: PortCallStatusFilters
  vesselId: PortCallVesselIdFilters
  vesselImo: PortCallVesselImoFilters
  vesselName: PortCallVesselNameFilters
}

input PortCallFiltersOr {
  agentId: PortCallAgentIdFilters
  agentName: PortCallAgentNameFilters
  eta: PortCallEtaFilters
  etd: PortCallEtdFilters
  fileId: PortCallFileIdFilters
  id: PortCallIdFilters
  operatorId: PortCallOperatorIdFilters
  operatorName: PortCallOperatorNameFilters
  portCountryCode: PortCallPortCountryCodeFilters
  portFunction: PortCallPortFunctionFilters
  portId: PortCallPortIdFilters
  portName: PortCallPortNameFilters
  status: PortCallStatusFilters
  vesselId: PortCallVesselIdFilters
  vesselImo: PortCallVesselImoFilters
  vesselName: PortCallVesselNameFilters
}

input PortCallIdFilters {
  OR: [PortCallIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallInsertInput {
  agentId: String!
  agentName: String!
  eta: String!
  etd: String!
  fileId: String!
  id: String
  operatorId: String!
  operatorName: String!
  portCountryCode: String!
  portFunction: PortCallPortFunctionEnum!
  portId: String!
  portName: String!
  status: String!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

type PortCallItem {
  agentId: String!
  agentName: String!
  eta: String!
  etd: String!
  fileId: String!
  id: String!
  operatorId: String!
  operatorName: String!
  portCountryCode: String!
  portFunction: PortCallPortFunctionEnum!
  portId: String!
  portName: String!
  status: String!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

input PortCallOperatorIdFilters {
  OR: [PortCallOperatorIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallOperatorIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallOperatorNameFilters {
  OR: [PortCallOperatorNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallOperatorNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallOrderBy {
  agentId: InnerOrder
  agentName: InnerOrder
  eta: InnerOrder
  etd: InnerOrder
  fileId: InnerOrder
  id: InnerOrder
  operatorId: InnerOrder
  operatorName: InnerOrder
  portCountryCode: InnerOrder
  portFunction: InnerOrder
  portId: InnerOrder
  portName: InnerOrder
  status: InnerOrder
  vesselId: InnerOrder
  vesselImo: InnerOrder
  vesselName: InnerOrder
}

input PortCallPortCountryCodeFilters {
  OR: [PortCallPortCountryCodefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallPortCountryCodefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

enum PortCallPortFunctionEnum {
  """Value: ace"""
  ace
  """Value: aes"""
  aes
  """Value: bunkering"""
  bunkering
  """Value: canal_transit"""
  canal_transit
  """Value: cargo_operations"""
  cargo_operations
  """Value: cleaning"""
  cleaning
  """Value: clearance"""
  clearance
  """Value: crew_change"""
  crew_change
  """Value: cruise_call"""
  cruise_call
  """Value: delivery_redelivery"""
  delivery_redelivery
  """Value: documentation"""
  documentation
  """Value: dry_docking"""
  dry_docking
  """Value: gassing_up"""
  gassing_up
  """Value: husbandry"""
  husbandry
  """Value: lay_up"""
  lay_up
  """Value: other"""
  other
  """Value: purging"""
  purging
  """Value: repairs"""
  repairs
  """Value: shelter_layby"""
  shelter_layby
  """Value: towage_on_floating_lines"""
  towage_on_floating_lines
  """Value: waiting_layby"""
  waiting_layby
}

input PortCallPortFunctionFilters {
  OR: [PortCallPortFunctionfiltersOr!]
  eq: PortCallPortFunctionEnum
  gt: PortCallPortFunctionEnum
  gte: PortCallPortFunctionEnum
  ilike: String
  """Array<undefined>"""
  inArray: [PortCallPortFunctionEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: PortCallPortFunctionEnum
  lte: PortCallPortFunctionEnum
  ne: PortCallPortFunctionEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [PortCallPortFunctionEnum!]
  notLike: String
}

input PortCallPortFunctionfiltersOr {
  eq: PortCallPortFunctionEnum
  gt: PortCallPortFunctionEnum
  gte: PortCallPortFunctionEnum
  ilike: String
  """Array<undefined>"""
  inArray: [PortCallPortFunctionEnum!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: PortCallPortFunctionEnum
  lte: PortCallPortFunctionEnum
  ne: PortCallPortFunctionEnum
  notIlike: String
  """Array<undefined>"""
  notInArray: [PortCallPortFunctionEnum!]
  notLike: String
}

input PortCallPortIdFilters {
  OR: [PortCallPortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallPortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallPortNameFilters {
  OR: [PortCallPortNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallPortNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type PortCallSelectItem {
  agentId: String!
  agentName: String!
  eta: String!
  etd: String!
  fileId: String!
  id: String!
  operatorId: String!
  operatorName: String!
  portCountryCode: String!
  portFunction: PortCallPortFunctionEnum!
  portId: String!
  portName: String!
  status: String!
  vesselId: String!
  vesselImo: String!
  vesselName: String!
}

input PortCallStatusFilters {
  OR: [PortCallStatusfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallStatusfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallUpdateInput {
  agentId: String
  agentName: String
  eta: String
  etd: String
  fileId: String
  id: String
  operatorId: String
  operatorName: String
  portCountryCode: String
  portFunction: PortCallPortFunctionEnum
  portId: String
  portName: String
  status: String
  vesselId: String
  vesselImo: String
  vesselName: String
}

input PortCallVesselIdFilters {
  OR: [PortCallVesselIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallVesselIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallVesselImoFilters {
  OR: [PortCallVesselImofiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallVesselImofiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallVesselNameFilters {
  OR: [PortCallVesselNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCallVesselNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCountryCodeFilters {
  OR: [PortCountryCodefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortCountryCodefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortFilters {
  OR: [PortFiltersOr!]
  countryCode: PortCountryCodeFilters
  id: PortIdFilters
  name: PortNameFilters
}

input PortFiltersOr {
  countryCode: PortCountryCodeFilters
  id: PortIdFilters
  name: PortNameFilters
}

type PortHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): PortHolidaysRelationPortRelation
  portId: String!
}

type PortHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

input PortIdFilters {
  OR: [PortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortInsertInput {
  countryCode: String!
  id: String
  name: String!
}

type PortItem {
  countryCode: String!
  id: String!
  name: String!
}

input PortNameFilters {
  OR: [PortNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input PortNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type PortOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): PortOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type PortOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

input PortOrderBy {
  countryCode: InnerOrder
  id: InnerOrder
  name: InnerOrder
}

type PortSelectItem {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [PortHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [PortOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [PortSofsRelation!]!
}

type PortSofsRelation {
  cargo(where: CargoFilters): PortSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): PortSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): PortSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [PortSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [PortSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): PortSofsRelationLaytimeRelation
  port(where: PortFilters): PortSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): PortSofsRelationVesselRelation
  vesselId: String!
}

type PortSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [PortSofsRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type PortSofsRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type PortSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [PortSofsRelationCharterPartyTermsRelationSofsRelation!]!
}

type PortSofsRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type PortSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [PortSofsRelationChartererRelationSofsRelation!]!
}

type PortSofsRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type PortSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): PortSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type PortSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type PortSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): PortSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type PortSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type PortSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [PortSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): PortSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): PortSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [PortSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type PortSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): PortSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): PortSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type PortSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [PortSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [PortSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type PortSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type PortSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [PortSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [PortSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type PortSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): PortSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): PortSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type PortSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type PortSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): PortSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type PortSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type PortSofsRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type PortSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [PortSofsRelationVesselRelationSofsRelation!]!
  type: String!
}

type PortSofsRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input PortUpdateInput {
  countryCode: String
  id: String
  name: String
}

type Query {
  agent(limit: Int, offset: Int, orderBy: AgentOrderBy, where: AgentFilters): [AgentSelectItem!]!
  agentSingle(offset: Int, orderBy: AgentOrderBy, where: AgentFilters): AgentSelectItem
  appointment(limit: Int, offset: Int, orderBy: AppointmentOrderBy, where: AppointmentFilters): [AppointmentSelectItem!]!
  appointmentSingle(offset: Int, orderBy: AppointmentOrderBy, where: AppointmentFilters): AppointmentSelectItem
  cargo(limit: Int, offset: Int, orderBy: CargoOrderBy, where: CargoFilters): [CargoSelectItem!]!
  cargoSingle(offset: Int, orderBy: CargoOrderBy, where: CargoFilters): CargoSelectItem
  charterPartyTerms(limit: Int, offset: Int, orderBy: CharterPartyTermsOrderBy, where: CharterPartyTermsFilters): [CharterPartyTermsSelectItem!]!
  charterPartyTermsSingle(offset: Int, orderBy: CharterPartyTermsOrderBy, where: CharterPartyTermsFilters): CharterPartyTermsSelectItem
  charterer(limit: Int, offset: Int, orderBy: ChartererOrderBy, where: ChartererFilters): [ChartererSelectItem!]!
  chartererSingle(offset: Int, orderBy: ChartererOrderBy, where: ChartererFilters): ChartererSelectItem
  comment(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [CommentSelectItem!]!
  commentSingle(offset: Int, orderBy: CommentOrderBy, where: CommentFilters): CommentSelectItem
  disbursement(limit: Int, offset: Int, orderBy: DisbursementOrderBy, where: DisbursementFilters): [DisbursementSelectItem!]!
  disbursementSingle(offset: Int, orderBy: DisbursementOrderBy, where: DisbursementFilters): DisbursementSelectItem
  event(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [EventSelectItem!]!
  eventSingle(offset: Int, orderBy: EventOrderBy, where: EventFilters): EventSelectItem
  exclusion(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [ExclusionSelectItem!]!
  exclusionSingle(offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): ExclusionSelectItem
  holiday(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [HolidaySelectItem!]!
  holidaySingle(offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): HolidaySelectItem
  laytime(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [LaytimeSelectItem!]!
  laytimeSingle(offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): LaytimeSelectItem
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [OfficeHoursSelectItem!]!
  officeHoursSingle(offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): OfficeHoursSelectItem
  operator(limit: Int, offset: Int, orderBy: OperatorOrderBy, where: OperatorFilters): [OperatorSelectItem!]!
  operatorSingle(offset: Int, orderBy: OperatorOrderBy, where: OperatorFilters): OperatorSelectItem
  port(limit: Int, offset: Int, orderBy: PortOrderBy, where: PortFilters): [PortSelectItem!]!
  portCall(limit: Int, offset: Int, orderBy: PortCallOrderBy, where: PortCallFilters): [PortCallSelectItem!]!
  portCallSingle(offset: Int, orderBy: PortCallOrderBy, where: PortCallFilters): PortCallSelectItem
  portSingle(offset: Int, orderBy: PortOrderBy, where: PortFilters): PortSelectItem
  sof(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [SofSelectItem!]!
  sofSingle(offset: Int, orderBy: SofOrderBy, where: SofFilters): SofSelectItem
  uploadedFile(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [UploadedFileSelectItem!]!
  uploadedFileSingle(offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): UploadedFileSelectItem
  vessel(limit: Int, offset: Int, orderBy: VesselOrderBy, where: VesselFilters): [VesselSelectItem!]!
  vesselSingle(offset: Int, orderBy: VesselOrderBy, where: VesselFilters): VesselSelectItem
}

input SignedUrlInput {
  contentType: String!
  fileName: String!
}

type SignedUrlResult {
  bucketName: String!
  fileName: String!
  signedUrl: String!
}

input SofCargoIdFilters {
  OR: [SofCargoIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofCargoIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type SofCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [SofCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type SofCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input SofCharterPartyTermsIdFilters {
  OR: [SofCharterPartyTermsIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofCharterPartyTermsIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type SofCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [SofCharterPartyTermsRelationSofsRelation!]!
}

type SofCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input SofChartererIdFilters {
  OR: [SofChartererIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofChartererIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type SofChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [SofChartererRelationSofsRelation!]!
}

type SofChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type SofEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): SofEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type SofEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type SofExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): SofExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type SofExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input SofFilters {
  OR: [SofFiltersOr!]
  cargoId: SofCargoIdFilters
  charterPartyTermsId: SofCharterPartyTermsIdFilters
  chartererId: SofChartererIdFilters
  id: SofIdFilters
  portId: SofPortIdFilters
  vesselId: SofVesselIdFilters
}

input SofFiltersOr {
  cargoId: SofCargoIdFilters
  charterPartyTermsId: SofCharterPartyTermsIdFilters
  chartererId: SofChartererIdFilters
  id: SofIdFilters
  portId: SofPortIdFilters
  vesselId: SofVesselIdFilters
}

input SofIdFilters {
  OR: [SofIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofInsertInput {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String
  portId: String!
  vesselId: String!
}

type SofItem {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type SofLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [SofLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): SofLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): SofLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [SofLaytimeRelationUploadedFilesRelation!]!
}

type SofLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): SofLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): SofLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type SofLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type SofLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [SofLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [SofLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type SofLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type SofLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type SofLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [SofLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [SofLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type SofLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): SofLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): SofLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type SofLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type SofLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type SofLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type SofLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type SofLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): SofLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): SofLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type SofLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type SofLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [SofLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [SofLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type SofLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): SofLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): SofLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type SofLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type SofLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type SofLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

input SofOrderBy {
  cargoId: InnerOrder
  charterPartyTermsId: InnerOrder
  chartererId: InnerOrder
  id: InnerOrder
  portId: InnerOrder
  vesselId: InnerOrder
}

input SofPortIdFilters {
  OR: [SofPortIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofPortIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type SofPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [SofPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [SofPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [SofPortRelationSofsRelation!]!
}

type SofPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): SofPortRelationHolidaysRelationPortRelation
  portId: String!
}

type SofPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type SofPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): SofPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type SofPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type SofPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type SofSelectItem {
  cargo(where: CargoFilters): SofCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): SofCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): SofChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [SofEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [SofExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): SofLaytimeRelation
  port(where: PortFilters): SofPortRelation
  portId: String!
  vessel(where: VesselFilters): SofVesselRelation
  vesselId: String!
}

input SofUpdateInput {
  cargoId: String
  charterPartyTermsId: String
  chartererId: String
  id: String
  portId: String
  vesselId: String
}

input SofVesselIdFilters {
  OR: [SofVesselIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input SofVesselIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type SofVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [SofVesselRelationSofsRelation!]!
  type: String!
}

type SofVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

input UploadedFileCreatedAtFilters {
  OR: [UploadedFileCreatedAtfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileCreatedAtfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFileNameFilters {
  OR: [UploadedFileFileNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFileNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFileSizeFilters {
  OR: [UploadedFileFileSizefiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input UploadedFileFileSizefiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input UploadedFileFileTypeFilters {
  OR: [UploadedFileFileTypefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFileTypefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFileUrlFilters {
  OR: [UploadedFileFileUrlfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFileUrlfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileFilters {
  OR: [UploadedFileFiltersOr!]
  createdAt: UploadedFileCreatedAtFilters
  fileName: UploadedFileFileNameFilters
  fileSize: UploadedFileFileSizeFilters
  fileType: UploadedFileFileTypeFilters
  fileUrl: UploadedFileFileUrlFilters
  id: UploadedFileIdFilters
  laytimeId: UploadedFileLaytimeIdFilters
  updatedAt: UploadedFileUpdatedAtFilters
  uploadedById: UploadedFileUploadedByIdFilters
}

input UploadedFileFiltersOr {
  createdAt: UploadedFileCreatedAtFilters
  fileName: UploadedFileFileNameFilters
  fileSize: UploadedFileFileSizeFilters
  fileType: UploadedFileFileTypeFilters
  fileUrl: UploadedFileFileUrlFilters
  id: UploadedFileIdFilters
  laytimeId: UploadedFileLaytimeIdFilters
  updatedAt: UploadedFileUpdatedAtFilters
  uploadedById: UploadedFileUploadedByIdFilters
}

input UploadedFileIdFilters {
  OR: [UploadedFileIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileInsertInput {
  """Date"""
  createdAt: String
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String
  laytimeId: String!
  """Date"""
  updatedAt: String
  uploadedById: String!
}

type UploadedFileItem {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedById: String!
}

input UploadedFileLaytimeIdFilters {
  OR: [UploadedFileLaytimeIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileLaytimeIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type UploadedFileLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [UploadedFileLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): UploadedFileLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): UploadedFileLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [UploadedFileLaytimeRelationUploadedFilesRelation!]!
}

type UploadedFileLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): UploadedFileLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type UploadedFileLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [UploadedFileLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [UploadedFileLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type UploadedFileLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type UploadedFileLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [UploadedFileLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [UploadedFileLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type UploadedFileLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): UploadedFileLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type UploadedFileLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type UploadedFileLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileLaytimeRelationSofRelation {
  cargo(where: CargoFilters): UploadedFileLaytimeRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): UploadedFileLaytimeRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): UploadedFileLaytimeRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [UploadedFileLaytimeRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [UploadedFileLaytimeRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileLaytimeRelationSofRelationLaytimeRelation
  port(where: PortFilters): UploadedFileLaytimeRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): UploadedFileLaytimeRelationSofRelationVesselRelation
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileLaytimeRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type UploadedFileLaytimeRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type UploadedFileLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileLaytimeRelationSofRelationChartererRelationSofsRelation!]!
}

type UploadedFileLaytimeRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): UploadedFileLaytimeRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type UploadedFileLaytimeRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): UploadedFileLaytimeRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type UploadedFileLaytimeRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileLaytimeRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [UploadedFileLaytimeRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [UploadedFileLaytimeRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileLaytimeRelationSofRelationPortRelationSofsRelation!]!
}

type UploadedFileLaytimeRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): UploadedFileLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type UploadedFileLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type UploadedFileLaytimeRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): UploadedFileLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type UploadedFileLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type UploadedFileLaytimeRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileLaytimeRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type UploadedFileLaytimeRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedById: String!
}

input UploadedFileOrderBy {
  createdAt: InnerOrder
  fileName: InnerOrder
  fileSize: InnerOrder
  fileType: InnerOrder
  fileUrl: InnerOrder
  id: InnerOrder
  laytimeId: InnerOrder
  updatedAt: InnerOrder
  uploadedById: InnerOrder
}

type UploadedFileSelectItem {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): UploadedFileUploadedByRelation
  uploadedById: String!
}

input UploadedFileUpdateInput {
  """Date"""
  createdAt: String
  fileName: String
  fileSize: Float
  fileType: String
  fileUrl: String
  id: String
  laytimeId: String
  """Date"""
  updatedAt: String
  uploadedById: String
}

input UploadedFileUpdatedAtFilters {
  OR: [UploadedFileUpdatedAtfiltersOr!]
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileUpdatedAtfiltersOr {
  """Date"""
  eq: String
  """Date"""
  gt: String
  """Date"""
  gte: String
  ilike: String
  """Array<Date>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  """Date"""
  lt: String
  """Date"""
  lte: String
  """Date"""
  ne: String
  notIlike: String
  """Array<Date>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileUploadedByIdFilters {
  OR: [UploadedFileUploadedByIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input UploadedFileUploadedByIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

type UploadedFileUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [UploadedFileUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [UploadedFileUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type UploadedFileUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): UploadedFileUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationUploadedFilesRelation!]!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationOperatorRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelation {
  cargo(where: CargoFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationLaytimeRelation
  port(where: PortFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationVesselRelation
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationChartererRelationSofsRelation!]!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationSofsRelation!]!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationCommentsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedById: String!
}

type UploadedFileUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type UploadedFileUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [UploadedFileUploadedByRelationLaytimesRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): UploadedFileUploadedByRelationLaytimesRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [UploadedFileUploadedByRelationLaytimesRelationUploadedFilesRelation!]!
}

type UploadedFileUploadedByRelationLaytimesRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileUploadedByRelationLaytimesRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): UploadedFileUploadedByRelationLaytimesRelationCommentsRelationUserRelation
  userId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileUploadedByRelationLaytimesRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type UploadedFileUploadedByRelationLaytimesRelationOperatorRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelation {
  cargo(where: CargoFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationLaytimeRelation
  port(where: PortFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationVesselRelation
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationCharterPartyTermsRelationSofsRelation!]!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationChartererRelationSofsRelation!]!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationSofsRelation!]!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [UploadedFileUploadedByRelationLaytimesRelationSofRelationVesselRelationSofsRelation!]!
  type: String!
}

type UploadedFileUploadedByRelationLaytimesRelationSofRelationVesselRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type UploadedFileUploadedByRelationLaytimesRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedById: String!
}

input VesselDeadweightTonnageFilters {
  OR: [VesselDeadweightTonnagefiltersOr!]
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input VesselDeadweightTonnagefiltersOr {
  eq: Float
  gt: Float
  gte: Float
  ilike: String
  """Array<undefined>"""
  inArray: [Float!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: Float
  lte: Float
  ne: Float
  notIlike: String
  """Array<undefined>"""
  notInArray: [Float!]
  notLike: String
}

input VesselFilters {
  OR: [VesselFiltersOr!]
  deadweightTonnage: VesselDeadweightTonnageFilters
  flag: VesselFlagFilters
  id: VesselIdFilters
  imo: VesselImoFilters
  name: VesselNameFilters
  type: VesselTypeFilters
}

input VesselFiltersOr {
  deadweightTonnage: VesselDeadweightTonnageFilters
  flag: VesselFlagFilters
  id: VesselIdFilters
  imo: VesselImoFilters
  name: VesselNameFilters
  type: VesselTypeFilters
}

input VesselFlagFilters {
  OR: [VesselFlagfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselFlagfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselIdFilters {
  OR: [VesselIdfiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselIdfiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselImoFilters {
  OR: [VesselImofiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselImofiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselInsertInput {
  deadweightTonnage: Float!
  flag: String!
  id: String
  imo: String!
  name: String!
  type: String!
}

type VesselItem {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  type: String!
}

input VesselNameFilters {
  OR: [VesselNamefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselNamefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselOrderBy {
  deadweightTonnage: InnerOrder
  flag: InnerOrder
  id: InnerOrder
  imo: InnerOrder
  name: InnerOrder
  type: InnerOrder
}

type VesselSelectItem {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [VesselSofsRelation!]!
  type: String!
}

type VesselSofsRelation {
  cargo(where: CargoFilters): VesselSofsRelationCargoRelation
  cargoId: String!
  charterPartyTerms(where: CharterPartyTermsFilters): VesselSofsRelationCharterPartyTermsRelation
  charterPartyTermsId: String!
  charterer(where: ChartererFilters): VesselSofsRelationChartererRelation
  chartererId: String!
  events(limit: Int, offset: Int, orderBy: EventOrderBy, where: EventFilters): [VesselSofsRelationEventsRelation!]!
  exclusions(limit: Int, offset: Int, orderBy: ExclusionOrderBy, where: ExclusionFilters): [VesselSofsRelationExclusionsRelation!]!
  id: String!
  laytime(where: LaytimeFilters): VesselSofsRelationLaytimeRelation
  port(where: PortFilters): VesselSofsRelationPortRelation
  portId: String!
  vessel(where: VesselFilters): VesselSofsRelationVesselRelation
  vesselId: String!
}

type VesselSofsRelationCargoRelation {
  id: String!
  quantity: Float!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [VesselSofsRelationCargoRelationSofsRelation!]!
  type: String!
  unit: String
}

type VesselSofsRelationCargoRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationCharterPartyTermsRelation {
  demurrageRate: Float
  dischargingRate: Float
  id: String!
  laytimeAllowed: Float!
  laytimeCalculationModifiers: String
  loadingRate: Float
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [VesselSofsRelationCharterPartyTermsRelationSofsRelation!]!
}

type VesselSofsRelationCharterPartyTermsRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationChartererRelation {
  email: String
  id: String!
  name: String!
  phone: String
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [VesselSofsRelationChartererRelationSofsRelation!]!
}

type VesselSofsRelationChartererRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationEventsRelation {
  description: String
  id: String!
  sof(where: SofFilters): VesselSofsRelationEventsRelationSofRelation
  sofId: String!
  """Date"""
  timestamp: String!
  type: String!
}

type VesselSofsRelationEventsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationExclusionsRelation {
  dateFrom: String!
  dateTo: String!
  duration: Int!
  event: String!
  id: String!
  percentage: String!
  sof(where: SofFilters): VesselSofsRelationExclusionsRelationSofRelation
  sofId: String!
  timeUsed: Int!
}

type VesselSofsRelationExclusionsRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationLaytimeRelation {
  """JSON"""
  amount: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [VesselSofsRelationLaytimeRelationCommentsRelation!]!
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operator(where: OperatorFilters): VesselSofsRelationLaytimeRelationOperatorRelation
  operatorId: String!
  sof(where: SofFilters): VesselSofsRelationLaytimeRelationSofRelation
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
  uploadedFiles(limit: Int, offset: Int, orderBy: UploadedFileOrderBy, where: UploadedFileFilters): [VesselSofsRelationLaytimeRelationUploadedFilesRelation!]!
}

type VesselSofsRelationLaytimeRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): VesselSofsRelationLaytimeRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): VesselSofsRelationLaytimeRelationCommentsRelationUserRelation
  userId: String!
}

type VesselSofsRelationLaytimeRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationLaytimeRelationCommentsRelationUserRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [VesselSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [VesselSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type VesselSofsRelationLaytimeRelationCommentsRelationUserRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  userId: String!
}

type VesselSofsRelationLaytimeRelationCommentsRelationUserRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationLaytimeRelationOperatorRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [VesselSofsRelationLaytimeRelationOperatorRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [VesselSofsRelationLaytimeRelationOperatorRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type VesselSofsRelationLaytimeRelationOperatorRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): VesselSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): VesselSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation
  userId: String!
}

type VesselSofsRelationLaytimeRelationOperatorRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationLaytimeRelationOperatorRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type VesselSofsRelationLaytimeRelationOperatorRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationLaytimeRelationSofRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelation {
  """Date"""
  createdAt: String!
  fileName: String!
  fileSize: Float!
  fileType: String!
  fileUrl: String!
  id: String!
  laytime(where: LaytimeFilters): VesselSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation
  laytimeId: String!
  """Date"""
  updatedAt: String!
  uploadedBy(where: OperatorFilters): VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation
  uploadedById: String!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelation {
  avatar: String
  comments(limit: Int, offset: Int, orderBy: CommentOrderBy, where: CommentFilters): [VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation!]!
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  laytimes(limit: Int, offset: Int, orderBy: LaytimeOrderBy, where: LaytimeFilters): [VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation!]!
  role: OperatorRoleEnum!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelation {
  """Date"""
  createdAt: String!
  id: String!
  laytime(where: LaytimeFilters): VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation
  laytimeId: String!
  message: String!
  status: CommentStatusEnum!
  user(where: OperatorFilters): VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation
  userId: String!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationLaytimeRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationCommentsRelationUserRelation {
  avatar: String
  email: String!
  firstName: String!
  id: String!
  lastName: String!
  role: OperatorRoleEnum!
}

type VesselSofsRelationLaytimeRelationUploadedFilesRelationUploadedByRelationLaytimesRelation {
  """JSON"""
  amount: String
  """Date"""
  createdAt: String!
  id: String!
  """Date"""
  laycanFrom: String
  """Date"""
  laycanTo: String
  laytimeAllowed: Float!
  """Date"""
  laytimeStarted: String
  laytimeUsed: Float
  """Date"""
  norAccepted: String
  """Date"""
  norTendered: String
  operation: LaytimeOperationEnum!
  operatorId: String!
  sofId: String!
  status: LaytimeStatusEnum!
  """Date"""
  updatedAt: String!
}

type VesselSofsRelationPortRelation {
  countryCode: String!
  holidays(limit: Int, offset: Int, orderBy: HolidayOrderBy, where: HolidayFilters): [VesselSofsRelationPortRelationHolidaysRelation!]!
  id: String!
  name: String!
  officeHours(limit: Int, offset: Int, orderBy: OfficeHoursOrderBy, where: OfficeHoursFilters): [VesselSofsRelationPortRelationOfficeHoursRelation!]!
  sofs(limit: Int, offset: Int, orderBy: SofOrderBy, where: SofFilters): [VesselSofsRelationPortRelationSofsRelation!]!
}

type VesselSofsRelationPortRelationHolidaysRelation {
  """Date"""
  holidayDate: String!
  id: String!
  port(where: PortFilters): VesselSofsRelationPortRelationHolidaysRelationPortRelation
  portId: String!
}

type VesselSofsRelationPortRelationHolidaysRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type VesselSofsRelationPortRelationOfficeHoursRelation {
  """Date"""
  endTime: String!
  id: String!
  port(where: PortFilters): VesselSofsRelationPortRelationOfficeHoursRelationPortRelation
  portId: String!
  """Date"""
  startTime: String!
  weekday: String!
}

type VesselSofsRelationPortRelationOfficeHoursRelationPortRelation {
  countryCode: String!
  id: String!
  name: String!
}

type VesselSofsRelationPortRelationSofsRelation {
  cargoId: String!
  charterPartyTermsId: String!
  chartererId: String!
  id: String!
  portId: String!
  vesselId: String!
}

type VesselSofsRelationVesselRelation {
  deadweightTonnage: Float!
  flag: String!
  id: String!
  imo: String!
  name: String!
  type: String!
}

input VesselTypeFilters {
  OR: [VesselTypefiltersOr!]
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselTypefiltersOr {
  eq: String
  gt: String
  gte: String
  ilike: String
  """Array<undefined>"""
  inArray: [String!]
  isNotNull: Boolean
  isNull: Boolean
  like: String
  lt: String
  lte: String
  ne: String
  notIlike: String
  """Array<undefined>"""
  notInArray: [String!]
  notLike: String
}

input VesselUpdateInput {
  deadweightTonnage: Float
  flag: String
  id: String
  imo: String
  name: String
  type: String
}