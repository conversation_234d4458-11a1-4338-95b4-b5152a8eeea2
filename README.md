## Setup

### Authenticate to the internal package registry

#### Generate a GitLab user token

- [Sign-in to Gitlab](https://gitlab.abraxa.com/users/sign_in)
- [Go to Profile -> User settings -> Access Tokens](https://gitlab.abraxa.com/-/profile/personal_access_tokens)
- Generate an Access Token with enabled `api` scope
- Execute from within the repository - `npm config set //gitlab.abraxa.com:_authToken=<access-token>`

### (Optional) Install nvm

https://github.com/nvm-sh/nvm

## Getting Started

```bash
nvm use # use local version for Node/npm

npm install # install deps

mv .env.sample .env # configure secrets

npm run start # start development server on :3000

npm run lint # lint

npm run typecheck # type check

npm run build # create production build
```

## Updating the GraphQL schema

In order to update the GraphQL schema, first make sure that you have correct NEXT_PUBLIC_GRAPHQL_ADDRESS env variable set. Then, run the following command:

```bash
npm run codegen:graphql
```
