module.exports = {
	extends: [require.resolve('@abraxa/tools-eslint')],
	ignorePatterns: ['.eslintrc.js', 'dist', 'build', 'generated', 'postcss.config.mjs'],
	parserOptions: {
		tsconfigRootDir: __dirname,
		project: './tsconfig.json'
	},
	plugins: ['unused-imports'],
	rules: {
		'import/no-unresolved': 'off', // TODO: figure out why eslint can't figure out path aliases
		'react/function-component-definition': 'off',
		'react/jsx-no-bind': 'off',
		'react/no-unknown-property': 'off',
		'jsx-a11y/no-autofocus': 'off',
		'unused-imports/no-unused-imports': 'warn',
		'@typescript-eslint/no-misused-promises': 'off',
		'unused-imports/no-unused-vars': [
			'warn',
			{
				vars: 'all',
				varsIgnorePattern: '^_',
				args: 'after-used',
				argsIgnorePattern: '^_'
			}
		]
	}
};
