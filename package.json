{"name": "@abraxa/prototype", "version": "0.1.0", "private": true, "scripts": {"start": "cross-env NODE_ENV=development webpack serve --config ./webpack/index.ts", "build:dev": "cross-env NODE_ENV=development webpack --config ./webpack/index.ts", "build:prod": "cross-env NODE_ENV=production webpack --config ./webpack/index.ts", "build": "npm run build:prod", "lint": "eslint --ext .ts,.tsx . --cache", "typecheck": "tsc --noEmit", "codegen:graphql": "cross-env graphql-codegen --require dotenv/config"}, "dependencies": {"@abraxa/authentication": "^0.2.2", "@abraxa/graphql": "^0.2.1", "@apollo/client": "^3.12.11", "@emotion/is-prop-valid": "^1.3.1", "@hookform/resolvers": "^4.1.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.1.2", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.1.7", "@tanstack/react-query": "^5.72.0", "@tanstack/react-table": "^8.20.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.0.0", "framer-motion": "^12.5.0", "graphql": "^16.10.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.474.0", "mapbox-gl": "^3.10.0", "next-themes": "^0.4.6", "react": "^18", "react-circle-flags": "^0.0.23", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-map-gl": "^8.0.1", "react-markdown": "^10.0.0", "react-resizable-panels": "^2.1.7", "react-router": "^7.3.0", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "sonner": "^2.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@abraxa/tools-codegen": "^0.1.0", "@abraxa/tools-eslint": "^0.1.8", "@abraxa/tools-prettier": "^0.1.0", "@abraxa/tools-typescript": "^0.2.0", "@abraxa/tools-webpack": "^0.8.1", "@faker-js/faker": "^9.5.1", "@tailwindcss/postcss": "^4.0.0", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "dotenv": "^16.4.7", "esbuild-loader": "^4.3.0", "esbuild-register": "^3.6.0", "eslint-plugin-unused-imports": "^4.1.4", "html-webpack-plugin": "^5.6.3", "postcss": "^8", "postcss-loader": "^8.1.1", "prettier-plugin-tailwindcss": "^0.6.11", "style-loader": "^4.0.0", "tailwindcss": "^4.0.0"}, "optionalDependencies": {"@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "engines": {"node": "22.x", "npm": "10.x"}}